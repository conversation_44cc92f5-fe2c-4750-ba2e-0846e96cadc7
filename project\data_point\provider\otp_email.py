import logging

from data_point.base import DynamicformBase
from django.db.models import Sum

logger: logging.Logger = logging.getLogger(__name__)

DATAPOINT_OPTIONS = {
    'fail_email_otp_cnt': {
        'label': 'Number of times user failed to verify Email OTP',
        'type': 'number',
    },
    'email_otp_request_cnt': {
        'label': 'Number of Email OTP request in a form',
        'type': 'number',
    },
}


class OTPEmail(DynamicformBase):
    name = 'otp_email'
    title = 'OTP Email'
    sub_title = ''
    description = 'Validate email addresses, effectively safeguarding the platform from fraudulent activities and maintaining a trustworthy user base.'
    icon = ''
    option_icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/uppass_verification_services_36/EmailOTP.png'
    data_point_opitons = DATAPOINT_OPTIONS
    required_item_types = [['OTP.VerifyEmail']]
    required_item_types_auto_connect = True

    def get_data_result(self, **kwargs):
        from otp.models import MobileOtpVerifications

        result = {}
        slug = self.applied_form.slug
        otp = MobileOtpVerifications.objects.filter(ref=slug).only(
            'channel',
            'status',
            'attempt',
        )
        email_otp = otp.filter(channel='email')
        email_otp_count = email_otp.count()

        result['fail_email_otp_cnt'] = self.get_number_of_fail_email_otp(email_otp)
        result['email_otp_request_cnt'] = email_otp_count

        return result

    def get_number_of_fail_email_otp(self, email_otp):
        attempt_count = email_otp.aggregate(Sum('attempt'))['attempt__sum']
        success_otp_count = email_otp.filter(status='success').count()
        result = attempt_count - success_otp_count

        return result
