
from datetime import datetime
from enum import Enum

from data_point.tests.provider.mock_up_service_result.mock_asia_verify_result import MockAsiaVerifyServiceResult
from dateutil.relativedelta import relativedelta


class AsiaVerifyValidateInputResultTestCase:
    class ExpectResult(Enum):
        SUCCESS_VALIDATE = True
        FAILED_VALIDATE = False
        
    ### Set of each test case
    # - Test case name
    # - Test case input   
    # - Expect result
    TEST_CASE: list = [
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: Japan case',
            {
                "country": "JPN",
                "company_id": "6010401027577"
            },
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: Malaysia case',
            {
                "country": "MYS",
                "company_id": "6010401027577"
            },
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: Singapore case',
            {
                "country": "SGP",
                "company_id": "6010401027577"
            }, 
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: Philipine case',
            {
                "country": "PHL",
                "company_id": "6010401027577"
            }, 
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
         (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: Philipine case',
            {
                "country": "AUS",
                "company_id": "6010401027577"
            }, 
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: None country',
            {
                "country": None,
                "company_id": "6010401027577"
            }, 
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'eKYB Business (Asia Verify): Country cannot be empty',
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: None company_id',
            {
                "country": "SGP",
                "company_id": None,
            }, 
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'eKYB Business (Asia Verify): Company ID cannot be empty',
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: None Input',
            {
                "country": None,
                "company_id": None,
            }, 
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'eKYB Business (Asia Verify): Company ID, Country cannot be empty',
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: Not country in country select',
            {
                "country": "THA",
                "company_id": "6010401027577"
            }, 
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'eKYB Business (Asia Verify): This country is not supported: THA',
            },
        ),
    ]


class AsiaVerifyAPIServiceTestCase:
    ''' Collects all test cases that use in 
        test_asia_verify_api_service
    '''
    class ExpectResult(Enum):
        SUCCESS_VALIDATE = True
        FAILED_VALIDATE = False
        
    ### Set of each test case
    # - Test case name
    # - Test case input   
    # - Expect result
    TEST_CASE: list = [
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}',
            {
                'status_code': 200,
                'result': {
                    "result": {
                        "data": {
                            "Shareholders": {},
                            "majorPerson": {},
                        }
                        }
                    },
                'error': None
            },
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: 404 not found.',
            {
                'status_code': 404,
                'result': {'detail': 'not found'},
                'error': None
            },
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'eKYB Business (Asia Verify): Asia verify service is temporarily unavailable: 404',
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: handle no data found in response',
            {
                'status_code': 200,
                'result': {},
                'error': None
            },
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": "eKYB Business (Asia Verify): Not found any Company Information in response: {'country': 'JPN', 'number': 'Test1234567'}",
            },
        )
    ]


class AsiaVerifyDatapointOptionResultTestCase:
    ''' Collects all test cases that use in 

    '''
    class ExpectResult(Enum):
        SUCCESS_VALIDATE = True
        FAILED_VALIDATE = False
        
        @staticmethod
        def data_point_result(
            company_name: str|None = None,
            registered_date: str|None = None,
            company_age: int|None = None,
            company_age_month: int|None = None,
            company_status: str|None = None,
            capital: float|None = None,
            company_name_en : str|None = None,
        ):
            if registered_date:
                start_date = datetime.strptime(registered_date, "%Y-%m-%d")
                current_date = datetime.now()
                
                # Calculate the date difference using relativedelta
                date_diff = relativedelta(current_date, start_date)
                
                # Calculate the total difference in months
                company_age = date_diff.years
                company_age_month = (date_diff.years * 12) + date_diff.months
            return {
                'company_name': company_name,
                'registered_date': registered_date,
                'company_age': company_age,
                'company_age_month': company_age_month,
                'company_status': company_status,
                'capital': capital,
                'company_name_en': company_name_en,
            }

    class KeywordTestCaseName(Enum):
        ASIA_VERIFY_JPN = 'asia_verify_jpn'
        ASIA_VERIFY_MYS = 'asia_verify_mys'
        ASIA_VERIFY_SGP = 'asia_verify_sgp'
        ASIA_VERIFY_HKG = 'asia_verify_hkg'
    
    TEST_CASE: list[tuple] = [
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_JPN.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.JPN_RESULT['result']['data'],
            },
            ExpectResult.data_point_result(
                company_name="本田技研工業株式会社",
                registered_date="1948-09-24",
                company_age=75,
                company_age_month=910,
                company_status="",
                capital=86067000000.00,
                company_name_en="",
            )
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_MYS.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.MYS_RESULT['result']['data'],
            },
            ExpectResult.data_point_result(
                company_name="BIG 10 SUPER STORE (TERENGGANU) SDN. BHD.",
                registered_date="2010-04-28",
                company_age=14,
                company_age_month=171,
                company_status="Exists",
                capital=100000,
                company_name_en="BIG 10 SUPER STORE (TERENGGANU) SDN. BHD.",
            )
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_SGP.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.SGP_RESULT['result']['data'],
            },
            ExpectResult.data_point_result(
                company_name="23 FILM COMPANY PTE. LTD.",
                registered_date="2019-02-12",
                company_age=5,
                company_age_month=66,
                company_status="Live Company",
                capital=1.00,
                company_name_en="23 FILM COMPANY PTE. LTD.",
            )
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_HKG.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.HKG_RESULT['result']['data'],
            },
            ExpectResult.data_point_result(
                company_name="理盛國際有限公司",
                registered_date="2014-10-10",
                company_age=9,
                company_age_month=118,
                company_status="Live",
                capital=10000,
                company_name_en="LSH INTERNATIONAL CO., LIMITED",
            )
        )
    ]

    

