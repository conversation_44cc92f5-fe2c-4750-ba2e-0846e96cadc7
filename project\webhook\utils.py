import os
from django.conf import settings
from importlib import import_module

services_ms_forms = settings.SERVICES_MS_FORMS

def import_callable(path_or_callable):
  if hasattr(path_or_callable, '__call__'):
    return path_or_callable
  else:
    assert isinstance(path_or_callable, str)
    package, attr = path_or_callable.rsplit('.', 1)
    return getattr(import_module(package), attr)

def get_ms_form_credential(form_slug, enpoint, username, password):
    microservice = services_ms_forms.get(form_slug, {})
    host = microservice.get('host', None)
    app_id = microservice.get('app_id', None)
    app_secrect = microservice.get('app_secrect', None)
    
    if host:
        enpoint = host
    # eg. SERVICES_MS_FORMS_credit-loan_app_id
    ms_app_id = os.environ.get(f'SERVICES_MS_FORMS_{form_slug}_app_id', app_id)
    # eg. SERVICES_MS_FORMS_credit-loan_app_secrect
    ms_app_secrect = os.environ.get(f'SERVICES_MS_FORMS_{form_slug}_app_secrect', app_secrect)
    if ms_app_id:
        username = ms_app_id
    if ms_app_secrect:
        password = ms_app_secrect

    return enpoint, username, password