from django.db import models
from dynamicform.submodules.model import AutoNow


class CustomStatusKey(AutoNow):
    form = models.ForeignKey(
        'Form',
        on_delete=models.CASCADE,
    )
    value = models.CharField(
        max_length=128,
    )
    description = models.CharField(
        max_length=150,
        null=True,
        blank=True,
    )
    icon = models.CharField(
        max_length=128,
        null=True,
    )
    priority = models.IntegerField(null=True, default=0)

    class Meta:
        verbose_name = 'custom status key'
        verbose_name_plural = 'custom status keys'
        constraints = [
            models.UniqueConstraint(
                fields=['form', 'value'],
                name='unique_custom_status_key',
            ),
        ]


class CustomStatusValue(AutoNow):
    custom_status_key = models.ForeignKey(
        'CustomStatusKey',
        on_delete=models.CASCADE,
    )
    value = models.CharField(
        max_length=128,
    )
    color = models.Char<PERSON>ield(
        max_length=128,
        null=True,
    )
    priority = models.IntegerField(null=True, default=0)

    class Meta:
        verbose_name = 'custom status value'
        verbose_name_plural = 'custom status values'
        constraints = [
            models.UniqueConstraint(
                fields=['custom_status_key', 'value'],
                name='unique_custom_status_value',
            ),
        ]
