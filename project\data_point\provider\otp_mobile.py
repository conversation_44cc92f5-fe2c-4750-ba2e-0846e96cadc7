import logging

from data_point.base import DynamicformBase
from django.db.models import Sum

logger: logging.Logger = logging.getLogger(__name__)

DATAPOINT_OPTIONS = {
    'fail_mobile_otp_cnt': {
        'label': 'Number of times user failed to verify Mobile OTP',
        'type': 'number',
    },
    'mobile_otp_request_cnt': {
        'label': 'Number of Mobile OTP request in a form',
        'type': 'number',
    },
}


class OTPMobile(DynamicformBase):
    name = 'otp_mobile'
    title = 'OTP Mobile'
    sub_title = ''
    description = 'Validate phone numbers, effectively safeguarding the platform from fraudulent activities and maintaining a trustworthy user base.'
    icon = ''
    option_icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/uppass_verification_services_36/MobileOTP.png'
    data_point_opitons = DATAPOINT_OPTIONS
    required_item_types = [['OTP.Verify']]
    required_item_types_auto_connect = True

    def get_data_result(self, **kwargs):
        from otp.models import MobileOtpVerifications

        result = {}
        slug = self.applied_form.slug
        otp = MobileOtpVerifications.objects.filter(ref=slug).only(
            'channel',
            'status',
            'attempt',
        )
        mobile_otp = otp.filter(channel='mobile')
        mobile_otp_count = mobile_otp.count()

        result['fail_mobile_otp_cnt'] = self.get_number_of_fail_mobile_otp(mobile_otp)
        result['mobile_otp_request_cnt'] = mobile_otp_count

        return result

    def get_number_of_fail_mobile_otp(self, mobile_otp):
        attempt_count = mobile_otp.aggregate(Sum('attempt'))['attempt__sum']
        success_otp_count = mobile_otp.filter(status='success').count()
        result = attempt_count - success_otp_count

        return result
