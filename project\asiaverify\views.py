import logging
from django.shortcuts import get_object_or_404
from rest_framework.viewsets import mixins
import json
from pydash import get as pydash_get
from rest_framework.response import Response
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny
from django.conf import settings

from django.views.decorators.csrf import csrf_exempt
from asiaverify.models import AsyncAsiaVerify

logger: logging.Logger = logging.getLogger(__name__)

# Create your views here.
class AsyncAsiaVerifyViewSet(viewsets.GenericViewSet, mixins.RetrieveModelMixin):
    def get_authenticators(self):
        if self.request.method in ["POST"]:  # callback
            return []

        return super().get_authenticators()

    def get_permissions(self):
        if self.action in ["callback", "callback_v2"]:
            return [AllowAny()]

        return super().get_permissions()

    @action(detail=False, methods=["POST"], url_path="webhook.site/callback")
    def callback(self, request, *args, **kwargs):
        """
        Handles webhook callback from AsiaVerify for Hong Kong.

        This function performs the following steps:
        1. Validates the request by checking the source IP to ensure it's from AsiaVerify.
        2. Parses the request body and verifies the presence of the 'orderNo' field.
        3. Ensures that the 'orderNo' does not already exist in the database (only first-time requests are saved).
        4. Saves the request data if all conditions are met.

        Returns:
            HTTP Response:
                200 OK: If the request is successfully processed.
                400 Bad Request: If 'orderNo' is missing or invalid.
                409 Conflict: If 'orderNo' already exists in the database.
                403 Forbidden: If IP validation fails.
        """
        is_success = False
        message = ""
        status_code: int

        # Allow only Asia Verify ip only
        forwarded_header = request.META.get('HTTP_FORWARDED', '')

        # Initialize IP to None
        client_ip = None
        
        # Parse the Forwarded header if it exists
        if forwarded_header:
            parts = forwarded_header.split(';')
            for part in parts:
                if 'for=' in part:
                    # Extract the IP after 'for='
                    client_ip = part.split('=')[1].strip().strip('"')
                    break
        
        # Get order_no for validate
        result = json.loads(request.body)
        order_no = pydash_get(result, "result.orderNo")
        allowed_ips = settings.DATA_POINT_ASIA_VERIFY_WEBHOOK_IP
        if "*" not in allowed_ips and client_ip not in allowed_ips:
            message = "Not Found"
            status_code = status.HTTP_403_FORBIDDEN
        elif not order_no:
            message = "Creation failed: Not Found 'order_no' in request body."
            status_code = status.HTTP_400_BAD_REQUEST
        else:
            get_hk_result = AsyncAsiaVerify.objects.filter(order_no=order_no)
            if not get_hk_result:
                is_success = True
                message = "Creation successful."
                status_code = status.HTTP_200_OK
                AsyncAsiaVerify.objects.create(order_no=order_no , result=result)
            else:
                message = "Creation failed: The order number you entered already exists."
                status_code = status.HTTP_409_CONFLICT
        
        logger.info(f'Asia verify callback: response {status_code}', {
                "is_success": is_success,
                "message": message,
            })
        return Response({
                "is_success": is_success,
                "message": message,
            }, status=status_code)
    
    
    @action(detail=False, methods=["POST"], url_path="webhook.site/callback_v2")
    def callback_v2(self, request, *args, **kwargs):
        """
        Handles webhook callback from AsiaVerify for Hong Kong (v2).

        This function performs the following steps:
        1. Validates the request by checking the source IP to ensure it's from AsiaVerify.
        2. Parses the request body and verifies the presence of the 'orderNo' field.
        3. Ensures that the 'orderNo' does not already exist in the database (only first-time requests are saved).
        4. Saves the request data if all conditions are met.

        Returns:
            HTTP Response:
                200 OK: If the request is successfully processed.
                400 Bad Request: If 'orderNo' is missing or invalid.
                409 Conflict: If 'orderNo' already exists in the database.
                403 Forbidden: If IP validation fails.
        """
        is_success = False
        message = ""
        status_code: int

        # Allow only Asia Verify ip only
        forwarded_header = request.META.get('HTTP_FORWARDED', '')

        # Initialize IP to None
        client_ip = None
        
        # Parse the Forwarded header if it exists
        if forwarded_header:
            parts = forwarded_header.split(';')
            for part in parts:
                if 'for=' in part:
                    # Extract the IP after 'for='
                    client_ip = part.split('=')[1].strip().strip('"')
                    break
        
        # Get order_no for validate
        result = json.loads(request.body)
        order_no = pydash_get(result, "orderNo")

        allowed_ips = settings.DATA_POINT_ASIA_VERIFY_WEBHOOK_IP
        if "*" not in allowed_ips and client_ip not in allowed_ips:
            message = "Not Found"
            status_code = status.HTTP_403_FORBIDDEN
        elif not order_no:
            message = "Creation failed: Not Found 'order_no' in request body."
            status_code = status.HTTP_400_BAD_REQUEST
        else:
            get_hk_result = AsyncAsiaVerify.objects.filter(order_no=order_no)
            if not get_hk_result:
                is_success = True
                message = "Creation successful."
                status_code = status.HTTP_200_OK
                AsyncAsiaVerify.objects.create(order_no=order_no , result=result)
            else:
                message = "Creation failed: The order number you entered already exists."
                status_code = status.HTTP_409_CONFLICT
        
        logger.info(f'Asia verify callback: response {status_code}', {
                "is_success": is_success,
                "message": message,
            })
        return Response({
                "is_success": is_success,
                "message": message,
            }, status=status_code)
