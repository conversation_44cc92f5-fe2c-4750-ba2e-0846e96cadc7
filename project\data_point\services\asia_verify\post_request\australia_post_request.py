from pydash import get


class AustraliaPostRequest:
    '''
    PostRequest
    - [percentageOwned] Australia not have any of percentageOwned in shareholder
    '''
    @classmethod
    def process(cls, v2_response: dict):
        company_information = get(v2_response, 'result', {})
        shareholders_data_list: list = get(
            company_information, 
            'Shareholders.data.individual', 
            []
        )
        total_shares = 0.0
        for shareholders_data in shareholders_data_list:
            total_shares += float(get(shareholders_data, 'numberOfShares', 0))
        
        for shareholders_data in shareholders_data_list:
            number_of_shares = float(get(shareholders_data, 'numberOfShares', 0))
            if total_shares == 0:
                shareholders_data['percentageOwned'] = 0.0
            else:
                shareholders_data['percentageOwned'] = round(number_of_shares / total_shares, 2) * 100
        return v2_response