import pytz
import json
from django.conf import settings
from rest_framework import serializers
from rest_pandas.serializers import PandasSerializer
from dynamicform.helpers.escape import csv_injection_escape
from dynamicform.models import Application
from dynamicform.submodules.answer.models import Answer

class ApplicationPartitionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Application
        fields = '__all__'

    def to_representation(self, obj):

        try:
            answers = json.loads(obj.answers)
        
            for question, value_obj in answers.items():
                value = value_obj.get('value', '')
                is_encrypted = value_obj.get('is_encrypt', False)

                if is_encrypted:
                    decrypted_value = Answer.decrypt_value(None, value, form=obj.form)
                    try:
                        decrypted_value = json.loads(decrypted_value)
                    except Exception:
                        pass
                    answers[question] = decrypted_value
                else:
                    answers[question] = value_obj.get('value', '')
        except:
            answers = {}
        try:
            custom_status = json.loads(obj.custom_status)
        except:
            custom_status = {}

        application_created_at = (
            obj.application_created_at.astimezone(pytz.timezone(settings.TIME_ZONE)).isoformat()
            if obj.application_created_at
            else None
        )
        application_submitted_at = (
            obj.application_submitted_at.astimezone(pytz.timezone(settings.TIME_ZONE)).isoformat()
            if obj.application_submitted_at
            else None
        )

        return {
            "id": obj.application_id,
            "created_at": application_created_at,
            "slug": obj.application_slug,
            "status": obj.application_status,
            "submitted_at": application_submitted_at,
            "applied_form": {
                "info": {
                    "form": obj.form_slug,
                    "applied_form": obj.application_slug,
                    "form_name": obj.form_name
                }
            },
            "data": answers,
            "custom_status": custom_status
        }


class ApplicationPartitionDownloadSerializer(serializers.ModelSerializer):
    class Meta:
        model = Application
        fields = '__all__'
        list_serializer_class = PandasSerializer
        pandas_index = ['application_slug']

    def to_representation(self, obj):
        try:
            answers = json.loads(obj.answers)
        
            for question, value_obj in answers.items():
                value = value_obj.get('value', '')
                is_encrypted = value_obj.get('is_encrypt', False)

                if is_encrypted:
                    decrypted_value = Answer.decrypt_value(None, value, form=obj.form)
                    try:
                        decrypted_value = json.loads(decrypted_value)
                    except Exception:
                        pass
                    answers[question] = decrypted_value
                else:
                    answers[question] = value_obj.get('value', '')
        except:
            answers = {}
        try:
            custom_status = json.loads(obj.custom_status)
        except:
            custom_status = {}

        application_created_at = (
            obj.application_created_at.astimezone(pytz.timezone(settings.TIME_ZONE)).isoformat()
            if obj.application_created_at
            else None
        )
        application_submitted_at = (
            obj.application_submitted_at.astimezone(pytz.timezone(settings.TIME_ZONE)).isoformat()
            if obj.application_submitted_at
            else None
        )

        for k in answers:
            answers[k] = csv_injection_escape(answers[k])
        for k in custom_status:
            custom_status[k] = csv_injection_escape(custom_status[k])

        data = {
            'application_slug': obj.application_slug,
            'form_slug': obj.form_slug,
            'application_created_at': application_created_at,
            'application_submitted_at': application_submitted_at,
            'application_status': obj.application_status,
            **custom_status,
            **answers
        }
        return data
