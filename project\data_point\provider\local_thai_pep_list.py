import logging
import inspect
from pydash import get

from data_point.base import DataPointBase
from data_point.exceptions import DataPointException, DataPointInputValidationError, DataPointResponseException
from data_point.fields.base import DataPoint<PERSON><PERSON><PERSON>ield
from django.conf import settings
from rest_framework import status

from data_point.utils import ErrorMessage

logger: logging.Logger = logging.getLogger(__name__)

DATA_POINT_LOCAL_THAI_PEP_LIST_HOST = settings.DATA_POINT_LOCAL_THAI_PEP_LIST_HOST
DATA_POINT_LOCAL_THAI_PEP_LIST_USERNAME = settings.DATA_POINT_LOCAL_THAI_PEP_LIST_USERNAME
DATA_POINT_LOCAL_THAI_PEP_LIST_PASSWORD = settings.DATA_POINT_LOCAL_THAI_PEP_LIST_PASSWORD

DATAPOINT_OPTIONS = {
    'is_any_found': {
        'label': '',
        'type': 'boolean',
    },
    'is_all_found': {
        'label': '',
        'type': 'boolean',
    },
    'is_any_in_house_of_representative': {
        'label': '',
        'type': 'boolean',
    },
    'is_all_in_house_of_representative': {
        'label': '',
        'type': 'boolean',
    },
    'is_any_president_of_provincial': {
        'label': '',
        'type': 'boolean',
    },
    'is_all_president_of_provincial': {
        'label': '',
        'type': 'boolean',
    },
    'is_any_minister': {
        'label': '',
        'type': 'boolean',
    },
    'is_all_minister': {
        'label': '',
        'type': 'boolean',
    },
    'is_any_in_police_armforces': {
        'label': '',
        'type': 'boolean',
    },
    'is_all_in_police_armforces': {
        'label': '',
        'type': 'boolean',
    },
    'is_any_president_of_subdistrict': {
        'label': '',
        'type': 'boolean',
    },
    'is_all_president_of_subdistrict': {
        'label': '',
        'type': 'boolean',
    },
    'is_any_senator': {
        'label': '',
        'type': 'boolean',
    },
    'is_all_senator': {
        'label': '',
        'type': 'boolean',
    },
}


class LocalThaiPepList(DataPointBase):
    full_name = DataPointJSONField(
        label='Full Name',
        type='string',
        required=True,
        is_array=True,
        split_fields=True,
        allowed_item_builder_types=['full_name'], #  allow only full name buildery
        extra_fields={
            "validate_only_lastname": {
                "label": "Validate with Last Name Only",
                "type": "Checkbox",
            },
        }
    )
       
    name = 'local_thai_pep_list'
    title = 'Local Thai PEP List'
    sub_title = 'Politically exposed person screening'
    description = 'Compare names against the local Thai PEP databases'
    icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/icon_64/ThaiPep_64.png'
    option_icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/option-icon_36/ThaiPep_36.png'
    data_point_opitons = DATAPOINT_OPTIONS
    
    def validate_full_name(self, value):
        self.is_valid_payload_connection(value, field_name='full_name')
        return value

    def set_current_payload(self):
        self.set_payload('full_name')
        self.set_payload('first_name')
        self.set_payload('middle_name')
        self.set_payload('last_name')
        return self.current_payload

    def get_data_result(self, **kwargs):
        full_name_list:list = self.get_payload('full_name', extra_fields_required=True)
        last_name_list:list = self.get_payload('last_name', extra_fields_required=False)
        
        # Handle if every value that set is_array to True is None or empty (see reference case in Clickup: 86cv5v5t6)
        # Throw exception if no input value in field that is_array is True
        if not full_name_list or all(not value for value in [v.get('value') for v in full_name_list]):
            full_name_field: DataPointJSONField = self.fields.get('full_name')
            raise DataPointInputValidationError(f'{self.title}: {ErrorMessage.NO_VALUE_FOR_DATAPOINT.value.format(**{'field': full_name_field.label})}')
        
        # Setup uppass input and local_thai_pep_list response
        save_result = {
            'local_thai_pep_list': []
        }
        local_thai_pep_response_list = []
        result = None
        
        for index, full_name_info in enumerate(full_name_list):
            full_name = full_name_info.get('value')
            validate_only_lastname = get(full_name_info, 'extra_fields.validate_only_lastname', False)
            
            # If full name is none skip this input
            if full_name is None :
                continue

            is_input_valid, error_message = self.validate_input(full_name, last_name_list, validate_only_lastname)

            # Setup led_input (request body)
            local_thai_pep_list_input = {
                "full_name": full_name
            }
            # Add last_name value if validate only last name is avaliable.
            if validate_only_lastname:
                local_thai_pep_list_input['last_name'] = last_name_list[index]
            
            if not is_input_valid :
                error_result =  self.invalid_input_error_result(error_message, DataPointInputValidationError)
                if 'error_result_tracking' in kwargs.keys() :
                    self.prepare_error_result_for_save_db(save_result, error_result, kwargs['error_result_tracking'])
                return error_result
            else:
                local_thai_pep_list_response = self.search_thai_pep_list(local_thai_pep_list_input)
                local_thai_pep_response_list.append({
	                'input': local_thai_pep_list_input,
	                'output': local_thai_pep_list_response,
	            })
                local_thai_pep_info: dict = local_thai_pep_list_response.get('data')
	
	            # Setup or update data point options result
                result = self.set_data_point_options_result(result, local_thai_pep_info)
    
        # Return error result if any full_name is invalid
        save_result['local_thai_pep_list'] = local_thai_pep_response_list
        self.set_current_data_point_output(save_result)
        return result         
            
        
    def search_thai_pep_list(self, local_thai_pep_input: dict):
        data_point_function_name = inspect.currentframe().f_code.co_name
        url = f'{DATA_POINT_LOCAL_THAI_PEP_LIST_HOST}/v1/thai_pep_list/search'
        
        result = self._request(
            method='GET',
            url=url,
            params=local_thai_pep_input,
            auth=(DATA_POINT_LOCAL_THAI_PEP_LIST_USERNAME, DATA_POINT_LOCAL_THAI_PEP_LIST_PASSWORD),
            data_point_function_name=data_point_function_name,
        )
        response_data = result.get('result')
        status_code = result.get('status_code')
        logging_context = self.get_logging_context()
        
        data_point_log_info = {
            'url': url,
            'json': local_thai_pep_input,
            'context': logging_context,
            'status_code': status_code,
        }
        logger.info(
            f'Local thai pep list response {status_code}',
            data_point_log_info
        )
        if not status.is_success(status_code):
            # raise DataPointException(
            #     f'Local thai pep list response {status_code} : {response_data}',
            #     data_point_log_info=data_point_log_info
            # )
            raise DataPointResponseException(
                f"{self.title}: {ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'Local Thai PEP List'})}: {status_code}",
                data_point_log_info=data_point_log_info
            )
        return response_data
    
    def set_data_point_options_result(
            self,
            current_result: dict[str, bool] | None,
            thai_pep_info: dict
        ) -> dict:
        if current_result is None :
            current_result = {
                'is_any_found': False,
                'is_all_found': True,
                'is_any_in_house_of_representative': False,
                'is_all_in_house_of_representative': True,
                'is_any_president_of_provincial': False,
                'is_all_president_of_provincial': True,
                'is_any_minister': False,
                'is_all_minister': True,
                'is_any_in_police_armforces': False,
                'is_all_in_police_armforces': True,
                'is_any_president_of_subdistrict': False,
                'is_all_president_of_subdistrict': True,
                'is_any_senator': False,
                'is_all_senator': True,
            }
        house_of_representative_type = ["สส.เขต" , "สส.บัญชีรายชื่อ"]
        president_of_provincial_type = ["อบจ"]
        minister_type = ["รัฐมนตรีและรองรัฐมนตรี"]
        police_armforces_type = ["ตำรวจ", "ทหาร"]
        president_of_subdistrict = ["อบต"]
        senator = ["สว"]

        # Setup flaging for update data point options result
        is_found = bool(thai_pep_info)
        is_house_of_representative = thai_pep_info.get("type", "") in house_of_representative_type
        is_president_of_provincial = thai_pep_info.get("type", "") in president_of_provincial_type
        is_minister = thai_pep_info.get("type", "") in minister_type
        is_in_police_armforces = thai_pep_info.get("type", "") in police_armforces_type
        is_president_of_subdistrict = thai_pep_info.get("type", "") in president_of_subdistrict
        is_senator = thai_pep_info.get("type", "") in senator

        # Update data point options result (is_all...)
        current_result['is_all_found'] = current_result['is_all_found'] and is_found
        current_result['is_all_in_house_of_representative'] = current_result['is_all_in_house_of_representative'] and is_house_of_representative
        current_result['is_all_president_of_provincial'] = current_result['is_all_president_of_provincial'] and is_president_of_provincial
        current_result['is_all_minister'] = current_result['is_all_minister'] and is_minister
        current_result['is_all_in_police_armforces'] = current_result['is_all_in_police_armforces'] and is_in_police_armforces
        current_result['is_all_president_of_subdistrict'] = current_result['is_all_president_of_subdistrict'] and is_president_of_subdistrict
        current_result['is_all_senator'] = current_result['is_all_senator'] and is_senator

        # Update data point options result (is_any...)
        current_result['is_any_found'] = current_result['is_any_found'] or is_found
        current_result['is_any_in_house_of_representative'] = current_result['is_any_in_house_of_representative'] or is_house_of_representative
        current_result['is_any_president_of_provincial'] = current_result['is_any_president_of_provincial'] or is_president_of_provincial
        current_result['is_any_minister'] = current_result['is_any_minister'] or is_minister
        current_result['is_any_in_police_armforces'] = current_result['is_any_in_police_armforces'] or is_in_police_armforces
        current_result['is_any_president_of_subdistrict'] = current_result['is_any_president_of_subdistrict'] or is_president_of_subdistrict
        current_result['is_any_senator'] = current_result['is_any_senator'] or is_senator
        
        return current_result

    def validate_input(
        self, 
        full_name: str | None,
        last_name_list: list = [],
        validate_only_lastname: bool = False
    )  -> tuple[bool, str | None]:
        '''Validate input that is valid for Local thai pep list connection
        
        Validate checklist
        - full_name must not be none
        - last_name must be part of any of full_name
        
        Args:
            full_name (str): full_name that need to validate
            last_name_list (list): last_name that need to validate
            validate_only_lastname (bool): validate last name if send true

        Returns:
            bool: True if inputs valid orElse, return False
        '''
        full_name_field: DataPointJSONField = self.fields.get('full_name')
        is_input_valid = True
        error_message = None
        if not full_name:
            is_input_valid = False
            format_text = {"fields": full_name_field.label,}
            error_message = f'{self.title}: {ErrorMessage.EMPTY_VALUE.value.format(**format_text)}'
        elif validate_only_lastname:
            last_name_valid_list = []
            for last_name in last_name_list:
                if last_name is None: 
                    continue
                if last_name in full_name:
                    last_name_valid_list.append(last_name)
            if not last_name_valid_list:
                is_input_valid = False
                format_text = {"field": full_name_field.label,}
                error_message = f'{self.title}: {ErrorMessage.INVALID_INPUT.value.format(**format_text)}'
        return is_input_valid, error_message
