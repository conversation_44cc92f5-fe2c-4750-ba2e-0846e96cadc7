import requests
import json
import jwt
from datetime import datetime
from rest_framework import status

from django.core.files.uploadedfile import UploadedFile
from django.conf import settings
from django.core.cache import cache

from .types.v2 import (
    CreateBatchApiResponse,
    UploadApiResponse,
    GetResultApiResponse,
    ListTransactionsApiResponse,
    ValidateApiResponse,
)

BANK_STATEMENT_GATEWAY_ENDPOINT = settings.BANK_STATEMENT_GATEWAY_ENDPOINT
BANK_STATEMENT_GATEWAY_USERNAME = settings.BANK_STATEMENT_GATEWAY_USERNAME
BANK_STATEMENT_GATEWAY_PASSWORD = settings.BANK_STATEMENT_GATEWAY_PASSWORD

BANK_STATEMENT_GATEWAY_TOKEN_USER_ID = settings.BANK_STATEMENT_GATEWAY_TOKEN_USER_ID
BANK_STATEMENT_GATEWAY_TOKEN_COMPANY_NAME = settings.BANK_STATEMENT_GATEWAY_TOKEN_COMPANY_NAME
BANK_STATEMENT_GATEWAY_JWT_PASSWORD = settings.BANK_STATEMENT_GATEWAY_JWT_PASSWORD

token_cache_timeout = 60
cache_key = "bankstatement_token"

GET_TOKEN_FAILED = "Get token failed"


def get_current_time():
    return datetime.now()


def get_token():
    bearer_token = cache.get(cache_key)
    if bearer_token:
        return bearer_token

    payload = {
        "user_id": int(BANK_STATEMENT_GATEWAY_TOKEN_USER_ID),
        "company_name": BANK_STATEMENT_GATEWAY_TOKEN_COMPANY_NAME,
    }
    jwt_algorithm = "RS256"
    key = bytes(BANK_STATEMENT_GATEWAY_JWT_PASSWORD, "utf-8")

    token = jwt.encode(payload, key, jwt_algorithm)
    bearer_token = f"Bearer {token}"
    cache.set(cache_key, bearer_token, token_cache_timeout)
    return bearer_token


def create_batch(slug: str):
    token = get_token()
    if not token:
        return GET_TOKEN_FAILED, 500

    headers = {"Authorization": token}
    data = {"slug": slug, "extract_by_inkredo": False}
    r = requests.post(
        BANK_STATEMENT_GATEWAY_ENDPOINT + "/v1/batch_bank_statement",
        headers=headers,
        json=data,
    )

    if status.is_success(r.status_code):
        result: CreateBatchApiResponse = r.json()
        return result, r.status_code
    return {"error": r.text}, r.status_code


def validate_batch(batch_id: int, validation: dict = {}):
    token = get_token()
    if not token:
        return GET_TOKEN_FAILED, 500

    headers = {"Authorization": token}
    r = requests.post(
        BANK_STATEMENT_GATEWAY_ENDPOINT + f"/v1/batch_bank_statement/{batch_id}/validate",
        headers=headers,
        json=validation,
    )
    if status.is_success(r.status_code):
        result: ValidateApiResponse = r.json()
        return result, r.status_code
    return {"error": r.text}, r.status_code


def upload_document(batch_id: int, file: UploadedFile, bank_code: str = "", password: str = "", payload: dict = {}):
    token = get_token()
    if not token:
        return GET_TOKEN_FAILED, 500

    headers = {"Authorization": token}
    data = {"batch_id": batch_id, "password": password, "bank_code": bank_code, "extract_by_inkredo": False, **payload}
    files = {
        # "bank_statement": file
        "bank_statement": (
            file.name,
            file,
            "application/pdf",
        )
    }
    r = requests.post(
        BANK_STATEMENT_GATEWAY_ENDPOINT + "/v1/bank_statement",
        headers=headers,
        data=data,
        files=files,
    )
    if status.is_success(r.status_code):
        result: UploadApiResponse = r.json()
        return result, r.status_code
    return {"error": r.text}, r.status_code


def get_document_result(request_id: str):
    token = get_token()
    if not token:
        return GET_TOKEN_FAILED, 500

    headers = {"Authorization": token}
    params = {"request_id": request_id, "hide_validation": False}
    r = requests.get(
        BANK_STATEMENT_GATEWAY_ENDPOINT + "/v1/bank_statement",
        headers=headers,
        params=params,
    )
    try:
        result: GetResultApiResponse = r.json()
        return result, r.status_code
    except Exception:
        return r.text, r.status_code


def delete_document(id: str):
    token = get_token()
    if not token:
        return GET_TOKEN_FAILED, 500

    headers = {"Authorization": token}
    r = requests.delete(
        BANK_STATEMENT_GATEWAY_ENDPOINT + f"/v1/bank_statement/{id}",
        headers=headers,
    )
    if r.status_code == 204:
        return True, r.status_code
    return {"error": r.text}, r.status_code


def list_transaction(id: str, query: dict = {}):
    token = get_token()
    if not token:
        return GET_TOKEN_FAILED, 500

    headers = {"Authorization": token}
    r = requests.get(
        BANK_STATEMENT_GATEWAY_ENDPOINT + f"/v1/bank_statement/{id}/transaction", headers=headers, params=query
    )
    if status.is_success(r.status_code):
        result: ListTransactionsApiResponse = r.json()
        return result, r.status_code
    return {"error": r.text}, r.status_code


def update_transaction(id: str, payload: dict = {}):
    token = get_token()
    if not token:
        return GET_TOKEN_FAILED, 500

    headers = {"Authorization": token}
    r = requests.patch(
        BANK_STATEMENT_GATEWAY_ENDPOINT + f"/v1/bank_statement/{id}/transaction", headers=headers, json=payload
    )
    if status.is_success(r.status_code):
        result: ListTransactionsApiResponse = r.json()
        return result, r.status_code
    return {"error": r.text}, r.status_code


def flag_batch_bank_statement(batch_id: int):
    token = get_token()
    if not token:
        return GET_TOKEN_FAILED, 500

    headers = {"Authorization": token}
    r = requests.get(BANK_STATEMENT_GATEWAY_ENDPOINT + f"/v1/batch_bank_statement/{batch_id}/flag", headers=headers)
    if status.is_success(r.status_code):
        result: ListTransactionsApiResponse = r.json()
        return result, r.status_code
    return {"error": r.text}, r.status_code

def get_summary(batch_id: str):
    token = get_token()
    if not token:
        return GET_TOKEN_FAILED, 500

    headers = {"Authorization": token}
    url = BANK_STATEMENT_GATEWAY_ENDPOINT + f"/v1/batch_bank_statement/{batch_id}/summary"
    r = requests.get(url, headers=headers)
    if status.is_success(r.status_code):
        result = r.json()
        return result, r.status_code
    return {"error": r.text}, r.status_code

def get_bank_list(country_code: str, language: str, is_corporate_statement: bool=False):
    token = get_token()

    if not token:
        return GET_TOKEN_FAILED, 500

    url = f'{BANK_STATEMENT_GATEWAY_ENDPOINT}/v2/bank_statement/support_list'
    headers = {
        'Authorization': token,
        'Accept-Language': language,
    }
    params = {
        'country': country_code,
        'is_corporate_statement': is_corporate_statement
    }
    
    r = requests.get(
        url,
        headers=headers,
        params=params,
    )

    if status.is_success(r.status_code):
        result = r.json()

        return result, r.status_code

    return {'error': r.text}, r.status_code

def get_flag_list():
    token = get_token()

    if not token:
        return GET_TOKEN_FAILED, 500

    headers = {'Authorization': token}
    url = f'{BANK_STATEMENT_GATEWAY_ENDPOINT}/v1/bank_statement/flag_list'

    r = requests.get(url, headers=headers)
    if status.is_success(r.status_code):
        result = r.json()
        return result, r.status_code

    return {'error': r.text}, r.status_code
