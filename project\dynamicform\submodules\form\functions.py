from django.apps import apps
from django.conf import settings
from django.db import models
from django.utils.translation import get_language
from workspace.events import convert_event_decision_flow_key_to_webhook_key
from rest_framework import status
from dynamicform.submodules.form.models.webhook import TRIGGER_EVENT_SUBMIT_FORM
from dynamicform.submodules.form.actions import FormAction
from dynamicform.submodules.page.models.page import Page
from dynamicform.schema.collecttext import collect_text
from dynamicform.schema.getapp import GetApp
from dynamicform.schema.rule import Rule
from dynamicform.schema.trans import trans
from dynamicform.util import mapping_params, mapping_value_format, current_user
from dynamicform.validator.validator import validate
from pydash import get, set_, to_list, find
import copy
import json
import logging
from datetime import datetime


logger: logging.Logger = logging.getLogger(__name__)
DATABASE_MODEL_USE_DB = settings.DATABASE_MODEL_USE_DB.get("form", {})
REPLACE_KEY_PATHS = [
    "submitted_page",
    "steps.ekyc_liveness.sections.ekyc_liveness.items.ekyc_max_attempt_warning.items.back_btn.target",
    "steps.ekyc_document.sections.ekyc_document_2.items.ekyc_max_attempt_warning.items.back_btn.target",
]
EKYC_REPORT_CELL_KEY = "___ekyc"


class FormFunctions(models.Model):
    class Meta:
        abstract = True

    def apply(self, *args, **kwargs):
        self.can_apply_form()

        appliedform = self.appliedform_set.create()
        appliedform = appliedform.set_first_step_setion()
        return appliedform

    def validate(self, answers, *args, **kwargs):
        is_valid = True
        errors = {}
        validate_all = kwargs.get("validate_all", False)
        if not answers and not validate_all:
            return is_valid, errors

        backend_schema = self.backend_schema
        rules = Rule.convert_rules(backend_schema["validator_rule"], **kwargs)
        labels = self.get_labels()
        errors = validate(answers, rules, overwrite_fields=labels)
        is_valid = not errors

        return is_valid, errors

    def update_lang(self, language, frontend_schema=None):
        from dynamicform.submodules.lang.models import Locale

        if not frontend_schema:
            frontend_schema = self.frontend_schema

        Locale.objects.filter(form=self, language=language).delete()
        text = collect_text(frontend_schema)
        for key, value in text.items():
            if type(value) == dict:
                value = json.dumps(value)
            Locale.objects.update_or_create(form=self, key=key, language=language, defaults={"text": value})

    def change_answer_key(self, old_key: str, new_key: str):
        from dynamicform.submodules.lang.models import Locale

        locales = Locale.objects.filter(form=self, key__contains=f".items.{old_key}.")
        for locale in locales:
            old_full_key = locale.key
            new_full_key = old_full_key.replace(f".items.{old_key}.", f".items.{new_key}.")
            locale.key = new_full_key

        Locale.objects.bulk_update(locales, ["key"])

    def trans(self, schema=None):
        if not schema:
            schema = self.frontend_schema
        lang = get_language()
        if not lang:
            return schema

        if self.dynamicform and self.dynamicform.locale_set:
            text = self.dynamicform.locale_set
        else:
            text = self.locale_set.filter(language=lang)

        return trans(schema, text)

    def get_labels(self):
        labels = {}
        backend_schema = self.backend_schema
        backend_labels = backend_schema["labels"]
        backend_labels_list = to_list(backend_labels)
        lang = get_language()

        if self.dynamicform and self.dynamicform.locale_set:
            labels_list = self.dynamicform.locale_set
        else:
            labels_list = self.locale_set.filter(key__in=backend_labels_list, language=lang)

        for item in labels_list:
            if item.key not in backend_labels_list:
                continue
            for question_name, label_key in backend_labels.items():
                if label_key == item.key:
                    labels[question_name] = item.text
                    break

        return labels

    def assign_user(self, user):
        if not self.users.filter(id=user.id).exists():
            self.users.add(user)

    def trigger_webhooks(
        self,
        applied_form,
        event_trigger=TRIGGER_EVENT_SUBMIT_FORM,
        data=None,
        merge_data=False,
        ids=None,
        **kwargs,
    ):
        from workspace.events import EVENTS_WEBHOOK_DECISION_FLOW

        form_webhooks = []
        need_to_check_currentdecisionflow = False
        currentdecisionflow_exists = False

        if ids:
            if type(ids) == str:
                ids = ids.split(",")

            if len(ids) > 0 and ids[0] != "":
                form_webhooks = self.formwebhook_set.filter(enable=True, id__in=ids)
        else:
            form_webhooks = self.formwebhook_set.filter(
                enable=True,
                # enable_auto_trigger=True,
                webhook__event_trigger=event_trigger,
            )
            need_to_check_currentdecisionflow = True
            decisionflow_event_trigger = EVENTS_WEBHOOK_DECISION_FLOW.get(event_trigger, None)
            if decisionflow_event_trigger:
                currentdecisionflow_exists = self.currentdecisionflow_set.filter(
                    event=decisionflow_event_trigger
                ).exists()

        if not data:
            all_data_answers = applied_form.get_report()
        else:
            all_data_answers = data

        data_answers = {
            "applied_form": applied_form,
            "application": applied_form.application,
        }
        main_applied_form_answers = {}
        success_all = True
        logger.info(f"trigger {event_trigger} {len(form_webhooks)}")

        for form_webhook in form_webhooks:
            if need_to_check_currentdecisionflow:
                if not form_webhook.enable_auto_trigger:
                    if currentdecisionflow_exists:
                        continue

            get_main_applied_form_answers = form_webhook.props.get("get_main_applied_form_answers", False)
            _main_applied_form_answers = {}
            if get_main_applied_form_answers and not main_applied_form_answers:
                _main_applied_form_answers = applied_form.main_applied_form.get_report()
                main_applied_form_answers = _main_applied_form_answers.copy()
            elif get_main_applied_form_answers and main_applied_form_answers:
                _main_applied_form_answers = main_applied_form_answers
            all_data_answers = {**_main_applied_form_answers, **all_data_answers}
            if merge_data:
                data_answers.update(all_data_answers)
            else:
                data_answers["answers"] = all_data_answers

            _task = self.trigger_webhook(form_webhook, applied_form, data=data_answers, **kwargs)
            try:
                status_code = int(_task.status_code)
                success_all = success_all and not status.is_server_error(status_code)
            except ValueError:
                success_all = False

        return success_all

    def trigger_webhook(self, form_webhook, applied_form, data=None, decision_flow_event=None, **kwargs):
        _json_template = form_webhook.json_template
        version = form_webhook.version
        task = form_webhook.webhook.create_task()

        if decision_flow_event:
            section_trigger_events = applied_form.form.backend_schema.get('section_trigger_events', [])
            task.event_trigger = convert_event_decision_flow_key_to_webhook_key(decision_flow_event, section_trigger_events)

        data["event"] = task
        
        applied_form.manual_update_ekyc_status = bool(int(kwargs.get("manual_update_ekyc_status", 0)))

        if _json_template:
            json_template = _json_template
        elif version == 1:
            json_template = applied_form.default_webhook_submit_body_template
        else:
            json_template = applied_form.default_webhook_submit_body_template_version_2

        body = mapping_params(data, json_template, map_request=False)
        url = getattr(form_webhook.webhook.webhook_method, "url", "")
        url = mapping_value_format(data, url)
        start_execution = start_execution = datetime.now()
        form_webhook.webhook.trigger(body=body, key=self.secret_key, overwrite_request={"url": url}, form=self)
        execution_time = datetime.now() - start_execution

        task = form_webhook.webhook.task

        if task:
            form = form_webhook.form
            workspace = form.form_of.workspace

            log_detail = {
                "created_at": str(task.created_at),
                "form_slug": form.slug,
                "applied_form_slug": applied_form.slug,
                "workspace_slug": workspace.slug,
                "workspace_name": workspace.name,
                "status": task.status_code,
                "webhook_event": task.event_trigger,
                "webhook_raw_response": task.response,
                "response_time": str(task.response_time),
                "task_id": task.id,
                "exception_class": task.exception_class,
                "time_spent": execution_time.total_seconds(),
            }
            logger.info(f"run form_webhook.webhook.trigger {task.id}", log_detail)

            self.formtask_set.create(task=task, applied_form=applied_form)
            actions = form_webhook.props.get("manage_results", [])
            action_answers = task.get_response()
            applied_form.actions(actions=actions, action_answers=action_answers)
        else:
            logger.info(f"run form_webhook.webhook.trigger NO TASK")

        return task

    def get_settings(self, path=None, default={}, secure=True):
        try:
            if self.settings:
                return self.settings.get_settings(path, default, secure)

            FormSetting = apps.get_model("dynamicform", "FormSetting")  # NOSONAR
            use_db = DATABASE_MODEL_USE_DB.get("read", "default")
            formsetting = FormSetting.objects.using(use_db).get(form_id=self.id)
            self.settings = formsetting
            value = formsetting.get_settings(path, default, secure)
            if path == "schema_config":
                return self.get_schema_config(value)
            if path == "dashboard":
                return self.get_dashboard(value)
            if value:
                return value
        except (Exception, FormSetting.DoesNotExist) as e:
            logger.warning(str(e))
        return self.get_settings_form_django_settings(path, default, secure)

    def get_settings_form_django_settings(self, path=None, default={}, secure=True):
        path = f"{self.slug}.{path}"
        return get(settings.FORM_SETTINGS, path, default)

    def is_my_form(self):
        user = current_user()
        if not user:
            return False

        if user.is_superuser:
            return True

        if user.is_api_token:
            return self.is_allow_token(user)

        if self.form_of.workspace.is_allow_member(user):
            return True

        return False

    def is_allow_token(self, user):
        return user.token.forms.filter(id=self.id).exists()

    def init_default_page_into_schema(self, submitted_page):
        frontend_schema = set_(self.frontend_schema, path="submitted_page", value=submitted_page)
        self.frontend_schema = frontend_schema
        self.save()

    def init_default_page(self):
        landing_page = Page.init_landing_page(self)
        thanks_page = Page.init_thanks_page(self)
        self.init_default_page_into_schema(thanks_page.name)
        return [landing_page, thanks_page]

    def do_actions_from_setting(self, setting_actions, answers={}, **kwargs):
        actions = self.get_settings(setting_actions, [])
        form_action = FormAction(self, actions, answers=answers)
        form_action.do_actions()
        return form_action.result

    def can_apply_form(self):
        self.can_use_credit()

    def can_use_credit(self): # NOSONAR
        from credit_system.feature_credits import FEATURE_CREDITS

        if not settings.ENABLE_CREDIT_SYSTEM:
            return True # NOSONAR

        feature_credits = self.backend_schema.get("feature_credits", [])
        min_credits_used = 0

        for feature in feature_credits:
            feature = FEATURE_CREDITS.get(feature, {})
            if not feature:
                continue

            min_credits_used += feature.get("credit_usage", 0)

        self.form_of.workspace.can_deduct_credit(min_credits_used)

        return True # NOSONAR

    def get_schema_config(self, schema_config):
        set_(schema_config, "configs.branding.show_branding", self.show_branding())
        return schema_config

    def show_branding(self):
        if not settings.ENABLE_CREDIT_SYSTEM or self.form_of.workspace.enable_post_paid is True:
            return False

        return self.form_of.workspace.is_free_package()

    def set_value_at_path(self, obj, str_1, str_2):
        for path in REPLACE_KEY_PATHS:
            target_value = get(obj, path)
            if target_value:
                set_(obj, path, target_value.replace(str_1, str_2))
        return obj

    def override_schema(self, schema):
        schema_override_settings = self.get_settings("schema_override.groups", [])
        for schema_override in schema_override_settings:
            if not schema_override.get("enabled"):
                continue
            mapping: dict[str, str] = schema_override.get("mapping", {})
            for path, value in mapping.items():
                set_(schema, path, value)

    def update_form_data_points(self):
        from dynamicform.models import CurrentDecisionFlow

        current_flows = CurrentDecisionFlow.objects.filter(form=self)
        data_points = []
        all_data_points = []
        for current_flow in current_flows:
            if not current_flow.decision_flow:
                continue

            _data_point = current_flow.decision_flow.backend_schema.get("data_point", {})
            if not _data_point:
                continue
            all_data_points = all_data_points + list(_data_point.keys())
            if current_flow.active:
                data_points = data_points + list(_data_point.keys())

        data_points = list(set(data_points))
        all_data_points = list(set(all_data_points))
        self.data_points = data_points
        self.all_data_points = all_data_points

        self.save(update_fields=["data_points", "all_data_points"])

    def __is_report_display_ekyc_status(self):
        form_setting_report_ekyc = self.get_settings(path="report.ekyc.components", default=[])
        if "Document" in form_setting_report_ekyc and "Liveness" in form_setting_report_ekyc:
            return True
        return False

    def get_dashboard(self, dashboard_config):
        from dynamicform.models import CustomStatusKey

        custom_status = CustomStatusKey.objects.prefetch_related("customstatusvalue_set").filter(form=self)

        list_custom_status_key = list(custom_status.values_list("value", flat=True))
        # add ekyc if found in config
        if self.__is_report_display_ekyc_status():
            list_custom_status_key.append(EKYC_REPORT_CELL_KEY)

        dashboard_config = FormFunctions.get_dashboard_custom_status(dashboard_config, list_custom_status_key)
        dashboard_config.update({"filter": self.get_dashboard_filter_config(custom_status)})

        return dashboard_config

    @staticmethod
    def get_dashboard_custom_status(dashboard_config, custom_status):
        current_dashboard_custom_status = []

        # 1. if found in dashboard_config do nothing
        # 2. if not found in dashboard_config then insert into columns
        def get_column_obj(value):
            if value == EKYC_REPORT_CELL_KEY:
                return {"cells": [{"type": "ekyc"}]}

            return {
                "cells": [
                    {
                        "type": "decision",
                        "label": value,
                        "class": "second-value",
                        "key": value,
                    }
                ]
            }

        # collect all custom status in dashboard
        def collect_columns_custom_status(columns_list, delete=False):
            form_status_column_index = None
            for column_index, column_cells in enumerate(columns_list):
                for cell in column_cells["cells"]:
                    cell_type = cell.get("type")

                    if cell_type == "status" and not delete:
                        form_status_column_index = column_index
                        continue

                    if cell_type != "decision":
                        continue

                    cell_key = cell.get("key", None)
                    if not cell_key:
                        continue

                    if delete:
                        # delete only if cell_key is not exists in custom_status
                        if cell_key not in custom_status:
                            column_cells["cells"].remove(cell)
                            continue

                    if cell_key not in current_dashboard_custom_status:
                        current_dashboard_custom_status.append(cell_key)

                if delete and len(column_cells["cells"]) == 0:
                    columns_list.remove(column_cells)

            return form_status_column_index

        form_status_column_index = collect_columns_custom_status(dashboard_config.get("columns", []))
        collect_columns_custom_status(dashboard_config.get("columns_hidden", []))

        # build inject columns
        new_columns = []
        for value in custom_status:
            if value not in current_dashboard_custom_status:
                new_columns.append(get_column_obj(value))

        # inject columns
        if form_status_column_index is None:
            form_status_column_index = len(dashboard_config.get("columns", []))

        for new_column in new_columns:
            dashboard_config["columns"].insert(form_status_column_index, new_column)

        collect_columns_custom_status(dashboard_config.get("columns", []), delete=True)
        collect_columns_custom_status(dashboard_config.get("columns_hidden", []), delete=True)

        return dashboard_config

    def get_dashboard_filter_config(self, custom_status):
        result = []
        visible_items_type: dict = self.backend_schema.get("visible_items_type", {})

        def append_ekyc():
            result.append(
                {
                    "label": "eKYC Status",
                    "value": "ekyc",
                    "icon": "lucide:user-search",
                    "options": [
                        {
                            "label": "Need Review",
                            "value": "need_review",
                            "color": "#997700"
                        },
                        {
                            "label": "Pass",
                            "value": "pass",
                            "color": "#21867F"
                        },
                        {
                            "label": "Fail",
                            "value": "fail",
                            "color": "#990D00"
                        },
                    ],
                }
            )

        has_liveness = find(visible_items_type, lambda v, k="": k.startswith("Ekyc.Liveness")) is not None
        has_document = find(visible_items_type, lambda v, k="": k.startswith("Ekyc.Document")) is not None
        if (has_liveness and has_document) or self.__is_report_display_ekyc_status():
            append_ekyc()

        for item in custom_status:
            options = []

            for item_value in item.customstatusvalue_set.all():
                options.append(
                    {
                        "value": item_value.value,
                        "color": item_value.color,
                        "priority": item_value.priority,
                    }
                )

            result.append({"value": item.value, "options": options, "icon": item.icon, "priority": item.priority})

        return result
