create or replace function hard_delete_applications(field varchar, submitted_null bool, setting_auto_delete varchar, ids integer[], workspace_slug varchar, query_limit integer)
returns table (
	id int
)
language plpgsql
as $func$ 
begin
RETURN QUERY

with 
applications_to_delete as (
	select application.id as application_id
	from get_application_to_delete(
	    field := field,
        submitted_null := submitted_null,
        setting_auto_delete := setting_auto_delete,
        ids := ids,
        workspace_slug := workspace_slug,
		query_limit := query_limit
	) as application
)
, applied_forms_to_delete as (
	select da.id as applied_form_id, da.slug as applied_form_slug
  	from dynamicform_appliedform da 
  	left join dynamicform_applicationappliedform da2 on da.id = da2.applied_form_id 
  	where da2.application_id in (select application_id from applications_to_delete)
)
, answers_to_delete as (
	select answers.id as answer_id
	from dynamicform_answer answers
	where answers.applied_form_id in (
		select applied_form_id 
		from applied_forms_to_delete
	)
)
, answer_files_to_delete as (
	select file.id as file_id
	from dynamicform_answerfile file 
	left join dynamicform_answer answers on answers.id = file.answer_id 
	where answers.id in (
		select answer_id
		from answers_to_delete
	)
)
, delete_ekyc as (
	delete from ekyc_ekyc e 
  	where e.ref in (
		select applied_form_slug 
		from applied_forms_to_delete
  	)
)
, delete_otp_log as (
	delete from otp_mobileotpverificationslog otp_log
  	where otp_log.reference_code in (
		select reference_code 
		from otp_mobileotpverifications otp
  		where otp.ref in (
			select applied_form_slug 
			from applied_forms_to_delete
	  	)
  	)
)
, delete_otp as (
	delete from otp_mobileotpverifications otp
  	where otp.ref in (
			select applied_form_slug 
			from applied_forms_to_delete
	  	)
)
, delete_bankstatement as (
	delete from bankstatement_bankstatement bs
  	where bs.ref in (
		select applied_form_slug 
		from applied_forms_to_delete
  	)
)
, delete_utility_bills as (
	delete from utility_bill_utilitybill ubu 
	where ubu.id in (
		select dau.utility_bill_id 
		from dynamicform_appliedformutilitybill dau
		where dau.applied_form_id in (
			select applied_form_id 
			from applied_forms_to_delete
		)
	)
)
, mark_answer_file_to_delete as (
	update dynamicform_answerfile file
  	set answer_id = null,
  		deleted_at = now() 
	where file.id in (
		select file_id
		from answer_files_to_delete
	)
)
, delete_answers as (
	delete from dynamicform_answer answers
	where answers.id in (
		select answer_id 
		from answers_to_delete
	)
)
, delete_application_partition as (
	delete from dynamicform_partitionapplication partition_application
	where partition_application.application_id in (
		select application_id
		from applications_to_delete
	)
)
, delete_answer_partition as (
	delete from dynamicform_partitionanswer partition_answer
	where partition_answer.application_id in (
		select application_id
		from applications_to_delete
	)
)
, mark_applied_form_to_delete as (
	update dynamicform_appliedform da 
  	set deleted_at = now()
  	where da.id in (
		select applied_form_id 
		from applied_forms_to_delete
  	)
)
, mark_application_to_delete as (
	update dynamicform_application application
  	set deleted_at = now() 
	where application.id in (
		select application_id
		from applications_to_delete
	)
)
select application_id as id
from applications_to_delete
;

end
$func$;


create or replace function soft_delete_applications(field varchar, submitted_null bool, setting_auto_delete varchar, ids integer[], workspace_slug varchar, select_limit integer)
returns table (
	id int
)
language plpgsql
as $func$ 
begin
RETURN QUERY

with 
applications_to_delete as (
	select application.id as application_id
	from get_application_to_delete(
	    field := field,
        submitted_null := submitted_null,
        setting_auto_delete := setting_auto_delete,
        ids := ids,
        workspace_slug := workspace_slug,
		select_limit := select_limit
	) as application
)
, applied_forms_to_delete as (
	select da.id as applied_form_id, da.slug as applied_form_slug
  	from dynamicform_appliedform da 
  	left join dynamicform_applicationappliedform da2 on da.id = da2.applied_form_id 
  	where da2.application_id in (select application_id from applications_to_delete)
)
, delete_application_partition as (
	delete from dynamicform_partitionapplication partition_application
	where partition_application.application_id in (
		select application_id
		from applications_to_delete
	)
)
, delete_answer_partition as (
	delete from dynamicform_partitionanswer partition_answer
	where partition_answer.application_id in (
		select application_id
		from applications_to_delete
	)
)
, mark_applied_form_to_delete as (
	update dynamicform_appliedform da 
  	set deleted_at = now()
  	where da.id in (
		select applied_form_id 
		from applied_forms_to_delete
  	)
)
, mark_application_to_delete as (
	update dynamicform_application application
  	set deleted_at = now() 
	where application.id in (
		select application_id
		from applications_to_delete
	)
)
select application_id as id
from applications_to_delete
;

end
$func$;