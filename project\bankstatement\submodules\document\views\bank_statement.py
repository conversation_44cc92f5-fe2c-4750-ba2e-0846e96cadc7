import logging
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound, PermissionDenied
from rest_framework.response import Response
from .v1.bank_statement import BankStatementViewSet as BankStatementViewSetV1
from .v2.bank_statement import BankStatementViewSet as BankStatementViewSetV2
from ....helpers.get_form import get_applied_form

VIEW_VERSIONS = {
    "latest": BankStatementViewSetV2,
    "1": BankStatementViewSetV1,
    "2": BankStatementViewSetV2,
}

from ..models.bank_statement import (
    BankStatement,
)

logger: logging.Logger = logging.getLogger(__name__)


class BankStatementViewSet(BankStatementViewSetV1):
    def get_applied_form_and_view_class(self, ref=None, applied_form=None, method=None, document=None):
        if not applied_form:
            if not ref:
                raise NotFound()
            applied_form = get_applied_form(ref)
        if not applied_form:
            raise NotFound()

        if document:
            view_version = document.bank_statement.version
        else:
            view_version = str(applied_form.get_form_settings("bank_statement.version", "latest"))

        viewset = VIEW_VERSIONS.get(view_version, BankStatementViewSetV2)

        if method and not hasattr(viewset, method):
            raise NotFound()

        view_class = viewset()
        view_class.request = self.request
        view_class.kwargs = self.kwargs

        logger.info(
            f"=== BANKSTATEMENT API ref:{applied_form.slug} version:{view_class.version} request_version:{view_version}"
        )
        return applied_form, view_class

    @action(detail=True, methods=["post"], url_path="flag/task")
    def flag_task(self, request, ref, *args, **kwargs):
        applied_form, view_class = self.get_applied_form_and_view_class(ref=ref)
        return view_class.flag_task(request, ref, *args, **kwargs)

    @action(detail=True, methods=["post"], url_path="flag/run")
    def flag_run(self, request, ref, *args, **kwargs):
        applied_form, view_class = self.get_applied_form_and_view_class(ref=ref)
        return view_class.flag_run(request, ref, *args, **kwargs)

    @action(detail=True, methods=["get"], url_path="flag/status")
    def flag_status(self, request, ref, *args, **kwargs):
        applied_form, view_class = self.get_applied_form_and_view_class(ref=ref)
        return view_class.flag_status(request, ref, *args, **kwargs)
    
    @action(detail=False, methods=['get'], url_path='support-list')
    def support_bank_list(self, request, *args, **kwargs):
        from bankstatement.submodules.document.external_api_v2 import get_bank_list
        language = request.headers.get('Accept-Language', 'th')
        country_code = request.GET.get('country_code', 'TH')
        is_corporate_statement = request.GET.get('is_corporate_statement', 0)
        is_corporate_statement = bool(is_corporate_statement)

        if str(language).upper() not in settings.BANK_STATEMENT_GATEWAY_ACCEPT_LANGUAGE:
            language = "EN"

        result, status_code = get_bank_list(
            country_code=country_code,
            language=language,
            is_corporate_statement=is_corporate_statement
        )

        return Response(result, status=status_code)
    
    @action(detail=False, methods=['get'], url_path='flag-list')
    def flag_list(self, request, *args, **kwargs):
        from bankstatement.submodules.document.external_api_v2 import get_flag_list
        
        result, status_code = get_flag_list()

        return Response(result, status=status_code)

    @action(detail=True, methods=["get"])
    def summary_xlxs(self, request, applied_form=None, *args, **kwargs):
        from io import BytesIO as IO
        from django.http import HttpResponse
        import xlsxwriter

        bank_statement: BankStatement = self.get_object()
        if not bank_statement.applied_form:
            raise NotFound()
        if not bank_statement.applied_form.can_view_report():
            raise PermissionDenied()

        query = dict(request.GET)
        query["size"] = "-1"
        
        excel_file = IO()
        workbook = xlsxwriter.Workbook(excel_file)
        bank_statement.workbook_write_account_tabs(workbook, query=query)
        bank_statement.workbook_write_summary_tab_data(workbook)
        workbook.close()
        excel_file.seek(0)

        response = HttpResponse(excel_file.read(),
                                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

        filename = f"Bankstatement_{bank_statement.applied_form.slug}"
        response['Content-Disposition'] = f'attachment; filename="{filename}.xlsx"'
        return response