from decision_flow.action.base import BaseAction
from rest_framework.serializers import ValidationError
from decision_flow.exceptions import DecisionFlowException
import json
from pydash.objects import get

class TriggerWebhook(BaseAction):

    def __str__(self):
        return 'Trigger Webhook'

    def validate_param(self, data):
        form_webhook_id = data.get('form_webhook_id')
        collect_result = data.get('collect_result')
        param_data = data.get('data')

        if not form_webhook_id or type(form_webhook_id) is not int:
            raise ValidationError('`form_webhook_id` must be an id')

        if collect_result and type(collect_result) is not bool:
            raise ValidationError('`collect_result` must be a boolean')

        if param_data and type(param_data) is not dict:
            raise ValidationError('`data` must be a dict')

        return data

    def do_action(self, data):
        context = self.context
        applied_form = context.get('applied_form')
        if not applied_form:
            return

        decision_flow = context.get("decision_flow")
        decision_flow_event = None
        if decision_flow:
            decision_flow_event = decision_flow.event
        
        param = self.initial_data.get('param', {})
        task = applied_form.trigger_webhook(decision_flow_event=decision_flow_event,**param)
        if task is None:
            raise DecisionFlowException("Webhook not found")
        # webhook name
        # url
        # request body
        # response
        # response status code
        attr = task.method
        try:
            response_body = json.loads(task.response)
        except:
            response_body = task.response

        return {
            'webhook': {
                'name': task.webhook.name,
                'url': attr.get('url', None),
            },
            'task': {
                'id': task.id,
                'request_body': task.body,
                'response_body': response_body,
                'response_status_code': task.status_code,
            }
        }

    def clone(self):
        """
        self.clone_from_data := {
            "webhooks": {"clone_from_id": "clone_to_id"},
            "custom_status_keys": {"clone_from_id": "clone_to_id"},
            "custom_status_values": {"clone_from_id": "clone_to_id"},
            "integrations": {"clone_from_id": "clone_to_id"},
        }
        self.root_log_path := path.to.root.item
        self.clone_frontend_schema := {frontend schema to override cloned data}
        """
        from_form_webhook_id = get(self.initial_data, "param.form_webhook_id", None)
        to_form_webhook_id = get(self.clone_from_data, ["webhooks", from_form_webhook_id])
        self.set_clone(self.root_log_path, "param.form_webhook_id", to_form_webhook_id)
