import logging
from django.conf import settings
from pydash import get

from rest_framework import status
from data_point.services.base import BaseService
from data_point.exceptions import DataPointException, DataPointResponseException
from data_point.utils import ErrorMessage

logger: logging.Logger = logging.getLogger(__name__)

class AsiaVerifyApi(BaseService):
    DATA_POINT_ASIA_VERIFY_HOST = settings.DATA_POINT_ASIA_VERIFY_HOST
    DATA_POINT_ASIA_VERIFY_AUTH = settings.DATA_POINT_ASIA_VERIFY_AUTH
    DATA_POINT_ASIA_VERIFY_SIGN = settings.DATA_POINT_ASIA_VERIFY_SIGN
    
    def search_asia_verify_v1(self, body: dict):
        '''
        body = {
        #     "connectType": "Sync",
        #     "template": "kybStandard",
        #     "language": "EN",
        #     "country": str(country).upper(),
        # }
        '''
        # Setup URL, header and request body
        url = f'{self.DATA_POINT_ASIA_VERIFY_HOST}/v1/open/api/package/1.0'
        headers = {
            'Authorization': f'{self.DATA_POINT_ASIA_VERIFY_AUTH}',
            'Sign': f'{self.DATA_POINT_ASIA_VERIFY_SIGN}',
        }
        
        # Logging when request API
        logging_context = self.get_logging_context()
        data_point_log_info = {
            'url': url,
            'json': body,
            'context': logging_context,
        }
        logger.info(f'Asia verify: API Request process', data_point_log_info)
        
        # Request API
        result = self._request(
            method='POST',
            url=url,
            json=body,
            headers=headers,
        )
        
        # Get result from request
        response_data = result.get('result')
        status_code = result.get('status_code')
        response_code = get(response_data, 'code')
        
        # Logging after got response
        data_point_log_info["status_code"] = status_code
        data_point_log_info["code"] = response_code
        logger.info(
            f'Asia verify: Request success: HTTP Response {status_code}',
            data_point_log_info
        )
        
        # Raise DataPointResponseException when status is not 200
        if not status.is_success(status_code):
            raise DataPointResponseException(
                f"{ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'Asia verify'})}: {status_code}",
                data_point_log_info=data_point_log_info
            )

        # Raise DataPointResponseException if no data is found in the response.
        elif get(response_data, 'result.data') is None:
            # Get company_id by number or codeField(MYS)
            number = body.get('number')
            code_field = body.get('codeField')
            error_infomation = {
                "country": body.get('country'),
                "number": number or code_field,
            }
            raise DataPointResponseException(
                f"{ErrorMessage.NO_DATA_RESPONSE.value.format(**{'data_name': 'Company Information'})}: {error_infomation}",
                data_point_log_info=data_point_log_info
            )
        return response_data

    def search_asia_verify_webhook_response_v1(self, body: dict):
        from crequest.middleware import CrequestMiddleware
        from django.core.handlers.wsgi import WSGIRequest
        
        # Setup host to callback
        request: WSGIRequest = CrequestMiddleware.get_request()
        request_host = request.get_host()
        request_scheme = request.scheme
        callback_url = f'{request_scheme}://{request_host}/api/asiaverify/webhook.site/callback/'

        # Setup URL, header and request body
        url = f'{self.DATA_POINT_ASIA_VERIFY_HOST}/v1/open/api/package/2.0'
        headers = {
            'Authorization': f'{self.DATA_POINT_ASIA_VERIFY_AUTH}',
            'Sign': f'{self.DATA_POINT_ASIA_VERIFY_SIGN}',
        }
        body['notifyUrl'] = callback_url
        
        # Logging when request API
        logging_context = self.get_logging_context()
        data_point_log_info = {
            'url': url,
            'json': body,
            'context': logging_context,
        }
        logger.info(f'Asia verify: API Request process', data_point_log_info)
        
        # Request API
        result = self._request(
            method='POST',
            url=url,
            json=body,
            headers=headers,
        )
            
        response_data = result.get('result')
        status_code = result.get('status_code')
        response_code = get(response_data, 'code')
        
        # Logging after got response
        data_point_log_info["status_code"] = status_code
        data_point_log_info["code"] = response_code
        logger.info(
            f'Asia verify: Request success: HTTP Response {status_code}',
            data_point_log_info
        )
        
        # Raise DataPointResponseException when status is not 200
        if not status.is_success(status_code):
            raise DataPointResponseException(
                f" {ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'Asia verify'})}: {status_code}",
                data_point_log_info=data_point_log_info
            )
        
        # Raise DataPointResponseException if no order_no (HKG) is found in the response.
        elif get(response_data, 'result.orderNo') is None:
            # Get company_id by number or codeField(MYS)
            number = body.get('number')
            code_field = body.get('codeField')
            error_infomation = {
                "country": body.get('country'),
                "number": number or code_field,
            }
            raise DataPointResponseException(
                f"{ErrorMessage.NO_DATA_RESPONSE.value.format(**{'data_name': 'Company Information'})}: {error_infomation}",
                data_point_log_info=data_point_log_info
            )
        # Get result from database (response will get from webhook and store in db by /webhook.site/callback/ path)
        else :
            asia_verify_webhook_result = None
            if body['country'] == "HKG":
                asia_verify_webhook_result = self.get_async_asia_verify(get(response_data, 'result.orderNo'))
                # Raise an exception if no data (HKG) is found in the response.
                if not get(asia_verify_webhook_result, 'result.data', {}):
                    # Get company_id by number or codeField(HKG)
                    number = body.get('number')
                    code_field = body.get('codeField')
                    error_infomation = {
                        "country": body.get('country'),
                        "number": number or code_field,
                    }
                    raise DataPointResponseException(
                        f"{ErrorMessage.NO_DATA_RESPONSE.value.format(**{'data_name': 'Company Information'})}: {error_infomation}",
                        data_point_log_info=data_point_log_info
                    )
            return asia_verify_webhook_result
    
    def get_async_asia_verify(self, order_no: str) -> dict|None:
        import time
        from asiaverify.models import AsyncAsiaVerify
        
        start_time = time.time()
        timeout = 10

        # Loop until timeout for get hongkong asia verify result
        while time.time() - start_time < timeout:
            try:
                instance = AsyncAsiaVerify.objects.filter(order_no=order_no)
                if instance.exists():
                    result = instance.first().result
                    return result

            except Exception as e:
                print(f"An error occurred: {e}")
            time.sleep(0.5)  # Wait before trying again

        raise DataPointException(detail=f"Timeout: No data received from the webhook: order_no: {order_no}")