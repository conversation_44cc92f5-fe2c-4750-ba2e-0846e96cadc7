from .util import check_pdf_pages

ACTION_TRIGGER_SMART_UPLOADER_GET_RESULTS = "trigger_smart_uploader_get_results"

FEATURE_SMART_UPLOADER = "smart_uploader"


class SmartUploader:
    def trigger_smart_uploader_get_results(self):
        from smartuploader.models.smartuploader import SmartUploader as SmartUploaderModel, STATUS_COMPLETE
        from dynamicform.submodules.appliedform.models.appliedform_smartuploader import AppliedFormSmartUploader
        from dynamicform.submodules.form.models import Form
        from .models.appliedform import AppliedForm

        applied_form: AppliedForm = self
        form: Form = applied_form.form
        backend_schema: dict = form.backend_schema
        input_smart_uploaders: dict = backend_schema.get("input_smart_uploaders", {})
        items_dict: dict = backend_schema.get("items")
        questions = input_smart_uploaders.keys()

        triggered_smart_uploader: list[SmartUploaderModel] = []

        try:
            applied_form_smart_uploaders: list[AppliedFormSmartUploader] = (
                AppliedFormSmartUploader.objects.filter(applied_form=applied_form, answer__question__in=questions)
                .order_by("created_at")
                .all()
            )
            for applied_form_smart_uploader in applied_form_smart_uploaders:
                answer_files = applied_form_smart_uploader.answer_files

                if len(answer_files) == 0:
                    continue
                if applied_form_smart_uploader.smart_uploader:
                    continue

                item_schema = items_dict.get(applied_form_smart_uploader.answer.question)

                match_type = item_schema.get("match_type", "any")
                match_keywords = item_schema.get("match_keywords", [])
                match_level = item_schema.get("match_level", 100)
                extract_verify_fields = item_schema.get("extract_verify_fields", [])
                extract_verify_tables = item_schema.get("extract_verify_tables", [])
                custom_fields_text_validation = {
                    "type": match_type,
                    "keyword_list": match_keywords,
                    "match_level": match_level,
                }
                custom_fields = extract_verify_fields + extract_verify_tables

                smart_uploader = SmartUploaderModel()
                applied_form_smart_uploader.smart_uploader = smart_uploader

                # Calculate credit to spend / files to process
                max_page_count: int = item_schema.get("configs", {}).get("max_page_count", 1)
                total_pages = 0
                processable_files = []
                for file in answer_files:
                    current_pages = check_pdf_pages(file.detail)
                    after_pages = total_pages + current_pages
                    if after_pages <= max_page_count:
                        total_pages = after_pages
                        processable_files += [(file.detail.name, file.detail.read(), file.mime_type)]
                applied_form_smart_uploader.page_count = total_pages

                # Check credit
                can_deduct_credit = True
                try:
                    applied_form.can_deduct_credit(feature=FEATURE_SMART_UPLOADER, multiplier=total_pages)
                except Exception:
                    can_deduct_credit = False

                # If has fields + credit = Actually using ocr feature
                has_custom_fields = len(custom_fields) > 0
                if can_deduct_credit and has_custom_fields:
                    smart_uploader.request_result(
                        files=processable_files,
                        custom_fields_text_validation=custom_fields_text_validation,
                        custom_fields=custom_fields,
                    )
                    applied_form.deduct_credit(feature=FEATURE_SMART_UPLOADER, multiplier=total_pages)
                # If no field = force complete with empty params
                else:
                    smart_uploader.request_result(
                        files=[],
                        custom_fields_text_validation={},
                        custom_fields=[],
                    )

                applied_form_smart_uploader.save()

                applied_form.save_log(
                    action=ACTION_TRIGGER_SMART_UPLOADER_GET_RESULTS,
                    detail={
                        "question": applied_form_smart_uploader.answer.question,
                        "total_pages": total_pages,
                        "can_deduct_credit": can_deduct_credit,
                        "has_custom_fields": has_custom_fields,
                    },
                    is_fail=False,
                )

                triggered_smart_uploader.append(smart_uploader)

            return triggered_smart_uploader

        except Exception as e:
            print(ACTION_TRIGGER_SMART_UPLOADER_GET_RESULTS, e)
            import traceback

            print(traceback.format_exc())
            applied_form.save_log(
                action=ACTION_TRIGGER_SMART_UPLOADER_GET_RESULTS,
                detail=str(e),
                is_fail=True,
            )

    def smart_uploader_auto_save_answers(self, smart_uploader=None):
        from smartuploader.models.smartuploader import SmartUploader as SmartUploaderModel
        from dynamicform.submodules.appliedform.dynamicform.dynamicform import Dynamicform
        from ..appliedform.models.appliedform_smartuploader import AppliedFormSmartUploader

        if not smart_uploader:
            return

        smart_uploader: SmartUploaderModel = smart_uploader  # NOSONAR
        smart_uploader_answers = {}

        if not smart_uploader.raw_results:
            return

        fields = smart_uploader.raw_results.get("result", {}).get("fields", {})
        if not fields or not isinstance(fields, dict):
            return

        answer_smart_uploader = (
            AppliedFormSmartUploader.objects.filter(applied_form_id=self.id, smart_uploader_id=smart_uploader.id)
            .select_related("answer")
            .first()
        )
        if not answer_smart_uploader:
            return

        dynamicform = Dynamicform(applied_form=self, form=self.form)

        question_name = answer_smart_uploader.answer.question
        item_schema = dynamicform.backend_schema_items.get(question_name)
        if not item_schema:
            return

        auto_save_answers = item_schema.get("auto_save_answers")
        if not auto_save_answers and not isinstance(auto_save_answers, dict):
            return

        smart_uploader_answers = self.smart_uploader_get_answers_to_save(
            backend_item_schema_auto_save_answers=auto_save_answers, fields=fields
        )

        dynamicform.save_answers(smart_uploader_answers)

    def smart_uploader_get_answers_to_save(
        self,
        backend_item_schema_auto_save_answers: dict,
        fields: dict,
    ):

        answers_to_save = {}

        for field_name, field_info in fields.items():
            value = field_info.get("value")
            item_field_name = backend_item_schema_auto_save_answers.get(field_name)
            if not item_field_name:
                continue
            answers_to_save.update({item_field_name: value})

        return answers_to_save

    def smart_uploader_trigger_decision_flow(self):
        from ..appliedform.models.appliedform_smartuploader import AppliedFormSmartUploader

        found_any_incompleted = (
            AppliedFormSmartUploader.objects.filter(applied_form_id=self.id)
            .exclude(smart_uploader__status="complete")
            .select_related("smart_uploader")
            .exists()
        )

        if found_any_incompleted:
            return

        self.trigger_decision_flow_events(events=["onSmartUploaderComplete"])
