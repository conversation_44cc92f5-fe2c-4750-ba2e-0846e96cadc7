from unittest import mock
from django.test import TestCase
from rest_framework.reverse import reverse
from django.conf import settings

mock_get_bank_list = mock.Mock(side_effect=lambda country_code, language, is_corporate_statement=False: ({}, 200))

# Create your tests here.
class BankStatementSupportBankListTests(TestCase):
    @classmethod
    def setUpTestData(cls):
        pass

    @classmethod
    def setUp(cls):
        settings.BANK_STATEMENT_GATEWAY_ACCEPT_LANGUAGE =  ['EN', 'TH']
        pass

    def do_support_bank_list(self, headers = {}, data = {}):
        response = self.client.get(
            reverse("bankstatement:bankstatement-support-bank-list"),
            data=data,
            headers=headers
        )
        return response

    def test_get_support_list_default(self):
        with mock.patch("bankstatement.submodules.document.external_api_v2.get_bank_list", mock_get_bank_list) as get_bank_list:
            get_bank_list.reset_mock()
            self.do_support_bank_list()
            get_bank_list.assert_called_once_with(
                country_code = 'TH',
                language = 'th',
                is_corporate_statement = False)
    
    def test_get_support_list_with_params(self):
        with mock.patch("bankstatement.submodules.document.external_api_v2.get_bank_list", mock_get_bank_list) as get_bank_list:
            get_bank_list.reset_mock()
            mock_headers = {
                'Accept-Language': 'en'
            }
            mock_data = {
                'country_code': 'PH',
                'is_corporate_statement': 1
            }
            self.do_support_bank_list(headers=mock_headers, data=mock_data)
            get_bank_list.assert_called_once_with(
                country_code = 'PH',
                language = 'en',
                is_corporate_statement = True)
            
            
    def test_get_support_list_with_invalid_lang_params(self):
        with mock.patch("bankstatement.submodules.document.external_api_v2.get_bank_list", mock_get_bank_list) as get_bank_list:
            get_bank_list.reset_mock()
            mock_headers = {
                'Accept-Language': '??'
            }
            mock_data = {
                'country_code': 'PH',
                'is_corporate_statement': 1
            }
            self.do_support_bank_list(headers=mock_headers, data=mock_data)
            get_bank_list.assert_called_once_with(
                country_code = 'PH',
                language = 'EN',
                is_corporate_statement = True)
            
            
        
