import json
import re

from django.db import models
from django.utils.translation import get_language
from dynamicform.models import (
    CustomStatusKey,
    CustomStatusValue,
    Form,
    Page,
    FormTask,
    FormUsers,
    FormWebhook,
    SchemaHistory,
)
from dynamicform.util import current_user
from rest_framework import serializers
from rest_framework.reverse import reverse
from webhook.serializer.webhook import WebhookSerializer
from pydash import get

REGEX_VALIDATE_FORM_NAME_TEMPLATE = r"^[A-Za-z0-9_ -]{1,20}$"
REGEX_VALIDATE_ANSWER_NAME_TEMPLATE = r"^[a-zA-Z0-9_]{1,191}$"
VALIDATE_FORM_NAME_ERROR_MESSAGE = "Please use (A-Z), (0-9), (_), (-) or Spaces ( )"

DYNAMICFORM_BUILDER_DETAIL_PATH = "dynamicform:builder-detail"


class FormListSerializer(serializers.ModelSerializer):
    primary_color = serializers.SerializerMethodField()

    class Meta:
        model = Form
        fields = [
            "slug",
            "name",
            "log_answers",
            "can_update_after_submit",
            "updated_at",
            "created_at",
            "is_active",
            "primary_color",
        ]

    def to_representation(self, form):
        data = super(FormListSerializer, self).to_representation(form)
        user_highlight_form_ids = self.context.get("user_highlight_form_ids", [])

        data["highlight"] = False
        if form.id in user_highlight_form_ids:
            data["highlight"] = True

        return data

    def get_primary_color(self, obj):
        path = "schema_config.styling.--app-primary-color"

        return obj.get_settings(path, default={}, secure=True)


class FormSerializer(serializers.ModelSerializer):
    frontend_schema = serializers.JSONField()
    locale = serializers.SerializerMethodField()
    report_schema = serializers.JSONField(required=False)
    slug = serializers.SlugField(required=False, max_length=60)
    changed_key = serializers.DictField(required=False)

    class Meta:
        model = Form
        fields = [
            "id",
            "slug",
            "name",
            "frontend_schema",
            "report_schema",
            "locale",
            "log_answers",
            "can_update_after_submit",
            "is_active",
            "changed_key",
        ]
        extra_kwargs = {
            "frontend_schema": {"lookup_field": "_frontend_schema"},
            "report_schema": {"lookup_field": "_report_schema"},
        }

    def validate_frontend_schema(self, data):
        if "steps" not in data:
            raise serializers.ValidationError({"frontend_schema": "key steps is missing"})

        frontend_schema = data
        if type(frontend_schema) == str:
            frontend_schema = json.loads(frontend_schema)

        keep_answers = frontend_schema.get("keep_answers", None)
        if keep_answers:
            for key, _ in keep_answers.items():
                is_valid_answer_name = re.match(REGEX_VALIDATE_ANSWER_NAME_TEMPLATE, key)
                if not is_valid_answer_name:
                    raise serializers.ValidationError({"keep_answers": f'"{key}" has an invalid key name.'})

        return data

    def validate_name(self, data):
        is_valid_form_name = re.match(REGEX_VALIDATE_FORM_NAME_TEMPLATE, data)
        if not is_valid_form_name:
            raise serializers.ValidationError(VALIDATE_FORM_NAME_ERROR_MESSAGE)

        return data

    def validate_slug(self, data):
        is_valid_form_name = re.match(r"^[A-Za-z0-9_ -]{1,60}$", data)
        if not is_valid_form_name:
            raise serializers.ValidationError(VALIDATE_FORM_NAME_ERROR_MESSAGE)

        return data

    def create(self, validated_data):
        frontend_schema = validated_data.pop("frontend_schema")
        if type(frontend_schema) == str:
            frontend_schema = json.loads(frontend_schema)

        validated_data["frontend_schema"] = frontend_schema

        form = Form.objects.create(**validated_data)

        # update lang
        locale = self.initial_data.get("locale", get_language())
        form.update_lang(locale)

        # init default page
        pages = form.init_default_page()

        # user
        user = current_user()
        if not user:
            user = None

        version_sha = None
        branch_name = None
        if user and user.is_superuser:
            response_new_commit = Form.commit_save(
                form_slug=form.slug,
                form_locale=locale,
                frontend_schema=json.dumps(form.frontend_schema),
                report_schema=json.dumps(form.report_schema),
                commit_message=f"init: create new form {form.slug}",
                pages=pages,
            )

            version_sha = response_new_commit.get("short_id") if response_new_commit else None
            branch_name = response_new_commit.get("branch_name") if response_new_commit else None

        # History
        history = SchemaHistory(
            form=form,
            schema=frontend_schema,
            user=user,
            version_sha=version_sha,
            branch_name=branch_name,
        )
        history.save()
        return form

    def update(self, form: Form, validated_data):
        form.name = validated_data.get("name", form.name)
        form.log_answers = validated_data.get("log_answers", form.log_answers)
        form.can_update_after_submit = validated_data.get("can_update_after_submit", form.can_update_after_submit)

        frontend_schema = validated_data.get("frontend_schema")
        if frontend_schema:
            if type(frontend_schema) == str:
                frontend_schema = json.loads(frontend_schema)
            form.frontend_schema = frontend_schema

        report_schema = validated_data.get("report_schema")
        if report_schema:
            if type(report_schema) == str:
                report_schema = json.loads(report_schema)
            form.report_schema = report_schema

        for attr, value in validated_data.items():
            if attr in ["frontend_schema", "report_schema"]:
                continue
            if not hasattr(form, attr):
                continue
            setattr(form, attr, value)

        form.save()

        # Apply Changed Keys (to keep its translation)
        changed_key: dict[str, str] = validated_data.get("changed_key")
        if changed_key:
            for old_key, new_key in changed_key.items():
                form.change_answer_key(old_key, new_key)

        if frontend_schema:
            # Update translations
            locale = self.initial_data.get("locale", get_language())
            form.update_lang(locale)

            # Save schema history
            user = current_user()
            if not user:
                user = None
            history = SchemaHistory(form=form, schema=frontend_schema, user=user)
            history.save()

        return form

    def to_representation(self, obj):
        return {
            "slug": obj.slug,
            "name": obj.name,
            "frontend_schema": obj.trans_frontend_schema,
            "report_schema": obj.report_schema,
            "updated_at": obj.updated_at,
            "log_answers": obj.log_answers,
            "can_update_after_submit": obj.can_update_after_submit,
            "is_active": obj.is_active,
            "pages": obj.pages_list,
            "schema_config": obj.get_settings(path="schema_config"),
            "util_base_url": (reverse(DYNAMICFORM_BUILDER_DETAIL_PATH, args=[obj.slug]) if obj else None),
        }


class FormCommitSerializer(serializers.ModelSerializer):
    frontend_schema = serializers.JSONField()
    locale = serializers.SerializerMethodField()
    commit_message = serializers.SerializerMethodField()
    report_schema = serializers.JSONField(required=False)
    slug = serializers.SlugField(required=False)

    class Meta:
        model = Form
        fields = [
            "id",
            "slug",
            "name",
            "frontend_schema",
            "report_schema",
            "locale",
            "log_answers",
            "can_update_after_submit",
            "is_active",
            "commit_message",
        ]
        extra_kwargs = {
            "frontend_schema": {"lookup_field": "_frontend_schema"},
            "report_schema": {"lookup_field": "_report_schema"},
        }

    def validate_frontend_schema(self, data):
        if "steps" not in data:
            raise serializers.ValidationError({"frontend_schema": "key steps is missing"})

        frontend_schema = data
        if type(frontend_schema) == str:
            frontend_schema = json.loads(frontend_schema)

        keep_answers = frontend_schema.get("keep_answers", None)
        if keep_answers:
            for key, _ in keep_answers.items():
                is_valid_answer_name = re.match(REGEX_VALIDATE_ANSWER_NAME_TEMPLATE, key)
                if not is_valid_answer_name:
                    raise serializers.ValidationError({"keep_answers": f'"{key}" has an invalid key name.'})

        return data

    def validate_name(self, data):
        is_valid_form_name = re.match(REGEX_VALIDATE_FORM_NAME_TEMPLATE, data)
        if not is_valid_form_name:
            raise serializers.ValidationError(VALIDATE_FORM_NAME_ERROR_MESSAGE)

        frontend_schema = data
        if type(frontend_schema) == str:
            frontend_schema = json.loads(frontend_schema)

        keep_answers = frontend_schema.get("keep_answers", None)
        if keep_answers:
            for key, _ in keep_answers.items():
                is_valid_answer_name = re.match(REGEX_VALIDATE_ANSWER_NAME_TEMPLATE, key)
                if not is_valid_answer_name:
                    raise serializers.ValidationError({"keep_answers": f'"{key}" has an invalid key name.'})

        return data

    def validate_name(self, data):
        is_valid_form_name = re.match(REGEX_VALIDATE_FORM_NAME_TEMPLATE, data)
        if not is_valid_form_name:
            raise serializers.ValidationError(VALIDATE_FORM_NAME_ERROR_MESSAGE)

        return data

    def update_with_commit(self, form, version_sha=None, branch_name=None):
        validated_data = self.validated_data
        form.name = validated_data.get("name", form.name)
        form.log_answers = validated_data.get("log_answers", form.log_answers)
        form.can_update_after_submit = validated_data.get("can_update_after_submit", form.can_update_after_submit)

        frontend_schema = validated_data.get("frontend_schema")
        if frontend_schema:
            if type(frontend_schema) == str:
                frontend_schema = json.loads(frontend_schema)
            if version_sha:
                frontend_schema["version_sha"] = version_sha
            form.frontend_schema = frontend_schema

        report_schema = validated_data.get("report_schema")
        if report_schema:
            if type(report_schema) == str:
                report_schema = json.loads(report_schema)
            if version_sha:
                report_schema["version_sha"] = version_sha
            form.report_schema = report_schema

        for attr, value in validated_data.items():
            if attr in ["frontend_schema", "report_schema"]:
                continue
            if not hasattr(form, attr):
                continue
            setattr(form, attr, value)

        form.save()

        if frontend_schema:
            locale = self.initial_data.get("locale", get_language())
            form.update_lang(locale)
            # History
            user = current_user()

            if not user:
                user = None
            branch = branch_name if version_sha else None
            history = SchemaHistory(
                form=form,
                schema=frontend_schema,
                user=user,
                version_sha=version_sha,
                branch_name=branch,
            )
            history.save()

        return form

    def to_representation(self, obj):
        return {
            "slug": obj.slug,
            "name": obj.name,
            "frontend_schema": obj.trans_frontend_schema,
            "report_schema": obj.report_schema,
            "updated_at": obj.updated_at,
            "log_answers": obj.log_answers,
            "can_update_after_submit": obj.can_update_after_submit,
            "is_active": obj.is_active,
            "pages": obj.pages_list,
            "schema_config": obj.get_settings(path="schema_config"),
            "util_base_url": (reverse(DYNAMICFORM_BUILDER_DETAIL_PATH, args=[obj.slug]) if obj else None),
        }


class FormCloneSerializer(serializers.ModelSerializer):
    frontend_schema = serializers.JSONField(required=False)
    locale = serializers.SerializerMethodField()
    report_schema = serializers.JSONField(required=False)
    slug = serializers.SlugField(required=False, allow_blank=True)

    class Meta:
        model = Form
        fields = [
            "id",
            "slug",
            "name",
            "frontend_schema",
            "report_schema",
            "locale",
            "log_answers",
            "can_update_after_submit",
            "is_active",
        ]
        extra_kwargs = {
            "frontend_schema": {"lookup_field": "_frontend_schema"},
            "report_schema": {"lookup_field": "_report_schema"},
        }

    def validate_name(self, data):
        is_valid_form_name = re.match(REGEX_VALIDATE_FORM_NAME_TEMPLATE, data)
        if not is_valid_form_name:
            raise serializers.ValidationError(VALIDATE_FORM_NAME_ERROR_MESSAGE)

        return data

    def to_representation(self, obj):
        return {
            "slug": obj.slug,
            "name": obj.name,
            "frontend_schema": obj.trans_frontend_schema,
            "report_schema": obj.report_schema,
            "updated_at": obj.updated_at,
            "log_answers": obj.log_answers,
            "can_update_after_submit": obj.can_update_after_submit,
            "is_active": obj.is_active,
            "pages": obj.pages_list,
            "schema_config": obj.get_settings(path="schema_config"),
            "util_base_url": reverse(DYNAMICFORM_BUILDER_DETAIL_PATH, args=[obj.slug]),
        }


class SchemaHistorySerializer(serializers.ModelSerializer):
    slug = serializers.SerializerMethodField()
    author = serializers.SerializerMethodField()

    def get_slug(self, obj):
        return obj.form.slug

    def get_author(self, obj):
        if obj.user:
            return f"{obj.user.first_name} {obj.user.last_name} ({obj.user.email})"
        else:
            return None

    class Meta:
        model = SchemaHistory
        fields = [
            "slug",
            "schema",
            "author",
            "branch_name",
            "version_sha",
            "created_at",
        ]


class FormWebhookListSerializer(serializers.ListSerializer):
    def to_representation(self, obj):
        iterable = obj.all() if isinstance(obj, models.Manager) else obj

        return [
            {
                "id": item.id,
                "form": item.form.slug,
                "enable": item.enable,
                "version": item.version,
                "created_at": item.created_at,
                "name": item.webhook.name,
                "event_trigger": item.webhook.event_trigger,
                "method": item.webhook.method,
                "description": item.webhook.description,
            }
            for item in iterable
        ]


class FormWebhookSerializer(serializers.ModelSerializer):
    webhook = serializers.JSONField()

    class Meta:
        model = FormWebhook
        fields = ["webhook", "version", "enable", "json_template"]
        list_serializer_class = FormWebhookListSerializer

    def to_representation(self, obj):
        webhook_detail = obj.webhook
        webhook_serializer = WebhookSerializer(instance=webhook_detail)
        return {
            "id": obj.id,
            "enable": obj.enable,
            "version": obj.version,
            "json_template": obj.json_template,
            "webhook": webhook_serializer.data,
        }


class FormTaskSerializer(serializers.ModelSerializer):
    webhook = serializers.JSONField()

    class Meta:
        model = FormTask
        fields = ["form", "task", "applied_form", "created_at", "webhook"]

    def to_representation(self, obj):
        application = obj.applied_form.application.slug if obj.applied_form else None
        method = obj.task.method_name
        return {
            "id": obj.id,
            "form": obj.form.slug,
            "application": application,
            "method": method,
            "status_code": obj.task.status_code,
            "created_at": obj.created_at,
        }


class FormTaskDetailSerializer(serializers.ModelSerializer):
    webhook = serializers.JSONField()

    class Meta:
        model = FormTask
        fields = [
            "form",
            "task",
            "applied_form",
            "created_at",
            "webhook",
            "body",
            "status_code",
            "response",
        ]

    def to_representation(self, obj):
        application = obj.applied_form.application.slug if obj.applied_form else None
        method = obj.task.method_name
        url = obj.task.url
        return {
            "id": obj.id,
            "form": obj.form.slug,
            "application": application,
            "method": method,
            "status_code": obj.task.status_code,
            "created_at": obj.created_at,
            "body": obj.task.body,
            "response": obj.task.response,
            "url": url,
        }


class FormUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = FormUsers
        fields = ["highlight"]


class CustomStatusKeySerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomStatusKey
        fields = "__all__"

    def validate_value(self, value):
        if re.search(r"[!@#$%^&*()+\s]+", value):
            raise serializers.ValidationError(f"Invalid value.")

        return value

    def to_representation(self, instance):
        if isinstance(instance, CustomStatusKey):
            custom_status_value = CustomStatusValue.objects.filter(custom_status_key=instance)
            custom_status_value_serializer = CustomStatusValueSerializer(
                custom_status_value,
                many=True,
            )
            result = {
                "id": instance.id,
                "value": instance.value,
                "icon": instance.icon,
                "description": instance.description,
                "priority": instance.priority,
                "options": custom_status_value_serializer.data,
            }
        else:
            result = {
                "id": instance["id"],
                "value": instance["value"],
                "icon": instance["icon"],
                "description": instance["description"],
                "priority": instance["priority"],
                "options": [],
            }

        return result

    def _is_deletable(self, raise_exception=True):
        custom_status_key = self.instance
        all_custom_status_values = custom_status_key.customstatusvalue_set.values_list("pk", flat=True)
        current_decision_flow_queryset = self.context.get("current_decision_flow_queryset", [])
        all_used_custom_status = []
        all_used_custom_status_keys = []

        for i in current_decision_flow_queryset:
            backend_schema = i.decision_flow.backend_schema

            custom_status = backend_schema.get("custom_status", [])
            all_used_custom_status.extend(custom_status)

            custom_status_keys = get(backend_schema, ['data_point', 'custom_status'], [])
            all_used_custom_status_keys.extend(custom_status_keys)

        if any(x in all_used_custom_status for x in all_custom_status_values) or (custom_status_key.id in all_used_custom_status_keys):
            if raise_exception:
                error_message = {
                    "value": [
                        "Can not delete used custom status",
                    ]
                }
                raise serializers.ValidationError(error_message)
            else:
                return False

        return True


class CustomStatusValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomStatusValue
        fields = "__all__"

    def validate_value(self, value):
        if re.search(r"[!@#$%^&*()+\s]+", value):
            raise serializers.ValidationError(f"Invalid value.")

        return value

    def to_representation(self, instance):
        if isinstance(instance, CustomStatusValue):
            result = {
                "id": instance.id,
                "value": instance.value,
                "color": instance.color,
                "priority": instance.priority,
            }
        else:
            result = {
                "id": instance["id"],
                "value": instance["value"],
                "color": instance["color"],
                "priority": instance["priority"],
            }

        return result

    def _is_deletable(self, raise_exception=True):
        custom_status_value = self.instance
        current_decision_flow_queryset = self.context.get("current_decision_flow_queryset", [])
        all_used_custom_status = []

        for i in current_decision_flow_queryset:
            backend_schema = i.decision_flow.backend_schema
            custom_status = backend_schema.get("custom_status", [])
            all_used_custom_status.extend(custom_status)

        if custom_status_value.pk in all_used_custom_status:
            if raise_exception:
                error_message = {
                    "value": [
                        "Can not delete used custom status",
                    ]
                }
                raise serializers.ValidationError(error_message)
            else:
                return False

        return True
