import logging
import re
import inspect
from data_point.base import DataPointBase
from data_point.exceptions import DataPointException, DataPointInputValidationError, DataPointResponseException, DataPointUnprocessableEntity
from data_point.fields.base import DataPoint<PERSON><PERSON><PERSON>ield
from django.conf import settings
from rest_framework import status
from rest_framework.exceptions import APIException
from data_point.utils import (
    InputValidationUtil, 
    ErrorMessage
)

logger: logging.Logger = logging.getLogger(__name__)
DATA_POINT_DAP_HOST = settings.DATA_POINT_DAP_HOST
DATA_POINT_DAP_USERNAME = settings.DATA_POINT_DAP_USERNAME
DATA_POINT_DAP_PASSWORD = settings.DATA_POINT_DAP_PASSWORD


DATAPOINT_OPTIONS = {
    'are_all_freeze_05': {
        'label': 'check if all the ID have been found under Freeze 05',
        'type': 'boolean',
    },
    'are_any_freeze_05': {
        'label': 'check if any of the ID have been found under Freeze 05',
        'type': 'boolean',
    },
    'are_all_hr_02': {
        'label': 'check if all have been found under HR02',
        'type': 'boolean',
    },
    'are_any_hr_02': {
        'label': 'check if any ID have been found under HR02',
        'type': 'boolean',
    },
    'are_all_hr_08_risk': {
        'label': 'check if all are at high risk',
        'type': 'boolean',
    },
    'are_any_hr_08_risk': {
        'label': 'check if anyone  is at high risk',
        'type': 'boolean',
    },
    'are_all_in_un_sanction': {
        'label': 'check if all name found in warning list',
        'type': 'boolean',
    },
    'are_any_in_un_sanction': {
        'label': 'check if any name found in warning list',
        'type': 'boolean',
    },
    'are_any_hits': {
        'label': 'check if any name found any warning list',
        'type': 'boolean',
    },
}


class AMLO(DataPointBase):
    national_id = DataPointJSONField(
        label='National ID',
        type='string',
        required=True,
        allowed_item_builder_types=[
            "short_long_answer" #  Allow only short_long_answer builder only
        ],
        
    )
    full_name = DataPointJSONField(
        label='Full Name',
        type='string',
        required=True,
        split_fields=True,
        allowed_item_builder_types=[
            "full_name" #  Allow only full_name builder only
        ],
    )

    name = 'amlo'
    title = 'AMLO'
    sub_title = 'Anti Money Laundering Office Thailand'
    description = 'Integration for AML/CFT activities with public and private organization or agencies in both Thailand and abroad'
    icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/icon_64/AMLO_64.png'
    option_icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/option-icon_36/AMLO_36.png'
    data_point_opitons = DATAPOINT_OPTIONS

    is_multiple_input = True
    is_multiple_input_one_list = True

    data_point_dap_host = {}
    data_point_dap_username = {}
    data_point_dap_password = {}

    # Variable for keep are../all... flagging in multi input case
    data_result_multiple_input_one_list = None
    
    def validate_national_id(self, value):
        self.is_valid_payload_connection(value, field_name='national_id')
        return value
    
    def validate_full_name(self, value):
        self.is_valid_payload_connection(value, field_name='full_name')
        return value

    def set_current_payload(self):
        self.set_payload('national_id')
        self.set_payload('full_name')
        self.set_payload('first_name')
        self.set_payload('middle_name')
        self.set_payload('last_name')
        return self.current_payload
    
    def get_data_result(self, **kwargs):
        national_id = self.get_payload('national_id')
        full_name = self.get_payload('full_name')

        # Get payload that split field from full_name
        first_name = self.get_payload('first_name') 
        middle_name = self.get_payload('middle_name') 
        last_name = self.get_payload('last_name')
        
        # Setup request_body for AMLO (amlo_person_screening) and (amlo_person_screening_un_list)
        amlo_data = {
            'id': national_id,  #  For Person-screening
            'cid': national_id, #  For Person-screening UN-LIST
            'name': first_name or '',
            'middle_name': middle_name or '',
            'surname': last_name or ''
        }
        result = {
            'is_freeze_05': False,
            'is_hr_02': False,
            'is_hr_08_risk': False,
            'is_in_un_sanction': False,
        }
        save_result = {
            'amlo_person_screening': {},
            'amlo_person_screening_un_list': {},
        }
        
        # Handle if every value is None or empty (see reference case in Clickup: 86cv5v5t6)
        # Do not do anything, just return None and skip validate
        if not national_id and not full_name :
            return None
        else :
            # Validate input before call API service
            is_input_valid, error_message = self.validate_input(
                first_name,
                last_name,
                middle_name,
                national_id
            )
        
        if not is_input_valid and not national_id:
            self.set_current_data_point_output(save_result)
            return self.invalid_input_error_result(error_message, DataPointInputValidationError)

        self.set_api_config()
        amlo_result = {}
        if is_input_valid:
            amlo_result = self.get_amlo_person_screening_result(amlo_data)
            save_result['amlo_person_screening'] = {
                'input': amlo_data,
                'output': amlo_result,
            }

            # Prepare error result and Return error result
            # if got error response in 200 status
            if type(amlo_result) is dict and 'errors' in amlo_result.keys():
                self.set_current_data_point_output(save_result)
                error_result = {"error": "national_id is invalid", 'exception_class': DataPointUnprocessableEntity}
                return error_result
        
        amlo_un_result = self.get_amlo_person_screening_un_list_result(amlo_data)
        save_result['amlo_person_screening_un_list'] = {
            'input': amlo_data,
            'output': amlo_un_result,
        }

        # Logic flagging must be (is_freeze_05, is_hr_02, is_hr_08_risk) for list_name
        # Update directly from AMLO API
        for i in amlo_result:
            if isinstance(i, dict):
                reason_name = i.get('list', "")
                
                # For FREEZE-04 keyword for config is not same as is_freeze_04
                # Change list_name to is_in_un_sanction if reason_name is FREEZE-04
                if reason_name == 'FREEZE-04':
                    list_name = 'is_in_un_sanction'
                else:
                    list_name = 'is_' + str(reason_name).replace("-", "_").lower()
                
                return_flag = str(i.get('return_flag'))
                if return_flag == 'Y':
                    result.update({list_name: True})
                elif return_flag == 'N':
                    result.update({list_name: False})
                else:
                    self.set_current_data_point_output(save_result)
                    error_result = {
                        "error": f"{ErrorMessage.RESPONSE_UNPROCESSABLE.value.format(**{'service': 'AMLO'})}: return flag case",
                        'exception_class': DataPointUnprocessableEntity
                    }
                    return error_result

        # Logic flagging (is_in_un_sanction)
        # Validate by total_record key in AMLO result
        current_un_sanction = result.get("is_in_un_sanction", False)
        if amlo_un_result.get('total_record', 0) > 0 :
            result.update({'is_in_un_sanction': True })
        elif not current_un_sanction:
            result.update({'is_in_un_sanction': False })

        self.set_current_data_point_output(save_result)

        # Flaging all.../any... in multi input case
        self.__flaging_data_result_multiple_input_one_list(result)
        
        return result

    def set_api_config(self):
        dynamicform = self.context.get("dynamicform", None)
        if not dynamicform:
            self.data_point_dap_host = DATA_POINT_DAP_HOST
            self.data_point_dap_username = DATA_POINT_DAP_USERNAME
            self.data_point_dap_password = DATA_POINT_DAP_PASSWORD
            return
    
        # new config
        self.data_point_dap_host = dynamicform.get_form_settings(
            'data_point_service.dap.host',
            default=None
        )
        self.data_point_dap_username = dynamicform.get_form_settings(
            'data_point_service.dap.username',
            default=None
        )
        self.data_point_dap_password = dynamicform.get_form_settings(
            'data_point_service.dap.password',
            default=None,
            secure=False
        )

        # backward compatibility
        if self.data_point_dap_host is None and self.data_point_dap_username is None and self.data_point_dap_password is None:
            self.data_point_dap_host = dynamicform.get_form_settings(
                'data_point.amlo.host',
                default=DATA_POINT_DAP_HOST
            )
            self.data_point_dap_username = dynamicform.get_form_settings(
                'data_point.amlo.username',
                default=DATA_POINT_DAP_USERNAME
            )
            self.data_point_dap_password = dynamicform.get_form_settings(
                'data_point.amlo.password',
                default=DATA_POINT_DAP_PASSWORD,
                secure=False
            )
    
    def get_amlo_person_screening_result(self, data: dict) -> dict:
        data_point_function_name = inspect.currentframe().f_code.co_name
        url = f'{self.data_point_dap_host}/api/amlo/person-screening'
        response = self._request(
            method='POST',
            url=url,
            json=data,
            auth=(self.data_point_dap_username, self.data_point_dap_password),
            data_point_function_name=data_point_function_name,
        )

        response_data = response.get('result', {})
        status_code = response.get('status_code')
        logging_context = self.get_logging_context()
        data_point_log_info = {
            'url': url,
            'json': data,
            'context': logging_context,
            'status_code': status_code,
        }
        logger.info(
            f'AMLO response {status_code}',
            data_point_log_info
        )

        # how to get pipeline or pipeline_id
        # pipeline_id # url = f"{DECISION_FLOW_TRIGGER_HOST}/api/workspaces/{workspace_slug}/forms/{form_slug}/decision-pipeline/{target_id}/run/"

        if not status.is_success(status_code):
            raise DataPointResponseException(
                f"{self.title}: {ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'AMLO'})}: {status_code}",
                data_point_log_info=data_point_log_info
            )

        return response_data
    
    def get_amlo_person_screening_un_list_result(self, data: dict) -> dict:
        data_point_function_name = inspect.currentframe().f_code.co_name
        url = f'{self.data_point_dap_host}/api/amlo/person-screening-un-list'
        response = self._request(
            method='POST',
            url=url,
            json=data,
            auth=(self.data_point_dap_username, self.data_point_dap_password),
            data_point_function_name=data_point_function_name
        )

        response_data = response.get('result', {})
        status_code = response.get('status_code')

        logging_context = self.get_logging_context()
        data_point_log_info = {
            'url': url,
            'json': data,
            'context': logging_context,
            'status_code': status_code,
        }
        logger.info(
            f'AMLO: AMLO response {status_code}',
            data_point_log_info
        )

        if not status.is_success(status_code):
            raise DataPointResponseException(
                f"{self.title}: {ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'AMLO'})}: {status_code}",
                data_point_log_info=data_point_log_info
            )
        return response_data

    def get_data_result_multiple_input_one_list(self):
        # Validate that data_result_multiple_input_one_list never flaging from any input
        # Throw exception if no input value in multi-input list
        if self.data_result_multiple_input_one_list is None:
            full_name_field: DataPointJSONField = self.fields.get('full_name')
            national_id_field: DataPointJSONField = self.fields.get('national_id')
            required_fields_key = ', '.join([
                national_id_field.label, 
                full_name_field.label
            ])
            format_text = {"fields": required_fields_key}
            raise DataPointInputValidationError(f'{self.title}: {ErrorMessage.NO_INPUT_SET_VALUE.value.format(**format_text)}')
        return self.data_result_multiple_input_one_list

    def reset_data_result_multiple_input_one_list(self):
        ''' reset to default by task issue in clickup #86cuwbvqt '''
        self.data_result_multiple_input_one_list = None
        
    def __flaging_data_result_multiple_input_one_list(self, result: dict[str, bool]):
        # Initial value for data_result_multiple_input_one_list result
        if self.data_result_multiple_input_one_list is None :
            self.data_result_multiple_input_one_list = {
                'are_all_freeze_05': True,
                'are_all_hr_02': True,
                'are_all_hr_08_risk': True,
                'are_all_in_un_sanction': True,
                'are_any_freeze_05': False,
                'are_any_hr_02': False,
                'are_any_hr_08_risk': False,
                'are_any_in_un_sanction': False,
                'are_any_hits': False,
            }
        # Flaging all.../any... in multi input case
        self.data_result_multiple_input_one_list['are_all_freeze_05'] = self.data_result_multiple_input_one_list['are_all_freeze_05'] and result['is_freeze_05']
        self.data_result_multiple_input_one_list['are_all_hr_02'] = self.data_result_multiple_input_one_list['are_all_hr_02'] and result['is_hr_02']
        self.data_result_multiple_input_one_list['are_all_hr_08_risk'] = self.data_result_multiple_input_one_list['are_all_hr_08_risk'] and result['is_hr_08_risk']
        self.data_result_multiple_input_one_list['are_all_in_un_sanction'] = self.data_result_multiple_input_one_list['are_all_in_un_sanction'] and result['is_in_un_sanction']
        
        self.data_result_multiple_input_one_list['are_any_freeze_05'] = self.data_result_multiple_input_one_list['are_any_freeze_05'] or result['is_freeze_05']
        self.data_result_multiple_input_one_list['are_any_hr_02'] = self.data_result_multiple_input_one_list['are_any_hr_02'] or result['is_hr_02']
        self.data_result_multiple_input_one_list['are_any_hr_08_risk'] = self.data_result_multiple_input_one_list['are_any_hr_08_risk'] or result['is_hr_08_risk']
        self.data_result_multiple_input_one_list['are_any_in_un_sanction'] = self.data_result_multiple_input_one_list['are_any_in_un_sanction'] or result['is_in_un_sanction']

        # logic for are_any_hits
        for any_hit in result.values():
            if self.data_result_multiple_input_one_list['are_any_hits'] is True:
                break
            self.data_result_multiple_input_one_list['are_any_hits'] = self.data_result_multiple_input_one_list['are_any_hits'] or any_hit
    
    def validate_input(
            self, 
            first_name: str | None,
            last_name: str | None,
            middle_name: str | None,
            national_id: str | None) -> tuple[bool, str | None]:
        '''Validate input that is valid for AMLO connection
        
        Validate checklist
        - Neither the full_name nor the national_id can be None.
        - national_id must valid from validate_thai_id function (in utilis.py from data_point/)
        
        Args:
            first_name (str | None): A string representing the first name to be validated.
            last_name (str | None): A string representing the last name to be validated.
            middle_name (str | None): A string representing the middle name to be validated.
            national_id (str | None): A string representing the Thai ID number to be validated.
        
        Returns:
            tuple: 
            * bool: True if inputs valid orElse, return False
            * str|None: Error message for display
            
        '''
        
        full_name_field: DataPointJSONField = self.fields.get('full_name')
        national_id_field: DataPointJSONField = self.fields.get('national_id')
        
        is_input_valid = True
        error_message = None
        is_name_valid = (first_name or last_name or middle_name)
        if not (is_name_valid and national_id):
            field_list = []
            
            if not is_name_valid :
                field_list.append(full_name_field.label)
            if not national_id:
                field_list.append(national_id_field.label)
                
            is_input_valid = False
            format_text = {"fields": ', '.join(field_list)}
            error_message = f'{self.title}: {ErrorMessage.EMPTY_VALUE.value.format(**format_text)}'
        elif not InputValidationUtil.validate_thai_id(national_id):
            is_input_valid = False
            format_text = {"field": national_id_field.label, "pattern_name": "Thai ID"}
            error_message = f'{self.title}: {ErrorMessage.NOT_MATCH_PATTERN.value.format(**format_text)}: {national_id}'
        return is_input_valid, error_message
