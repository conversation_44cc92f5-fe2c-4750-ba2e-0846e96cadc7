from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.core.files.uploadedfile import InMemoryUploadedFile
from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound, PermissionDenied
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>art<PERSON>arser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from dynamicform.models import AppliedForm, Form, Answer, AnswerFile
from dynamicform.models import AppliedFormSmartUploader
from dynamicform.submodules.appliedform.serializer import AppliedFormSmartUploaderSerializer
from dynamicform.submodules.appliedform.util import check_pdf_pages
from smartuploader.serializer import SmartUploaderInputSerializer
from workspace.helpers.permissions import HasEditSubmissionDetailPermission, HasViewSubmissionDetailPermission
from project.custom_logger import logger


ACTION_LIST_SMART_UPLOADER = "list_smart_uploader"
ACTION_RETRIEVE_SMART_UPLOADER = "retrieve_smart_uploader"
ACTION_UPLOAD_SMART_UPLOADER = "upload_smart_uploader"
ACTION_UPDATE_SMART_UPLOADER = "update_smart_uploader"
ACTION_DELETE_SMART_UPLOADER = "delete_smart_uploader"
ACTION_GET_RESULT_SMART_UPLOADER = "get_result_smart_uploader"

# role permissions
ACTION_EDIT_SUBMISSION_DETAIL = [
    "update",
    "upload_files_api",
]
ACTION_VIEW_SUBMISSION_DETAIL = [
    "get_smartuploader_result",
]

DATABASE_MODEL_USE_DB = settings.DATABASE_MODEL_USE_DB.get("smart_uploader", {})
DATABASE_MODEL_USE_DB_FORM = settings.DATABASE_MODEL_USE_DB.get("form", {})
DATABASE_MODEL_USE_DB_APPLIED_FORM = settings.DATABASE_MODEL_USE_DB.get("applied_form", {})


class AppliedFormSmartUploaderViewSet(viewsets.ModelViewSet):
    parser_classes = [JSONParser, FormParser, MultiPartParser]
    serializer_class = AppliedFormSmartUploaderSerializer
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ["id", "smart_uploader__created_at"]
    ordering = ["-smart_uploader__created_at"]

    def get_permissions(self):
        if self.action in ACTION_EDIT_SUBMISSION_DETAIL:
            permission_classes = [IsAuthenticated, HasEditSubmissionDetailPermission]
        elif self.action in ACTION_VIEW_SUBMISSION_DETAIL:
            permission_classes = [IsAuthenticated, HasViewSubmissionDetailPermission]
        else:
            permission_classes = self.permission_classes

        return [permission() for permission in permission_classes]

    def get_queryset(self):
        form_slug = self.kwargs["form_slug"]
        applied_form_slug = self.kwargs["applied_form_slug"]
        question = self.kwargs["question"]
        queryset = AppliedFormSmartUploader.objects

        if self.action in ["list", "retrieve"]:
            use_db = DATABASE_MODEL_USE_DB.get("read", "default")
            queryset = queryset.using(use_db)

        smart_uploaders = queryset.filter(
            applied_form__slug=applied_form_slug, applied_form__form__slug=form_slug, answer__question=question
        )

        return smart_uploaders

    def get_applied_form(self):
        applied_form_slug = self.kwargs["applied_form_slug"]
        form_slug = self.kwargs["form_slug"]
        question = self.kwargs["question"]

        form_use_db = DATABASE_MODEL_USE_DB_FORM.get("read", "default")
        form = Form.objects.using(form_use_db).filter(slug=form_slug).first()
        if not form:
            raise NotFound()

        backend_schema = form.backend_schema
        input_smart_uploaders = backend_schema.get("input_smart_uploaders", {})
        item = input_smart_uploaders.get(question)
        if not item:
            raise NotFound()

        applied_form_use_db = DATABASE_MODEL_USE_DB_APPLIED_FORM.get("read", "default")
        applied_form = AppliedForm.objects.using(applied_form_use_db).filter(slug=applied_form_slug, form=form).first()
        if not applied_form:
            raise NotFound()

        return applied_form, item

    def create(self, request, question=None, *args, **kwargs):
        import memory_profiler

        # Initial RAM check
        initial_memory = memory_profiler.memory_usage()[0]
        logger.debug(f"Memory usage at start: {initial_memory:.2f} MB")

        # Get applied form
        applied_form, item = self.get_applied_form()
        if not applied_form.can_update_info():
            return Response(_("Cannot update form"), status=status.HTTP_403_FORBIDDEN)

        form_memory = memory_profiler.memory_usage()[0]
        logger.debug(
            f"Memory after getting form: {form_memory:.2f} MB (diff: {(form_memory - initial_memory):.2f} MB)"
        )

        # Input validation
        data = request.data.copy()
        files = data.get("files[]", data.get("files", None))
        password = data.get("password", None)

        if not files:
            data = {}
        else:
            data["files"] = files
        
        if password:
            data["password"] = password

        serializer = SmartUploaderInputSerializer(data=data)
        is_valid = serializer.is_valid(raise_exception=False)
        if not is_valid:
            applied_form.log(
                None, None, action=ACTION_UPLOAD_SMART_UPLOADER, detail={"content": serializer.errors}, is_fail=True
            )
            return Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        validation_memory = memory_profiler.memory_usage()[0]
        logger.debug(
            f"Memory after input validation: {validation_memory:.2f} MB (diff: {(validation_memory - form_memory):.2f} MB)"
        )

        # Answer handling
        try:
            answer = Answer.objects.get(applied_form=applied_form, question=question)
        except Answer.DoesNotExist:
            answer = Answer.objects.create(applied_form=applied_form, question=question, value=[])

        answer_memory = memory_profiler.memory_usage()[0]
        logger.debug(
            f"Memory after answer handling: {answer_memory:.2f} MB (diff: {(answer_memory - validation_memory):.2f} MB)"
        )

        # Page validation
        first_file: InMemoryUploadedFile = serializer.validated_data.get("files", [])[0]
        answer_files: list[AnswerFile] = answer.answerfile_set.all()
        logger.debug(f"Answer Files: {len(answer_files)}")
        logger.debug(f"New file: {first_file.name}")
        before_page_count = sum([check_pdf_pages(answer_file.detail) for answer_file in answer_files])
        new_page_count = check_pdf_pages(first_file, keep_file_open=True)
        after_page_count = before_page_count + new_page_count
        max_page_count = item.get("configs", {}).get("max_page_count", 1)

        if after_page_count > max_page_count:
            error_response = {
                "fail_reason": "file_exceeds_page_limit",
            }
            if before_page_count >= max_page_count:
                error_response["already_at_max"] = True
            applied_form.log(
                None, None, action=ACTION_UPLOAD_SMART_UPLOADER, detail={"content": error_response}, is_fail=True
            )
            return Response(error_response, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        pages_memory = memory_profiler.memory_usage()[0]
        logger.debug(
            f"Memory after page validation: {pages_memory:.2f} MB (diff: {(pages_memory - answer_memory):.2f} MB)"
        )

        # Upload handling
        try:
            applied_form_smart_uploader, created = AppliedFormSmartUploader.objects.update_or_create(
                applied_form=applied_form, answer=answer, defaults={"page_count": after_page_count}
            )
            return_response = applied_form_smart_uploader.upload_file(first_file, password=password)
            applied_form.log(None, None, action=ACTION_UPLOAD_SMART_UPLOADER, is_fail=False)
        except Exception as e:
            applied_form.log(
                None,
                None,
                action=ACTION_UPLOAD_SMART_UPLOADER,
                detail={"content": getattr(e, "detail", str(e))},
                is_fail=True,
            )
            raise e

        upload_memory = memory_profiler.memory_usage()[0]
        logger.debug(
            f"Memory after upload handling: {upload_memory:.2f} MB (diff: {(upload_memory - pages_memory):.2f} MB)"
        )

        return_status = (
            status.HTTP_201_CREATED if return_response.get("pass_validation") else status.HTTP_400_BAD_REQUEST
        )
        return Response(return_response, status=return_status)

    @action(detail=False, methods=["post"], url_path="files")
    def upload_files_api(self, request, question=None, *args, **kwargs):
        applied_form, item = self.get_applied_form()

        if not applied_form.can_update_info():
            return Response(_("Cannot update form"), status=status.HTTP_403_FORBIDDEN)

        data = request.data
        files = data.get("files[]", data.get("files", None))
        password = data.get("password", None)

        if not files:
            data = {}
        else:
            data["files"] = files

        if password:
            data["password"] = password

        # Validate Input
        serializer = SmartUploaderInputSerializer(data=data)
        is_valid = serializer.is_valid(raise_exception=False)
        if not is_valid:
            applied_form.log(
                None,
                None,
                action=ACTION_UPLOAD_SMART_UPLOADER,
                detail={"content": serializer.errors},
                is_fail=True,
            )
            return Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        # Create/Get Answer
        answer: Answer
        try:
            answer = Answer.objects.get(applied_form=applied_form, question=question)
        except Answer.DoesNotExist:
            answer = Answer.objects.create(applied_form=applied_form, question=question, value=[])

        # Validate File Pages
        first_file: InMemoryUploadedFile = serializer.validated_data.get("files", [])[0]
        answer_files: list[AnswerFile] = answer.answerfile_set.all()
        before_page_count = sum([check_pdf_pages(answer_file.detail) for answer_file in answer_files])
        new_page_count = check_pdf_pages(first_file, keep_file_open=True)
        after_page_count = before_page_count + new_page_count
        max_page_count = item.get("configs", {}).get("max_page_count", 1)

        if after_page_count > max_page_count:
            error_response = {"files": ["File exceeds the page limit."]}
            applied_form.log(
                None,
                None,
                action=ACTION_UPLOAD_SMART_UPLOADER,
                detail={"content": error_response},
                is_fail=True,
            )
            return Response(error_response, status=413)

        # Create/Get AppliedFormSmartUploader
        applied_form_smart_uploader, created = AppliedFormSmartUploader.objects.update_or_create(
            applied_form=applied_form, answer=answer, defaults={"page_count": after_page_count}
        )

        # Upload
        return_response = None
        try:
            return_response = applied_form_smart_uploader.upload_file(first_file, password=password)
        except Exception as e:
            applied_form.log(
                None,
                None,
                action=ACTION_UPLOAD_SMART_UPLOADER,
                detail={"content": getattr(e, "detail", str(e))},
                is_fail=True,
            )
            raise e

        applied_form.log(None, None, action=ACTION_UPLOAD_SMART_UPLOADER, is_fail=False)

        pass_validation = return_response.get("pass_validation")
        if pass_validation:
            return_status = status.HTTP_201_CREATED
        else:
            return_response = {"files": ["File validation failed."]}
            return_status = status.HTTP_422_UNPROCESSABLE_ENTITY

        return Response(return_response, status=return_status)

    @action(detail=False, methods=["get"], url_path="result")
    def get_smartuploader_result(self, request, question=None, *args, **kwargs):
        applied_form, item = self.get_applied_form()
        if not applied_form.can_view_report():
            raise PermissionDenied()

        data = {
            "result": {},
            "message": None,
            "validation": {},
            "files": [],
        }
        response = {"status": "incomplete", "data": data, "detail": "waiting_for_submit"}
        try:
            answer = Answer.objects.get(applied_form=applied_form, question=question)
        except Answer.DoesNotExist:
            # no answer yet then return default
            return Response(response)

        files = []
        for answer_files in answer.answerfile_set.all():
            files.append(answer_files.submit_info())
        data.update({"files": files})

        try:
            applied_form_smart_uploader = AppliedFormSmartUploader.objects.get(
                applied_form=applied_form, answer__question=question
            )
        except AppliedFormSmartUploader.DoesNotExist:
            # answered but not submit then return files list if any
            return Response(response)

        # submit
        status, detail, data_result = applied_form_smart_uploader.get_question_result()
        if status:
            response.update({"status": status})
        if detail:
            response.update({"detail": detail})
        elif detail == "":
            response.update({"detail": None})
        if data_result:
            data.update(data_result)
        return Response(response)
