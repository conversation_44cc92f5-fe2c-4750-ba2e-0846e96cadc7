from copy import copy
from pydash.objects import get
from pydash.strings import snake_case
from dynamicform.schema import GetApp
import json
import re
from rest_framework import exceptions
from typing import List
from smartuploader.types import (
	ISchemaItemSmartUploader,
	ExtractVerifyErrorPayload,
	ExtractVerifyFields,
	ExtractVerifyTables,
	ExtractVerifyColumns
)

class Converter:
	file_types = ['OfflineUploader', 'SignaturePad', 'SmartUploader']

	@staticmethod
	def convert(schema):
		if type(schema) == str:
			schema = json.loads(schema)

		output = {}
		if 'steps' not in schema:
			return output

		steps = schema['steps']
		items, items_steps = Converter.collect_all_items(steps)

		output['app_list'] = Converter.collect_apps(steps)
		output['all_apps'] = Converter.collect_all_apps(items)
		output['steps'] = Converter.collect_steps(steps)
		output['validator_rule'] = Converter.collect_rules(items)
		output['input_files'] = Converter.collect_input_files(items)
		output['labels'] = Converter.collect_labels(items)
		keep_answers, default_values = Converter.get_keep_answers_and_default(schema)
		output['keep_answers'] = keep_answers
		output['default_values'] = default_values
		items_info, visible_items_type = Converter.collect_items_info(items, output['keep_answers'])
		output['items_steps'] = items_steps
		output['items'] = items_info
		output['visible_items_type'] = visible_items_type
		output['extra_answers'], output['extra_answers_main_form'] = Converter.collect_extra_answers(items)
		output['disable_logging'] = Converter.collect_disable_logging(items)

		output['allow_notification'] = schema.get('allow_notification', True)
		output['allowed_guest'] = schema.get('allowed_guest', True)
		output['mapping_init_answers'] = Converter.collect_mapping_init_answers(
			schema)
		output['first_step_section'] = Converter.first_step_section(steps)
		output['disabled_update'] = schema.get('disabled_update', [])
		output['input_utility_bills'] = Converter.collect_input_utility_bills(items)
		output['input_smart_uploaders'] = Converter.collect_input_smart_uploaders(items)
		output['feature_credits'] = Converter.collect_feature_credits(items)
		output['steps_sections'], output['section_trigger_events'] = Converter.collect_steps_sections(steps)
		output['steps_sections_item_types'] = Converter.collect_steps_sections_item_types(
			output['steps_sections'],
			items
		)
		output['modify_validation_rules'] = Converter.modify_validation_rules(schema)
		output['releted_questions'], output['auto_compute_value']  = Converter.collect_releted_questions(items)
		output['disabled_save'] = schema.get('disabled_save', []) + list(output['auto_compute_value'].keys())
		output['encrypt_answers'] = schema.get('encrypt_answers', [])

		return output

	@staticmethod
	def collect_steps(steps):
		output = {}
		for k, step in steps.items():
			visible = get(step, 'visible', True)
			if not visible:
				continue

			_step = GetApp.get_step_app(step)
			output[k] = _step.convert(step)

		return output

	@staticmethod
	def collect_actions_items(items, actions, step, section):
		for acton in actions:
			if 'manage_reults' in acton:
				for result_action in acton['manage_reults']:
					_items = Converter.collect_action_items(
						items, result_action, step, section)
					items.update(_items)
			if 'manage_results' in acton:
				for result_action in acton['manage_results']:
					_items = Converter.collect_action_items(
						items, result_action, step, section)
					items.update(_items)

		return items

	@staticmethod
	def collect_action_items(items, action, step, section):
		result = {}
		if action['action'] != 'save':
			return items

		mapping_items = action.get('params', {}).get('mapping', None)
		if mapping_items == None:
			return {}

		for key, value in mapping_items.items():
			if key in items:
				continue

			result.update({
				key: {
					'name': key,
					'step': step,
					'section': section,
					'display': {
						'label': key
					},
					'type': 'InputText',
					'_keys': []
				}
			})

		return result

	@staticmethod
	def collect_all_items(steps):
		items = dict()
		items_steps = dict()
		
		def item_append_step(item_name, step_key):
			if not item_name:
				return
			
			_steps = items_steps.get(item_name, [])
			_steps.append(step_key)
			_steps = list(sorted(set(_steps)))
			items_steps.update({
				item_name: _steps
			})

		for step_key, step in steps.items():
			visible = get(step, 'visible', True) #disable / no hide by default
			if not visible:
				continue

			item_keys = ['steps']
			item_keys.append(step_key)
			if 'sections' not in step:
				continue

			step_visible = step.get('visible', True)
			item_keys.append('sections')
			for section_key, section in step['sections'].items():
				_item_keys = copy(item_keys)
				if 'pre_save' in section:
					_items = Converter.collect_actions_items(
						items, section['pre_save'], step_key, section_key)
					items.update(_items)
					for item_name in _items:
						item_append_step(item_name, step_key)

				if 'post_save' in section:
					_items = Converter.collect_actions_items(
						items, section['post_save'], step_key, section_key)
					items.update(_items)
					for item_name in _items:
						item_append_step(item_name, step_key)

				if 'items' not in section:
					continue

				_item_keys.append(section_key)
				_item_keys.append('items')
				_items = Converter.collect_items(
					section['items'], step_key, section_key, step_visible=step_visible, item_keys=_item_keys)
				items.update(_items)
				for item_name in _items:
					item_append_step(item_name, step_key)

		return items, items_steps

	@staticmethod
	def collect_apps(steps):
		step_app_list = [GetApp.app_default]
		sections_app_list = [GetApp.app_default]

		for _, step in steps.items():
			step_type = get(step, 'type', None)
			visible = get(step, 'visible', True)
			if not visible:
				continue

			if step_type and step_type not in step_app_list:
				step_app_list.append(step_type)

			if 'sections' not in step:
				continue

			for _, section in get(step, 'sections', {}).items():
				section_type = get(section, 'type', None)
				if section_type and section_type not in sections_app_list:
					sections_app_list.append(section_type)

		return {
			'steps': step_app_list,
			'sections': sections_app_list,
		}

	@staticmethod
	def collect_items(items, step_key, section_key, step_visible=True, item_keys=[]):
		result = dict()
		for item_key, item in items.items():
			_keys = copy(item_keys)
			_keys.append(item_key)
			if 'items' in item:
				_keys.append('items')
				_items = Converter.collect_items(
					item['items'], step_key, section_key, step_visible=step_visible, item_keys=_keys)
				result.update(_items)
				del item['items']

			item['step_visible'] = step_visible
			item['step'] = step_key
			item['section'] = section_key
			item['_keys'] = _keys
			_result = {
				item_key: item
			}
			result.update(_result)
		# end for items =====
		return result

	@staticmethod
	def collect_rules(items):
		result = dict()
		for item_key, item in items.items():
			if 'name' not in item:
				continue
			if item.get('visible') == False or item.get('step_visible') == False:
				continue
			# :todo
			if item['name'] == 'consent':
				continue

			item = Converter.add_file_type_rule(item)

			if not item or ('validator_rule' not in item) or (item.get('validator_rule') == ''):
				continue

			rule = item.get('validator_rule')
			_item = {
				item['name']: rule
			}
			result.update(_item)
		# end for items =====
		return result

	@staticmethod
	def collect_input_files(items):
		file_types = ['OfflineUploader', 'SignaturePad']

		result = dict()
		for item_key, item in items.items():
			if 'step' not in item:
				continue
			if 'section' not in item:
				continue
			if 'name' not in item:
				continue
			if 'type' not in item:
				continue
			if item['type'] not in file_types:
				continue

			multiple = True
			if 'configs' in item and 'multiple' in item['configs']:
				multiple = item['configs']['multiple']

			_item = {
				item['name']: {
					'step': item['step'],
					'section': item['section'],
					'multiple': multiple,
				}
			}
			result.update(_item)
		# end for items =====
		return result
	
	@staticmethod
	def validate_table_columns(
			columns: List[ExtractVerifyColumns],
			table_index: int,
			error_payload: ExtractVerifyErrorPayload
		):
		key =  'extract_verify_tables'

		COLUMN_NAME_PATTERN = r"^[a-zA-Z0-9_-]*$"
		COLUMN_NAME_PATTERN_ERROR_MESSAGE = 'Invalid column_name format. Please use (A-Z), (0-9), (_), (-)'
		COLUMN_NAME_REQUIRED_ERROR_MESSAGE = 'column_name is required'
		COLUMN_DESC_ERROR_MESSAGE = 'column_description is required'

		# Validate column_name and column_description
		for j, col in enumerate(columns):
			column_name = col.get('column_name')
			column_description = col.get('column_description')
			if not column_name:
				(
					error_payload[key]
					.setdefault(table_index, {})
					.setdefault('columns', {})
					.setdefault(j, {})
					.update({"column_name": COLUMN_NAME_REQUIRED_ERROR_MESSAGE})
				)
			elif not re.match(COLUMN_NAME_PATTERN, column_name):
				(
					error_payload[key]
					.setdefault(table_index, {})
					.setdefault('columns', {})
					.setdefault(j, {})
					.update({"column_name": COLUMN_NAME_PATTERN_ERROR_MESSAGE})
				)
			
			if not column_description:
				(
					error_payload[key]
					.setdefault(table_index, {})
					.setdefault('columns', {})
					.setdefault(j, {})
					.update({"column_description": COLUMN_DESC_ERROR_MESSAGE})
				)

	@staticmethod
	def validate_fields(
			extract_verify_list: List[ExtractVerifyFields] | List[ExtractVerifyTables],
			key: str, 
			error_payload: ExtractVerifyErrorPayload
		):

		FIELD_NAME_PATTERN = r"^[a-zA-Z0-9_-]*$"
		FIELD_NAME_PATTERN_ERROR_MESSAGE = 'Invalid field_name format. Please use (A-Z), (0-9), (_), (-)'
		FIELD_NAME_REQUIRED_ERROR_MESSAGE = 'field_name is required'
		FIELD_DESC_ERROR_MESSAGE = 'field_description is required'

		# Validate field_name and field_description
		for i, item in enumerate(extract_verify_list):
			field_name = item.get("field_name")
			field_description = item.get("field_description")
			field_type = item.get("field_type")
			columns = item.get("columns", [])

			if not field_name:
				error_payload[key][i] = {"field_name": FIELD_NAME_REQUIRED_ERROR_MESSAGE}
			elif not re.match(FIELD_NAME_PATTERN, field_name):
				error_payload[key][i] = {"field_name": FIELD_NAME_PATTERN_ERROR_MESSAGE}

			if not field_description:
				error_payload[key].setdefault(i, {}).update({"field_description": FIELD_DESC_ERROR_MESSAGE})

			if field_type == 'table':
				Converter.validate_table_columns(columns, i, error_payload)


	@staticmethod
	def validate_extract_verify_payload(schema: ISchemaItemSmartUploader):
		error_payload: ExtractVerifyErrorPayload = {
			"extract_verify_fields": {},
			"extract_verify_tables": {},
    	}

		Converter.validate_fields(schema.get("extract_verify_fields", []), "extract_verify_fields", error_payload)
		Converter.validate_fields(schema.get("extract_verify_tables", []), "extract_verify_tables", error_payload)

		if error_payload["extract_verify_fields"] or error_payload["extract_verify_tables"]:
			raise  exceptions.ValidationError(error_payload)
		return True

	@staticmethod
	def collect_input_smart_uploaders(items: dict):
		file_types = ['SmartUploader']

		result = dict()
		for item_key, item in items.items():
			if 'step' not in item:
				continue
			if 'section' not in item:
				continue
			if 'name' not in item:
				continue
			if 'type' not in item:
				continue

			if item['type'] not in file_types:
				continue

			schema: ISchemaItemSmartUploader = item

			# Validate extract verify payload
			Converter.validate_extract_verify_payload(schema)

			extract_verify_fields = schema.get('extract_verify_fields', [])

			rules: dict[str, str] = {}
			related_answers: dict[str, list[str]] = {}

			for r in extract_verify_fields:
				field_name: str  = r.get('field_name')
				rule_str: str = r.get('validator_rule') or ''
				rules[field_name] = rule_str
				related_answers[field_name] = []

				if not rule_str:
					continue
				
				rule_list = rule_str.split('|')
				for rule in rule_list:
					rule_parts = rule.split(':')
					if rule_parts[0] == 'same':
						related_answers[field_name].append(rule_parts[1])

			configs = item.get('configs')
			multiple = True

			_item = {
				item['name']: {
					'step': item['step'],
					'section': item['section'],
					'configs': configs,
					'multiple': multiple,
					'rules': rules,
					'related_answers': related_answers,
				}
			}
			result.update(_item)
		# end for items =====
		return result

	@staticmethod
	def collect_input_utility_bills(items):
		file_types = ['UtilityBill']

		result = dict()
		for item_key, item in items.items():
			if 'step' not in item:
				continue
			if 'section' not in item:
				continue
			if 'name' not in item:
				continue
			if 'type' not in item:
				continue
			if item['type'] not in file_types:
				continue

			multiple = True

			_item = {
				item['name']: {
					'step': item['step'],
					'section': item['section'],
					'multiple': multiple,
				}
			}
			result.update(_item)
		# end for items =====
		return result

	@staticmethod
	def collect_labels(items):

		result = dict()
		for item_key, item in items.items():
			if 'name' not in item:
				continue
			if 'display' not in item:
				continue

			if 'label' not in item['display']:
				continue

			else:
				label_key = item['_keys']
				label_key += ['display', 'label']
				label_key = '.'.join(label_key)
				label = label_key

			_item = {
				item['name']: label
			}
			result.update(_item)
		# end for items =====
		return result
	
	@staticmethod
	def get_item_info(item):
		from decision_flow.condition.children import Children

		item_type = item.get('type', None)
		if item_type == 'DynamicResultCriteria':
			tiers = get(item, 'tiers')
			for tier in tiers:
				condition = tier.get('condition', {})
				children = Children(data=condition, context={})
				children.is_valid()
				tier.update({'condition_children': children.data})
		elif item_type == 'SmartUploader':
			item_name = item.get("name")
			auto_gen_items = dict()
			auto_save_answers = dict()
			for extract_field in get(item, 'extract_verify_fields', []):
				field_name = snake_case(get(extract_field, 'field_name', ''))
				field_type = get(extract_field, 'field_type')
				auto_gen_item_name = f'{item_name}_{field_name}'
				auto_gen_item_type = 'InputNumber' if field_type == 'number' else 'InputText'
				auto_gen_items.update({auto_gen_item_name: auto_gen_item_type})
				auto_save_answers.update({field_name:auto_gen_item_name})
			for extract_field in get(item, 'extract_verify_tables', []):
				field_name = snake_case(get(extract_field, 'field_name', ''))
				auto_gen_item_name = f'{item_name}_{field_name}'
				auto_gen_item_type = 'InputText'
				auto_gen_items.update({auto_gen_item_name: auto_gen_item_type})
				auto_save_answers.update({field_name:auto_gen_item_name})
			item["auto_gen_items"] = auto_gen_items
			item["auto_save_answers"] = auto_save_answers
		
		return item

	@staticmethod
	def collect_items_info(items, keep_answers={}):
		result = dict()
		items_type = dict()
		auto_gen_items = dict()
		for item_key, item in items.items():
			if 'name' not in item:
				continue
			_item = {
				item['name']: {
					**Converter.get_item_info(item),
					'step': item['step'],
					'section': item['section']
				}
			}
			result.update(_item)

			if item.get('visible') is False or item.get('step_visible') is False:
				continue

			item_type = item.get('type', None)
			if not item_type:
				continue

			_auto_gen_items = item.get("auto_gen_items", {})
			auto_gen_items.update(_auto_gen_items)

			if item_type not in items_type:
				items_type[item_type] = {}
			items_type[item_type].update(_item)

		def add_auto_gen_item(item_key, item_type):
			if item_key in result:
				return
			_item = {
				item_key: {
					'display': {
						'label': item_key
					},
					'type': item_type,
					'name': item_key,
					'step': '',
					'section': ''
				}
			}
			result.update(_item)
			if item_type not in items_type:
				items_type[item_type] = {}
			items_type[item_type].update(_item)

		for item_key, _ in keep_answers.items():
			add_auto_gen_item(item_key, 'InputText')
		for item_key, item_type in auto_gen_items.items():
			add_auto_gen_item(item_key, item_type)

		return result, items_type

	@staticmethod
	def add_file_type_rule(item):
		if 'configs' not in item:
			return item

		if 'file_type' not in item['configs']:
			return item

		allow_file_type = ','.join(item['configs']['file_type'])
		rule = 'extension:' + allow_file_type

		if ('validator_rule' in item) and len(item['validator_rule']) > 0:
			rule = '|'.join([item['validator_rule'], rule])
		
		item['validator_rule'] = rule
		return item

	@staticmethod
	def collect_all_apps(items):
		all_apps = []
		for _, item in items.items():
			item_type = item.get('type', None)
			if not item_type:
				continue
			item_apps = item_type.split(".")
			_app = item_apps[0]
			app = "dynamicform"
			if len(item_apps) > 1:
				app = _app
			if app not in all_apps:
				all_apps.append(app)
		
		return all_apps
		

	@staticmethod
	def collect_extra_answers(items):
		'''
		To attach answers from related applied-form to the current applied form
		input
		{
		  "current-form-item-a": {
			"name": "current-form-item-a",
			"lookup": {
				"form_slug": "source-form-slug",
				"properties": "source-form-applied-form-property",
				"name": "source-form-item-name",
			  }, 
			}
		  }
		}

		return json
		{
		  "extra_answers": {
			"source-form-slug": {
			  "questions": [
				"source-form-item-name"
			  ],
			  "properties": [],
			  "map": {
				"current-form-item-a": "source-form-item-name"
			  }
			}
		  },
		}
		!note: if lookup has no form-slug provides 
		then dynamicform will look up only answer in main applied_form 
		in the application of the focus form by a question
		'''
		result = {}
		find_in_main = {'questions': [], 'properties': [], 'map': {}}
		for item_key, item in items.items():
			if 'lookup' in item and 'form_slug' in item['lookup']:
				if not item['lookup']['form_slug'] in result:
					result.update({
						item['lookup']['form_slug']: {
							'questions': [],
							'properties': [],
							'map': {},
						}
					})

				if 'name' in item['lookup']:
					result[item['lookup']['form_slug']]['questions'].append(
						item['lookup']['name'])
					result[item['lookup']['form_slug']]['map'].update({
						item['lookup']['name']: item['name']
					})
				elif 'property' in item['lookup']:
					result[item['lookup']['form_slug']]['properties'].append(
						item['lookup']['property'])
					result[item['lookup']['form_slug']]['map'].update({
						item['lookup']['property']: item['name']
					})
			elif 'lookup' in item and 'form_slug' not in item['lookup']:
				if 'name' in item['lookup']:
					find_in_main['questions'].append(item['lookup']['name'])
					find_in_main['map'].update(
						{item['lookup']['name']: item['name']})
				elif 'property' in item['lookup']:
					find_in_main['properties'].append(
						item['lookup']['property'])
					find_in_main['map'].update(
						{item['lookup']['property']: item['name']})

		return result, find_in_main

	@staticmethod
	def collect_disable_logging(items):
		item_paths = []
		questions = []
		for _, item in items.items():
			disable_logging = item.get('disable_logging', None)
			if disable_logging and 'name' in item:
				item_path = '.'.join(
					[item['step'], item['section'], item['name']])
				item_paths.append(item_path)
				questions.append(item['name'])
		return {
			'item_paths': item_paths,
			'questions': questions
		}

	@staticmethod
	def collect_mapping_init_answers(schema):
		mapping_init_answers = schema.get('mapping_init_answers', {})
		return {'data': mapping_init_answers}

	@staticmethod
	def first_step_section(schema):
		for step_name, step in schema.items():
			for section_name, section in step.get('sections', {}).items():
				return step_name, section_name
			break
		return None, None

	@staticmethod
	def collect_steps_sections(schema):
		result = []
		section_trigger_events = []

		for step_name, step in schema.items():

			if not step.get('visible', True):
				continue

			have_builder_key = step.get('builder', None) is not None

			for section_name, section in step.get('sections', {}).items():
				if not section.get('visible', True):
					continue

				result.append({
					'step': step_name,
					'section': section_name,
					'label': section.get('label', None),
				})

				if have_builder_key:
					# it's a special step/section.
					continue
				
				event_label = section.get('label', None)
				event_value = section_name.capitalize()
				section_trigger_events.append({
					'label': f'On {event_label} Complete',
					'value': f'onSection{event_value}Complete',
				})

		return result, section_trigger_events

	@staticmethod
	def collect_feature_credits(items):
		from credit_system.feature_credits import FEATURE_CREDITS
		features = []
		for _, item in items.items():
			item_type = item.get('type', None)
			if not item.get('step_visible', True):
				continue
			for feature_key, frature in FEATURE_CREDITS.items():
				if item_type in frature.get('item_types', []):
					features.append(feature_key)

		return features

	@staticmethod
	def collect_steps_sections_item_types(steps_sections, items):
		'''
		return 
		{
			"step": {
				"section": {
					"itep_type": [
						"item_name",
					]
				}
			}
		}
		'''
		result = {}
		for steps_section in steps_sections:
			step = steps_section.get("step")
			section = steps_section.get("section")
			section_app = {}
			for item_key, item in items.items():
				if item.get("step", None) != step:
					continue
				if item.get("section", None) != section:
					continue

				item_type = item.get("type", None)
				if not item_type:
					continue
				if len(item_type.split(".")) < 2:
					continue
				
				if item_type not in section_app:
					section_app.update({
						item_type: [item_key]
					})
				else:
					section_app[item_type].append(item_key)

			if not section_app:
				continue

			if result.get(step):
				result.get(step).update({section: section_app})
			else:
				result.update({step: {section: section_app}})

		return result

	@staticmethod
	def modify_validation_rules(schema):
		from decision_flow.entity.node_condition import NodeCondition

		_methods = [
			"set_item_required",
			"unset_item_required",
			"hide_item",
			"show_item",
			"hide_step",
		]
		events = schema.get("events", [])
		result = []
		
		for event in events:
			actions = event.get("actions", [])
			modify_actions = []
			for action in actions:
				method = action.get("method", None)
				if method in _methods:
					modify_actions.append(action)

			if not modify_actions:
				continue
			
			guards = event.get("guards", [])
			_guards = []
			for guard in guards:
				condition = NodeCondition(data={"param": guard}, context={})
				# This is not a decision flow so, just ignore the validation result
				# .is_valid is required when a serializer calls .data
				condition.is_valid()
				_guards.append(condition.data)
	
			event["guards"] = _guards
			event["actions"] = modify_actions
			result.append(event)
		
		return result

	@staticmethod
	def collect_releted_questions(items):
		result = {}
		auto_compute_value = {}

		def update_result(question_name, item_key):
			if not question_name:
				return

			if item_key not in auto_compute_value:
				auto_compute_value.update({item_key:[]})

			if question_name not in auto_compute_value[item_key]:
				auto_compute_value[item_key].append(question_name)

			if question_name not in result:
				result.update({
					question_name: [item_key]
				})
			elif item_key not in result[question_name]:
				result[question_name].append(item_key)

		for item_key, item in items.items():
			if item.get("type") == "DynamicResultScore":
				questions = item.get("questions", [])
				for question in questions:
					update_result(
						question.get("name"),
						item_key
					)
			elif item.get("type") == "DynamicResultCriteria":
				tiers = item.get("tiers", [])
				for tier in tiers:
					condition = get(tier, "condition")
					initial_data_category = get(condition, "initial_data.category")
					if initial_data_category == "answer":
						initial_data_value = get(condition, "initial_data.value")
						update_result(
							initial_data_value,
							item_key
						)
					expected_data_category = get(condition, "expected_data.category")
					if expected_data_category == "answer":
						expected_data_value = get(condition, "expected_data.value")
						update_result(
							expected_data_value,
							item_key
						)

		return result, auto_compute_value

	@staticmethod
	def get_keep_answers_and_default(schema):
		'''
		schema := {
			"builder": {"enabled_input_param": true}
			"keep_answers": {
				"question_1": "{{param}}",
				"question_2": {
					"template": "no_{{param2}}",
					"default": {
						"value": "system_id",
  						"value_type": "string"
					}
				}
			}	
		}
		====
		return 
		keep_answers := {
			"question_1":  "{{param}}",
			"question_2": "no_{{param2}}",
		},
		default_values := {
			"question_2": {
				"value": "system_id",
				"value_type": "string"
			}
		}
		'''
		keep_answers = {} #{'question': 'request.obj.key'}
		default_values = {} #{'question': {'value': null,'value_type': 'string'}}
		
		schema_keep_answers = get(schema, 'keep_answers', {})
		enabled_input_param = get(schema, 'builder.enabled_input_param', True)
		if not schema_keep_answers:
			return keep_answers, default_values
		
		for question, config in schema_keep_answers.items():
			if isinstance(config, str):
				keep_answers.update({
					question: config
				})
				# no default
				continue
			
			#  always attatch keep_answers
			template = config.get("template")
			if template:
				keep_answers.update({
					question: template
				})
			else:
				keep_answers.update({
					question: f"{{{{{question}}}}}"
				})
			
			if not enabled_input_param:
				# disable all default input
				continue
			
			config_default = config.get("default")
			default_value = config_default.get("value", None)
			# default is None -> do nothing
			if default_value is None:
				continue
			
			default_values.update({
				question: config_default
			})

		return keep_answers, default_values
