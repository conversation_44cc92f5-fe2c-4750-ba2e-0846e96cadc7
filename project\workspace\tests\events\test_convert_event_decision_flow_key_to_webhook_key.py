from django.test import SimpleTestCase
from workspace.events import convert_event_decision_flow_key_to_webhook_key


SECTION_TRIGGER_EVENTS = [
    {
        "label": "On Section Personal Detail",
        "value": "onSectionPersonalDetail"
    }
]


class ConvertEventDecisionFlowKeyToWebhookKey(SimpleTestCase):

    def test_event_in_alowed_events(self):
        self.assertEqual(convert_event_decision_flow_key_to_webhook_key("onSubmit", SECTION_TRIGGER_EVENTS), "on_submit")

    def test_event_in_section(self):
        self.assertEqual(convert_event_decision_flow_key_to_webhook_key("onSectionPersonalDetail", SECTION_TRIGGER_EVENTS), "on_section_personal_detail")

    def test_no_event_in_any_list(self):
        self.assertEqual(convert_event_decision_flow_key_to_webhook_key("onNewEvent", SECTION_TRIGGER_EVENTS), "on_new_event")
