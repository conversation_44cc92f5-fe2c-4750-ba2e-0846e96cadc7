from crequest.middleware import CrequestMiddleware
from django.apps import apps
from django.conf import settings
from django.template import Context, Template
from django.utils.http import urlencode
from importlib import import_module
from google.oauth2 import service_account
from google.auth.transport.requests import AuthorizedSession, Request
from pydash import objects
from rest_framework.reverse import reverse as rest_framework_reverse
from dynamicform.submodules.appliedform.google_task import create_task
import base64
import json
import datetime
import pytz
import requests
import logging
import time
import re

logger: logging.Logger = logging.getLogger(__name__)

credentials = None
NOTIFICATION_SERVICE_URL = settings.NOTIFICATION_SERVICE_URL

if settings.GOOGLE_SERVICE_ACCOUNT_BASE64_KEY and NOTIFICATION_SERVICE_URL:
  google_service_account_base64_key = settings.GOOGLE_SERVICE_ACCOUNT_BASE64_KEY
  client_config = json.loads(base64.standard_b64decode(google_service_account_base64_key))
  credentials = service_account.IDTokenCredentials.from_service_account_info(
    client_config, target_audience=NOTIFICATION_SERVICE_URL)


def reverse(view_name, query_kwargs=None, *args, **kwargs):
  return rest_framework_reverse(view_name, args=args, kwargs=kwargs, request=CrequestMiddleware.get_request()) + (f"?{'&'.join(['%s=%s' % (k,v) for k,v in query_kwargs.items()])}" if query_kwargs else '')


def build_absolute_uri(url):
  request = CrequestMiddleware.get_request()
  if request:
    return request.build_absolute_uri(url)
  return url


def datetime_to_str(input_datetime, format='%d/%m/%y %I:%M%p'):
  zone = getattr(settings, 'TIMEZONE', 'Asia/Bangkok')
  local_tz = pytz.timezone(zone)
  input_datetime = input_datetime.replace(tzinfo=pytz.utc).astimezone(local_tz)
  return input_datetime.strftime(format)


def current_user():
  request = CrequestMiddleware.get_request()
  if not (request and request.user and request.user.is_authenticated):
    return False
  return request.user


def get_setting(code, formsetting={}):
  if formsetting.get(code, None):
    return formsetting.get(code, None)

  try:
    result = getattr(settings, code)
  except:
    result = None
    
  if apps.is_installed('appsetting'):
    AppSetting = apps.get_model('appsetting', 'AppSetting') # NOSONAR
    try:
      setting = AppSetting.objects.get(code=code)
      result = setting.value
    except AppSetting.DoesNotExist:
      pass

  return result


def set_session(key, value, expiry=False):
  request = CrequestMiddleware.get_request()
  request.session[key] = value
  if not request.user.is_authenticated:
    dynamicform_applied_form_timeout = get_setting('DYNAMICFORM_APPLIED_FORM_TIMEOUT')
  else:
    dynamicform_applied_form_timeout = settings.DASHBOARD_SESSION_TIMEOUT

  request.session.set_expiry(dynamicform_applied_form_timeout)


def get_session(key):
  request = CrequestMiddleware.get_request()
  value = request.session.get(key)
  return value


def client_ip_address():
  request = CrequestMiddleware.get_request()
  if (not request) or (not hasattr(request, 'META')):
    return None

  x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')

  if x_forwarded_for:
    ip = x_forwarded_for.split(',')[0]
  else:
    ip = request.META.get('REMOTE_ADDR')
  return ip


def verify_recaptcha(token):
  request = CrequestMiddleware.get_request()
  url = 'https://www.google.com/recaptcha/api/siteverify'
  body = {
    # Required. The shared key between your site and reCAPTCHA.
    'secret': getattr(settings, 'RECAPTCHA_SECRET_KEY', ''),
    # Required. The user response token provided by the reCAPTCHA client-side integration on your site.
    'response': token,
    'remoteip': client_ip_address(),  # Optional. The user's IP address.
  }
  r = requests.post(url, data=body)
  if r.status_code == 200:
    res = r.json()
    return res['success']
  return False


def import_callable(path_or_callable):
  if hasattr(path_or_callable, '__call__'):
    return path_or_callable
  else:
    assert isinstance(path_or_callable, str)
    package, attr = path_or_callable.rsplit('.', 1)
    return getattr(import_module(package), attr)

def fetching_identity_access_tokens(service_url):
  # current cloud run request another cloud run
  # https://cloud.google.com/run/docs/triggering/https-request
  # https://cloud.google.com/run/docs/securing/service-identity
  host_service_url = service_url.replace('/message', '')
  url = f'http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/identity?audience={host_service_url}' #NOSONAR
  response = requests.get(url, headers={'Metadata-Flavor': 'Google'})
  return response.text
    
def request_with_identity_token(data, queue):
  # t = time.time()
  token = fetching_identity_access_tokens(NOTIFICATION_SERVICE_URL)
  # logger.info('Time fetching_identity_access_tokens {}'.format((time.time() - t)))

  # logger.info('Time create_task request_with_identity_token')
  # t = time.time()
  create_task(NOTIFICATION_SERVICE_URL, data, headers={'Authorization': f'Bearer {token}'}, queue=queue)
  # logger.info('Time create_task {}'.format((time.time() - t)))

def request_with_service_account_info(data, queue):
  request = Request()
  t = time.time()
  if credentials and not credentials.valid:
    credentials.refresh(request=request)
    logger.info('Time credentials.refresh {}'.format((time.time() - t)))
  elif not credentials:
    logger.warn('No credentials for request_with_service_account_info')
    return
  else:
    logger.info('Time not credentials.refresh {}'.format((time.time() - t)))

  # t = time.time()
  # logger.info('Time create_task request_with_service_account_info')
  create_task(NOTIFICATION_SERVICE_URL, data, headers={'Authorization': f'Bearer {credentials.token}'}, queue=queue)
  # logger.info('Time create_task {}'.format((time.time() - t)))


def notification(appliedform, **kwargs):
  if not NOTIFICATION_SERVICE_URL:
    logger.warn('No NOTIFICATION_SERVICE_URL setting')
    return
  channel = settings.SUBMIT_FORM_NOTIFICATION_CHANNEL

  applicationappliedform = appliedform.applicationappliedform_set.first()
  application = applicationappliedform.application
  first_applicationappliedform = application.applicationappliedform_set.order_by('created_at').first()
  first_applied_form = first_applicationappliedform.applied_form

  payload = {
    "embeds": [
      {
        "color": 310974,
        "title": "Dynamicform App Submit Application",
        "fields": [
          {
            "name": "Form",
            "value": appliedform.form.slug,
            "inline": True
          },
          {
            "name": "Appliedform",
            "value": appliedform.slug,
            "inline": True
          },
          {
            "name": "Applied No",
            "value": appliedform.no_format,
            "inline": False
          },
          {
            "name": "Submitted at",
            "value": appliedform.submitted_at.strftime("%Y-%m-%d %H:%M:%S"),
            "inline": False
          },
          {
            "name": "Application",
            "value": application.slug,
            "inline": False
          },
          {
            "name": "Application First Form",
            "value": first_applied_form.form.slug,
            "inline": True
          },
          {
            "name": "Application First Applied",
            "value": first_applied_form.slug,
            "inline": True
          },
          {
            "name": "Application First Applied No",
            "value": first_applied_form.no_format,
            "inline": False
          },
          {
            "name": "App Name",
            "value": settings.APP_NAME,
            "inline": True
          },
          {
            "name": "ENV",
            "value": settings.APP_ENV,
            "inline": True
          }
        ]
      }
    ]
  }

  data = {
    "channel": channel,
    "payload_json": payload
  }

  queue = appliedform.get_form_settings(path=f'queue', default={})
  try:
    request_with_identity_token(data, queue)
    logger.info(f'Time request_with_identity_token')
  except:
    request_with_service_account_info(data, queue)
    logger.info(f'Time request_with_service_account_info')
  return

def objects_get_mapping_value(data, value_path_list):
  result = {}
  for key, value_path in value_path_list.items():
    if key in data:
      result[key] = mapping_value_format(data, value_path)
  return result

def mapping_value_format(data, value_format):
  value_keys = re.findall(r'{{\s*[\w.]+\s*}}', value_format)
  for key in value_keys:
    if value_format.replace(key, '') == '':
      lookup_key = key.replace('{{','').replace('}}','').replace(' ','')
      value = objects.get(data, lookup_key)
      return value
  
  template = Template(value_format)
  context = Context(data)
  value = template.render(context)
  try:
    value = json.loads(value)
  except json.decoder.JSONDecodeError:
    try:
      _value = value.replace('\n', '\\n')
      value = json.loads(_value)
    except json.decoder.JSONDecodeError:
      pass
  except:
    pass
  return value

  

def map_value_to_dest_key(data, mapping_value, key=None):
  if type(mapping_value) == str:
    return mapping_value_format(data, mapping_value)
  elif type(mapping_value) == dict:
    result = {}
    for index, item in mapping_value.items():
      if index == '__all__' and item == True:
        result = {**result, **data}
      elif index == '__all__' and type(item) == str:
        _data = objects.get(data, item, {})
        result = {**result, **_data}
      else:
        result[index] = map_value_to_dest_key(data, item, key=index)
    return result
  elif type(mapping_value) == list:
    result = []
    for item in mapping_value:
      result.append(map_value_to_dest_key(data, item))
    return result
  elif key == '__all__' and mapping_value == True:
    return data

  return mapping_value


def mapping_params(data, params, map_request=True):
  mapping = params
  if map_request:
    mapping = params.get('json') if params.get('json') else params.get('data')
  
  _data = {}
  if mapping:
    _data = map_value_to_dest_key(data, mapping, key=None)

    if not map_request:
      return _data

    if 'json' in params:
      params['json'] = _data
    elif 'data' in params:
      params['data'] = _data

  return params
