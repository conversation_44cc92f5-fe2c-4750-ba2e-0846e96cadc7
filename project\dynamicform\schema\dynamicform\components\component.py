import json
from django.apps import apps
from django.conf import settings
from dynamicform.schema.getapp import GetApp
from dynamicform.validator.validator import validate
from dynamicform.schema.rule import Rule
from dynamicform.util import reverse
from pydash import get


SEARCHABLE_QUESTIONS = settings.DYNAMICFORM_SEARCHABLE_QUESTIONS


class Component():
    # for submodules/appliedform/dynamicform.py Dynamicform
    # design to save in the class
    custom_save_answers = False

    def build(self, key, json, **kwargs):
        """
            Should be called by subclass with 
            json = super().build(key, json, **kwargs)[key]
        """
        prefill_url = get(json, "prefill.url")
        if prefill_url:
            json["prefill"]["url"] = reverse(
                "services:services-get-external", query_kwargs={"url": prefill_url}
            )
        return {key: json}

    def get_partition_answer_value(self, value):
        if value == None:
            return None

        partition_max_length = 191
        return str(value)[:partition_max_length]

    def answer_for_partitioning(self, applied_form, answers):
        '''
            "answer_for_partitioning" : {
                "first_name_abc": "first_name",
                "question_in_builder": "searchable_question"
            }
            return
            {
                "first_name" : {
                    "value": "Kiyotaka",
                    "item": {...}
                },
                "last_name" : {
                    "value": "Ayanokouji",
                    "item": {...}
                }
            },
            {
                "first_name_abc": "first_name",
                "question_in_builder": "last_name"
            }
        '''
        partition_mapping = applied_form.get_form_settings(
            path="answer_for_partitioning", default={})
        if not partition_mapping:
            return answers, {}

        answer_partition = {}
        for question, answer in answers.items():
            question_partition = partition_mapping.get(question, None)
            if not question_partition:
                continue

            answer_partition[question_partition] = answer

        return answer_partition, partition_mapping


    def check_is_encrypt(self, applied_form, question):
        is_encrypt = False
        if encrypt_answers := applied_form.backend_schema.get("encrypt_answers", []):
            if question in encrypt_answers:
                is_encrypt = True

        return is_encrypt

    def save_answers(self, applied_form, answers, step=None, section=None, extra={}, **kwargs):
        questions = list(answers.keys())
        answer_partition, partition_mapping = self.answer_for_partitioning(
            applied_form, answers)
        questions_partition = list(answer_partition.keys())

        Answer = apps.get_model(GetApp.app_default, 'Answer') # NOSONAR
        PartitionAnswer = apps.get_model(GetApp.app_default, 'PartitionAnswer') # NOSONAR
        AnswerLog = apps.get_model(GetApp.app_default, 'AnswerLog') # NOSONAR

        existing_answers = Answer.objects.filter(
            applied_form=applied_form,
            question__in=questions
        )
        existing_searchable_answers = PartitionAnswer.objects.filter(
            application=applied_form.application,
            question__in=questions_partition
        )

        log_section = extra.get('section', applied_form.form.slug)

        # update
        update_answers = []
        update_answers_searchable = []
        answers_user_log = []
        answers_log = []

        for existing_searchable_answer in existing_searchable_answers:
            question = existing_searchable_answer.question
            item = answer_partition.get(question, None)
            value = item.get('value', None)
            is_encrypt = self.check_is_encrypt(applied_form, question)
            if is_encrypt:
                value = Answer.encrypt_value(applied_form, value)
            if existing_searchable_answer.value == value:
                continue
            existing_searchable_answer.value = self.get_partition_answer_value(
                value)
            update_answers_searchable.append(existing_searchable_answer)

        for existing_answer in existing_answers:
            question = existing_answer.question
            item = answers.pop(question, None)
            value = item.get('value', None)
            is_encrypt = self.check_is_encrypt(applied_form, question)

            old_value = existing_answer.value
            old_decrypted_value = existing_answer.decrypted_value

            if existing_answer.is_encrypt and old_decrypted_value == value:
                continue

            if old_value == value:
                continue

            if is_encrypt:
                value = Answer.encrypt_value(applied_form, value)

            answers_user_log.append(
                {
                    'section': log_section,
                    'action': 'Update',
                    'detail': f'Update {question} answer',
                    'old_value': old_value,
                    'new_value': value,
                }
            )
            answers_log.append(
                {
                    'step': step,
                    'section': section,
                    'question': question,
                    'value': value,
                    'action': 'update',
                }
            )
            existing_answer.value = value
            existing_answer.is_encrypt = is_encrypt
            update_answers.append(existing_answer)

        if update_answers:
            Answer.objects.bulk_update(update_answers, ['_value', 'is_encrypt'])
        if update_answers_searchable:
            PartitionAnswer.objects.bulk_update(
                update_answers_searchable, ['value'])

        # ##########################
        # create
        create_answers = []
        create_answers_searchable = []

        for question, item in answers.items():
            value = item.get('value', None)
            if value is None:
                continue
            is_encrypt = self.check_is_encrypt(applied_form, question)
            if is_encrypt:
                value = Answer.encrypt_value(applied_form, value)

            answers_user_log.append(
                {
                    'section': log_section,
                    'action': 'Add',
                    'detail': f'Add {question} answer',
                    'old_value': None,
                    'new_value': value,
                }
            )
            answers_log.append(
                {
                    'step': step,
                    'section': section,
                    'question': question,
                    'value': value,
                    'action': 'create',
                }
            )
            answer = Answer(
                applied_form=applied_form,
                question=question,
                value=value,
                step=step,
                section=section,
                is_encrypt=is_encrypt
            )
            create_answers.append(answer)

            # create partition answer
            question_to_partiton = partition_mapping.get(question, question)
            if question_to_partiton in SEARCHABLE_QUESTIONS:
                answer = PartitionAnswer(
                    application=applied_form.application,
                    question=question_to_partiton,
                    value=self.get_partition_answer_value(value)
                )
                create_answers_searchable.append(answer)

        if create_answers:
            Answer.objects.bulk_create(create_answers)
        if create_answers_searchable:
            PartitionAnswer.objects.bulk_create(create_answers_searchable)

        if answers_user_log:
            self.user_logs(applied_form, logs=answers_user_log)
        if answers_log:
            AnswerLog.log_bulk(
                applied_form,
                logs=answers_log,
                form=applied_form.form
            )

    def save(self, applied_form, question, step, section, answer, extra={}, **kwargs):
        Answer = apps.get_model(GetApp.app_default, 'Answer') # NOSONAR
        is_encrypt = self.check_is_encrypt(applied_form, question)
        searchable = settings.DYNAMICFORM_SEARCHABLE_QUESTIONS
        _, partition_mapping = self.answer_for_partitioning(
            applied_form, {question: answer})
        question_to_partiton = partition_mapping.get(question, question)
        PartitionAnswer = apps.get_model(GetApp.app_default, 'PartitionAnswer') # NOSONAR

        backend_schema = applied_form.form.backend_schema
        if question in backend_schema.get('disabled_save', []):
            return None

        obj = Answer.objects.filter(
            applied_form=applied_form, question=question).first()
        application = applied_form.application

        if not obj:
            obj = Answer.objects.create(
                applied_form=applied_form,
                question=question,
                step=step,
                section=section,
                value=answer,
                is_encrypt=is_encrypt
                )
            answer = obj.value
            partition_answer_value = self.get_partition_answer_value(answer)
            if question_to_partiton in searchable:
                PartitionAnswer.objects.create(
                    application=application,
                    question=question_to_partiton,
                    value=partition_answer_value
                )

            if applied_form.form.log_answers:
                obj.log_answer('create')

            self.user_log(
                applied_form=applied_form,
                section=extra.get('section', applied_form.form.slug),
                action='Add',
                detail=f'Add {question} answer',
                old_value=None,
                new_value=answer,
            )
        else:
            if question in backend_schema.get('disabled_update', []):
                return obj
            
            old_value = obj.value
            old_decrypted_value = obj.decrypted_value

            if obj.is_encrypt and old_decrypted_value == answer:
                return obj

            if old_value == answer:
                return obj

            if is_encrypt:
                answer = Answer.encrypt_value(self.applied_form, answer)
            
            obj.value = answer
            obj.save()
            if applied_form.form.log_answers:
                obj.log_answer('update')
            self.user_log(
                applied_form=applied_form,
                section=extra.get('section', applied_form.form.slug),
                action='Update',
                detail=f'Update {question} answer',
                old_value=old_value,
                new_value=answer
            )
            partition_answer_value = self.get_partition_answer_value(answer)
            if question_to_partiton in searchable:
                PartitionAnswer.objects.filter(
                    application=application,
                    question=question_to_partiton
                ).update(
                    value=partition_answer_value
                )

        return obj

    def user_log(self, applied_form, **kwargs):
        applied_form.application.user_log(
            **kwargs
        )

    def user_logs(self, applied_form, **kwargs):
        applied_form.application.user_logs(
            **kwargs
        )
