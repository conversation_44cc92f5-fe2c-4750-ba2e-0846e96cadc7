import logging
from datetime import datetime

import requests
from data_point.base import CATEGORY_GLOBAL_DATA_SOURCE, EKYCBase
from data_point.exceptions import DataPointException
from data_point.fields.base import DataPointJSONField
from workspace.events import ALL_EKYC_LIVENESS_ITEM_TYPES, ALL_EKYC_DOCUMENT_ITEM_TYPES
from django.conf import settings
from pydash import get
from rest_framework import status


logger: logging.Logger = logging.getLogger(__name__)
DATA_POINT_XENDIT_KTP_API = settings.DATA_POINT_XENDIT_KTP_API
DATA_POINT_XENDIT_API_KEY = settings.DATA_POINT_XENDIT_API_KEY

DATAPOINT_OPTIONS = {
    'is_verified': {
        'label': 'Return whether the user is verified againse Liveness and ID from Xendit',
        'type': 'boolean',
    },
}


class Xendit(EKYCBase):
    full_name = DataPointJSONField(
        label='Name',
        type='string',
        required=True,
    )
    id_card_number = DataPointJSONField(
        label='ID card number',
        type='string',
        required=True,
    )
    birthdate = DataPointJSONField(
        label='Birthdate',
        type='date',
        required=True,
    )
    phone = DataPointJSONField(
        label='Phone',
        type='string',
    )
    email = DataPointJSONField(
        label='Email',
        type='string',
    )
    # AUTO CONNECT WITH Front card
    # ktp_image = DataPointJSONField(
    #     label="BIN",
    #     type="file",
    #     required=True,
    # )
    # AUTO CONNECT WITH Liveness
    # selfie = DataPointJSONField(
    #     label="BIN",
    #     type="file",
    #     required=True,
    # )

    name = 'xendit'
    title = 'Indonesian Identity Credential'
    sub_title = 'Identity Verification'
    description = 'UpPass helps you confirm validity of the KTPs of the end users, verifying details of NIK, name, date and place of birth against the government database.'
    icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/icon_64/Indonesia_ID_64.png'
    option_icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/option-icon_36/Indonesia_ID_36.png'
    data_point_opitons = DATAPOINT_OPTIONS
    category = CATEGORY_GLOBAL_DATA_SOURCE
    required_item_types = [ALL_EKYC_LIVENESS_ITEM_TYPES, ALL_EKYC_DOCUMENT_ITEM_TYPES]
    required_item_types_auto_connect = False

    ekyc = None

    def validate_full_name(self, value):
        self.is_valid_payload_connection(value, field_name='full_name')
        return value

    def validate_id_card_number(self, value):
        self.is_valid_payload_connection(value, field_name='id_card_number')
        return value

    def validate_birthdate(self, value):
        self.is_valid_payload_connection(value, field_name='birthdate')
        return value

    def validate_phone(self, value):
        self.is_valid_payload_connection(value, field_name='phone')
        return value

    def validate_email(self, value):
        self.is_valid_payload_connection(value, field_name='email')
        return value

    def get_birthdate(self):
        birthdate = self.get_payload('birthdate')
        if not birthdate:
            return birthdate
        return datetime.strptime(birthdate, '%Y-%m-%d').strftime('%d-%m-%Y')

    def get_kyc_files(self):
        ekyc = self.get_ekyc()

        # id card
        ktp_url = get(ekyc.frontcard_result, ['gateway_result', 'image_url', 'img'], '')
        ktp_file_content = self.get_file(ktp_url)
        ktp_image = ('ktp_image.jpg', ktp_file_content, 'image/jpg')

        # liveness
        selfie_url = get(ekyc.liveness_result, ['gateway_result', 'image_url', 'img'], '')
        selfie_file_content = self.get_file(selfie_url)
        selfie = ('selfie.jpg', selfie_file_content, 'image/jpg')

        return ktp_image, selfie

    def get_file(self, file_url):
        response = requests.get(file_url)
        return response.content
    
    def set_current_payload(self):
        self.set_payload('full_name')
        self.set_payload('birthdate')
        self.set_payload('id_card_number')
        self.set_payload('phone')
        self.set_payload('email')
        return self.current_payload

    def get_data_result(self, **kwargs):
        name = self.get_payload('full_name')
        id_card_number = self.get_payload('id_card_number')
        birthdate = self.get_birthdate()
        phone = self.get_payload('phone')
        email = self.get_payload('email')

        ktp_image, selfie = self.get_kyc_files()

        data = {
            'name': name,
            'id_card_number': id_card_number,
            'birthdate': birthdate,
        }

        files = {
            'ktp_image': ktp_image,
            'selfie': selfie,
        }

        if phone and phone != '':
            data.update({'phone': phone})

        if email and email != '':
            data.update({'email': email})

        xendit_data = self._request(data=data, files=files)
        result = self.result_datapoint(xendit_data)
        self.set_current_data_point_output(xendit_data)

        return result

    def _request(self, data={}, files={}, *args, **kwargs):
        url = DATA_POINT_XENDIT_KTP_API
        username = DATA_POINT_XENDIT_API_KEY

        response = requests.post(
            url=url,
            data=data,
            files=files,
            auth=(username, ''),
        )

        status_code = response.status_code
        result = response.json()

        logging_context = self.get_logging_context()
        data_point_log_info = {
            'url': url,
            'json': data,
            'context': logging_context,
            'status_code': status_code,
        }
        logger.info(
            f'Xendit {status_code}',
            data_point_log_info
        )
        if not status.is_success(response.status_code):
            raise DataPointException(
                f'Xendit response {response.status_code} : {response.content}',
                data_point_log_info=data_point_log_info
            )

        return result

    def result_datapoint(self, xendit_data):
        return {'is_verified': get(xendit_data, 'is_verified', False)}
