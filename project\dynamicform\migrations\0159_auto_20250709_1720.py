# Generated by Django 5.0.3 on 2025-07-09 10:20

import random
from django.db import migrations, models

def populate_icon_field_sql(apps, schema_editor):
    """
    Fill 'icon' in CustomStatusKey model
    with random values from a predefined array using RunSQL
    """
 
    possible_icons = [
        'lucide:building',
        'lucide:store',
        'lucide:home',
        'lucide:car',
        'lucide:gamepad-2',
        'lucide:palette',
        'lucide:smile',
        'lucide:cake',
        'lucide:globe',
        'lucide:file',
    ]
    case_statements = []
    step = 1.0 / len(possible_icons)
    for i, icon in enumerate(possible_icons):
        lower_bound = i * step
        upper_bound = (i + 1) * step
        case_statements.append(
            f"WHEN CAST(RANDOM() AS REAL) >= {lower_bound} AND CAST(RANDOM() AS REAL) < {upper_bound} THEN '{icon}'"
        )
    case_statements_str = " ".join(case_statements)
    sql = f"""
    UPDATE dynamicform_customstatuskey
    SET icon = CASE
        {case_statements_str}
        ELSE '{possible_icons[0]}'
    END;
    """
    
    schema_editor.execute(sql)

def reverse_populate_icon_field_sql(apps, schema_editor):
    """
    Function to reverse the changes made by populate_icon_field_sql
    by setting the 'icon' field to NULL or a default value.
    """
    sql = """
    UPDATE dynamicform_customstatuskey
    SET icon = NULL;
    """
    schema_editor.execute(sql)


def populate_custom_status_value_color_sql(apps, schema_editor):
    """
    Function to populate the 'color' field in customstatus_value table
    with a predefined set of colors, cycling through them based on the order of records
    within each custom_status_key_id.
    """

    possible_colors = [
        '#116088',
        '#21867F',
        '#997700',
        '#994700',
        '#9F1346',
        '#990D00',
        '#8800CC',
        '#777B8B',
    ]
    num_colors = len(possible_colors)

    case_statements = []
    for i, color in enumerate(possible_colors):
        case_statements.append(
            f"WHEN MOD(ranked.rn, {num_colors}) = {i} THEN '{color}'"
        )
    case_statements_str = " ".join(case_statements)
   
    sql = f"""
    WITH RankedCustomStatus AS (
        SELECT
            id,
            custom_status_key_id,
            ROW_NUMBER() OVER (PARTITION BY custom_status_key_id ORDER BY id) - 1 AS rn
        FROM
            dynamicform_customstatusvalue
    )
    UPDATE dynamicform_customstatusvalue AS t
    SET color = CASE
        {case_statements_str}
        ELSE '{possible_colors[0]}'
    END
    FROM RankedCustomStatus AS ranked
    WHERE t.id = ranked.id;
    """
    
    schema_editor.execute(sql)

def reverse_populate_custom_status_value_color_sql(apps, schema_editor):
    """
    Function to reverse the changes made by populate_custom_status_value_color_sql
    by setting the 'color' field to NULL or a default value.
    """

    sql = """
    UPDATE dynamicform_customstatusvalue
    SET color = NULL;
    """
    schema_editor.execute(sql)

class Migration(migrations.Migration):

    dependencies = [
        ('dynamicform', '0158_customstatuskey_description_customstatuskey_icon_and_more'),
    ]

    operations = [
        migrations.RunPython(populate_icon_field_sql, reverse_populate_icon_field_sql),
        migrations.RunPython(populate_custom_status_value_color_sql, reverse_populate_custom_status_value_color_sql),
    ]
