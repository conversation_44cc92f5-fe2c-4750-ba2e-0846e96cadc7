from rest_framework import status
from helper.test.mixin import APITestsMixin
from dynamicform.models import CustomStatusKey
from django.contrib.auth import get_user_model


User = get_user_model()


class CreateCustomStatus(APITestsMixin):
    fixtures = ['helper/fixtures/simple-flow.json']
    def setUp(self):
        super().setUp()
        self.workspace_owner = User.objects.get(username="<EMAIL>")

    def test_api_can_create_custom_status_key(self):
        # http://localhost:8000/api/workspaces/A8kt2/forms/simple-flow/custom-status-key/
        form_slug = 'simple-flow'
        workspace_slug = 'A8kt2'
        self.client.force_login(self.workspace_owner)

        custom_status_data = {
            "value": "testing",
            "description": "testing description",
            "icon": "lucide:test-tube",
            "options": [
                { "value": "Pass", "color": "#21867F" },
                { "value": "Fail", "color": "#990D00" },
            ]
        }
        response = self.post(f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/",
            data=custom_status_data
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['value'], custom_status_data['value'])
        self.assertEqual(response.data['description'], custom_status_data['description'])
        self.assertEqual(response.data['icon'], custom_status_data['icon'])
        self.assertIsInstance(response.data["options"], list)
        assert len(response.data["options"]) == len(custom_status_data["options"])
        assert "id" in response.data and isinstance(response.data["id"], int)
        assert "priority" in response.data and isinstance(response.data["priority"], int)

    def test_api_can_create_custom_status_key_with_no_options(self):
        # http://localhost:8000/api/workspaces/A8kt2/forms/simple-flow/custom-status-key/
        form_slug = 'simple-flow'
        workspace_slug = 'A8kt2'
        self.client.force_login(self.workspace_owner)
  
        custom_status_data = {
            "value": "testing",
            "description": "testing description",
            "icon": "lucide:test-tube",
        }
        response = self.post(f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/",
            data=custom_status_data
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['value'], custom_status_data['value'])
        self.assertEqual(response.data['description'], custom_status_data['description'])
        self.assertEqual(response.data['icon'], custom_status_data['icon'])
        self.assertIsInstance(response.data["options"], list)
        self.assertEqual(response.data["options"], [])
        assert "id" in response.data and isinstance(response.data["id"], int)
        assert "priority" in response.data and isinstance(response.data["priority"], int)

    def test_api_cannot_create_custom_status_key(self):
        # http://localhost:8000/api/workspaces/A8kt2/forms/simple-flow/custom-status-key/
        form_slug = 'simple-flow'
        workspace_slug = 'A8kt2'
        self.client.force_login(self.workspace_owner)
  
        custom_status_data = {
            "description": "testing description",
            "icon": "lucide:test-tube",
        }
        response = self.post(f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/",
            data=custom_status_data
        )

        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertTrue('value' in response.json)
        
    def test_api_can_create_custom_status_value(self):
        # http://localhost:8000/api/workspaces/A8kt2/forms/simple-flow/custom-status-key/3/
        form_slug = 'simple-flow'
        workspace_slug = 'A8kt2'
        unused_status_id = 3
        self.client.force_login(self.workspace_owner)

        custom_status_value_data = { "value": "status_option", "color": "#21867F", "priority": 3 }
        response = self.post(f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/{unused_status_id}/custom-status-value/",
            data=custom_status_value_data
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['value'], custom_status_value_data['value'])
        self.assertEqual(response.data['color'], custom_status_value_data['color'])
        self.assertEqual(response.data['priority'], custom_status_value_data['priority'])
        self.assertIsInstance(response.data["id"], int)

    def test_api_cannot_create_custom_status_value(self):
        # http://localhost:8000/api/workspaces/A8kt2/forms/simple-flow/custom-status-key/3/
        form_slug = 'simple-flow'
        workspace_slug = 'A8kt2'
        unused_status_id = 3
        self.client.force_login(self.workspace_owner)

        custom_status_value_data = { "value": "status option", "color": "#21867F", "priority": 3 }
        response = self.post(f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/{unused_status_id}/custom-status-value/",
            data=custom_status_value_data
        )

        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertTrue('value' in response.json)