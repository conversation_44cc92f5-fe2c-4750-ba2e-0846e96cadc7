from data_point.base import <PERSON><PERSON><PERSON><PERSON><PERSON>ase
from workspace.events import ALL_EKYC_LIVENESS_ITEM_TYPES
from django.conf import settings
from pydash import get

EKYC_LIVENESS_MAX_ATTEMPT = settings.EKYC_LIVENESS_MAX_ATTEMPT

DATAPOINT_OPTIONS = {
    'is_bright_face': {
        'label': 'The selfie is too bright',
        'type': 'boolean',
    },
    'is_dark_face': {
        'label': 'The selfie is too dark',
        'type': 'boolean',
    },
    'is_blurry_face': {
        'label': 'The selfie is too blurry',
        'type': 'boolean',
    },
    'is_face_out_of_frame': {
        'label': 'The selfie is out of frame',
        'type': 'boolean',
    },
    'is_mask_detected': {
        'label': 'Face obsured by the mask',
        'type': 'boolean',
    },
    'is_live_person': {
        'label': 'Detect Liveness whether the person is live or still image',
        'type': 'boolean',
    },
    'is_eye_close_detected': {
        'label': 'Selfie with eyes closed',
        'type': 'boolean',
    },
    'is_smile_detected': {
        'label': 'Selfie with smiling',
        'type': 'boolean',
    },
    'is_mouth_open_detected': {
        'label': 'Selfie with mouth opened',
        'type': 'boolean',
    },
    'poor_selfie_attempts_count': {
        'label': 'The number of times the selfie is bright_face or dark_face or blurry_face or face_out_of_frame or mask_detected or eye_close_detected or mouth_open_detected in the form',
        'type': 'number',
    },
    'attempts_count': {
        'label': 'The number of the selfie attempts in the form.',
        'type': 'number',
    },
    'fail_attempts_count': {
        'label': 'The number of times the selfie attempts failed in the form.',
        'type': 'number',
    },
    'fraud_attempts_count': {
        'label': 'The number of times the selfie fail liveness detection in the form.',
        'type': 'number',
    },
    'is_ever_hit_max_attempt': {
        'label': 'Has the slugh ever hit max attempts',
        'type': 'boolean',
    },
}


class Liveness(EKYCBase):
    name = 'liveness'
    title = 'Liveness Verification'
    sub_title = ''
    description = 'The Liveness Check API verifies if the customer is genuine, alive, and prevents fraudulent activities through facial recognition technology with reliability and ease.'
    icon = ''
    option_icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/uppass_verification_services_36/Liveness-Verification_36.png'
    option_icon_name = "lucide:user-check"
    data_point_opitons = DATAPOINT_OPTIONS
    required_item_types = [ALL_EKYC_LIVENESS_ITEM_TYPES]
    required_item_types_auto_connect = True

    def get_data_result(self, values_list=[], **kwargs):
        # fmt: off
        result = {}
        ekyc = self.get_ekyc()
        if not ekyc.liveness:
            return {}
        
        liveness_result = ekyc.liveness.result_response
        gateway_result = get(liveness_result, 'gateway_result.result', {})
        lighting_result = self._get_lighting_data(gateway_result)
        attemps_count_result = self._get_attempts_count(ekyc)
        result['is_bright_face'] = self.get_is_bright_face(lighting_result)
        result['is_dark_face'] = self.get_is_dark_face(lighting_result)
        result['is_blurry_face'] = not get(gateway_result, 'blur_detection.result.status', True)
        result['is_face_out_of_frame'] = not get(gateway_result, 'face_out_frame.result.status', True)
        result['is_mask_detected'] = not get(gateway_result, 'mask_detection.result.status', True)
        result['is_live_person'] = self.get_is_live_person(gateway_result)
        result['is_eye_close_detected'] = not get(gateway_result, 'eye_open_detection.result.status', True)
        result['is_smile_detected'] = not get(gateway_result, 'face_smile.result.status', True)
        result['is_mouth_open_detected'] = not get(gateway_result, 'face_mouth.result.status', True)
        result['poor_selfie_attempts_count'] = attemps_count_result['poor_selfie_attempts_count']
        result['attempts_count'] = attemps_count_result['attempts_count']
        result['fail_attempts_count'] = attemps_count_result['fail_attempts_count']
        result['fraud_attempts_count'] = attemps_count_result['fraud_attempts_count']
        result['is_ever_hit_max_attempt'] = attemps_count_result['is_ever_hit_max_attempt']
        # fmt: on

        return result

    def _get_attempts_count(self, ekyc):
        # fmt: off
        result = {
            'poor_selfie_attempts_count': [],
            'attempts_count': [],
            'fail_attempts_count': [],
            'success_attempts_count': [],
            'fraud_attempts_count': [],
            'is_ever_hit_max_attempt': '',
        }
        max_attempt = self.form.get_settings(
            path='ekyc.liveness.max_attempt',
            default=EKYC_LIVENESS_MAX_ATTEMPT,
            secure=True,
        )
        liveness_set = ekyc.liveness_set.exclude(
            status='pending'
        ).only(
            'result_response',
            'status',
        )
        attempts_count = 0
        fail_attempts_count = 0
        success_attempts_count = 0

        for i in liveness_set:
            result_response = i.result_response
            status = i.status
            brightness_status = get(result_response, 'result.brightness.status', True)
            blur_detection_status = get(result_response, 'result.blur_detection.status', True)
            face_out_frame_status = get(result_response, 'result.face_out_frame.status', True)
            mask_detection_status = get(result_response, 'result.mask_detection.status', True)
            eye_open_detection_status = get(result_response, 'result.eye_open_detection.status', True)
            face_mouth_status = get(result_response, 'result.face_mouth.status', True)
            liveness_detection_status = get(result_response, 'result.liveness_detection.status', True)

            result['poor_selfie_attempts_count'].extend(
                [
                    brightness_status,
                    blur_detection_status,
                    face_out_frame_status,
                    mask_detection_status,
                    eye_open_detection_status,
                    face_mouth_status,
                ]
            )
            result['fraud_attempts_count'].append(liveness_detection_status)
            attempts_count += 1

            if status == 'success':
                success_attempts_count += 1
            elif status in {'failed_backend', 'failed_frontend'}:
                fail_attempts_count += 1

        result['poor_selfie_attempts_count'] = result['poor_selfie_attempts_count'].count(False)
        result['attempts_count'] = attempts_count
        result['fail_attempts_count'] = fail_attempts_count
        result['success_attempts_count'] = success_attempts_count
        result['fraud_attempts_count'] = result['fraud_attempts_count'].count(False)

        if (
            result['attempts_count'] == max_attempt
            and result['success_attempts_count'] == 0
        ):
            result['is_ever_hit_max_attempt'] = True
        else:
            result['is_ever_hit_max_attempt'] = False
        # fmt: on

        return result

    def _get_lighting_data(self, gateway_data: dict):
        # fmt: off
        result = {}
        brightness_data = get(gateway_data, 'brightness.raw', {'': {}})
        threshold_data = get(gateway_data, 'brightness.threshold', {})
        image_data = brightness_data.popitem()[-1]
        darkness_l_score = image_data.get('darkness_l_score', 0)
        darkness_median_score = image_data.get('darkness_median_score', 0)
        darkness_l_threshold = threshold_data.get('darkness_l_threshold', 0)
        darkness_median_threshold = threshold_data.get('darkness_median_threshold', 0)
        brightness_l_score = image_data.get('brightness_l_score', 0)
        brightness_median_score = image_data.get('brightness_median_score', 0)
        brightness_l_threshold = threshold_data.get('brightness_l_threshold', 0)
        brightness_median_threshold = threshold_data.get('brightness_median_threshold', 0)
        result['darkness_l_score'] = darkness_l_score
        result['darkness_median_score'] = darkness_median_score
        result['darkness_l_threshold'] = darkness_l_threshold
        result['darkness_median_threshold'] = darkness_median_threshold
        result['brightness_l_score'] = brightness_l_score
        result['brightness_median_score'] = brightness_median_score
        result['brightness_l_threshold'] = brightness_l_threshold
        result['brightness_median_threshold'] = brightness_median_threshold
        # fmt: on

        return result

    def get_is_bright_face(self, brightness_data: dict):
        brightness_l_score = brightness_data['brightness_l_score']
        brightness_median_score = brightness_data['brightness_median_score']
        brightness_l_threshold = brightness_data['brightness_l_threshold']
        brightness_median_threshold = brightness_data['brightness_median_threshold']

        if (
            brightness_l_score >= brightness_l_threshold
            and brightness_median_score <= brightness_median_threshold
        ):
            return True
        else:
            return False

    def get_is_dark_face(self, brightness_data: dict):
        darkness_l_score = brightness_data['darkness_l_score']
        darkness_median_score = brightness_data['darkness_median_score']
        darkness_l_threshold = brightness_data['darkness_l_threshold']
        darkness_median_threshold = brightness_data['darkness_median_threshold']

        if (
            darkness_l_score >= darkness_l_threshold
            and darkness_median_score <= darkness_median_threshold
        ):
            return True
        else:
            return False

    def get_is_live_person(self, gateway_data: dict):
        raw_data = get(gateway_data, 'liveness_detection.raw', {'': {}})
        threshold_data = get(gateway_data, 'liveness_detection.threshold', {})
        image_data = raw_data.popitem()[-1]
        probability = image_data.get('probability', 0)
        liveness_threshold = threshold_data.get('LIVENESS_THRESHOLD', 0)

        if probability > liveness_threshold:
            return True
        else:
            return False
