from rest_framework import status
from helper.test.mixin import APITestsMixin
from dynamicform.models import CustomStatusKey
from django.contrib.auth import get_user_model


User = get_user_model()


class UpdateCustomStatus(APITestsMixin):
    fixtures = ['helper/fixtures/simple-flow.json']
    def setUp(self):
        super().setUp()
        self.workspace_owner = User.objects.get(username="<EMAIL>")

    def test_api_can_batch_update_custom_status_key(self):
        # http://localhost:8000/api/workspaces/A8kt2/forms/simple-flow/custom-status-key/
        form_slug = 'simple-flow'
        workspace_slug = 'A8kt2'
        self.client.force_login(self.workspace_owner)

        custom_status_data = [
            {
                "id": 3,
                "value": "to_test_3",
                "priority": 9,
            },
            {
                "id": 4,
                "value": "to_test_4",
                "priority": 10,
            }
        ]
        response = self.patch(f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/",
            data={"custom_status_keys": custom_status_data}
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data, list)
        self.assertEqual(len(response.data), 2) 
        self.assertEqual(response.data[0]['value'], custom_status_data[0]['value'])
        self.assertEqual(response.data[0]['priority'], custom_status_data[0]['priority'])
        self.assertEqual(response.data[1]['value'], custom_status_data[1]['value'])
        self.assertEqual(response.data[1]['priority'], custom_status_data[1]['priority'])
    
    def test_api_cannot_batch_update_custom_status_key(self):
        # http://localhost:8000/api/workspaces/A8kt2/forms/simple-flow/custom-status-key/
        form_slug = 'simple-flow'
        workspace_slug = 'A8kt2'
        self.client.force_login(self.workspace_owner)

        custom_status_data = [
            {
                "id": 3,
                "value": "to_test_3",
                "priority": 'abc',
            }
        ]
        response = self.patch(f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/",
            data={"custom_status_keys": custom_status_data}
        )

        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertTrue('priority' in response.json)
        self.assertNotEqual(CustomStatusKey.objects.get(id=3).value, 'to_test_3')

    def test_api_can_update_single_custom_status_key(self):
        # http://localhost:8000/api/workspaces/A8kt2/forms/simple-flow/custom-status-key/3/
        form_slug = 'simple-flow'
        workspace_slug = 'A8kt2'
        target_id = 3
        self.client.force_login(self.workspace_owner)

        custom_status_data = {
            "value": "change_test_3",
            "priority": 33,
        }
        
        response = self.patch(f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/{target_id}/",
            data=custom_status_data
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['value'], custom_status_data['value'])
        self.assertEqual(response.data['priority'], custom_status_data['priority']) 
        self.assertEqual(response.data['id'], target_id)
    
    def test_api_cannot_update_single_custom_status_key(self):
        # http://localhost:8000/api/workspaces/A8kt2/forms/simple-flow/custom-status-key/3/
        form_slug = 'simple-flow'
        workspace_slug = 'A8kt2'
        target_id = 3
        self.client.force_login(self.workspace_owner)

        custom_status_data = { "value": "change test 3 shoould not work" }
        
        response = self.patch(f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/{target_id}/",
            data=custom_status_data
        )

        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertTrue('value' in response.json)
    
    def test_api_can_batch_update_custom_status_value(self):
        # http://localhost:8000/api/workspaces/A8kt2/forms/simple-flow/custom-status-key/3/custom-status-value/
        form_slug = 'simple-flow'
        workspace_slug = 'A8kt2'
        target_id = 3
        self.client.force_login(self.workspace_owner)

        custom_status_data = [
            {
                "id": 10,
                "value": "to_test_3_10",
                "priority": 10,
            },
        ]
        response = self.patch(f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/{target_id}/custom-status-value/",
            data={"custom_status_values": custom_status_data}
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data, list)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['value'], custom_status_data[0]['value'])
        self.assertEqual(response.data[0]['priority'], custom_status_data[0]['priority'])

    def test_api_cannot_batch_update_custom_status_value(self):
        # http://localhost:8000/api/workspaces/A8kt2/forms/simple-flow/custom-status-key/3/custom-status-value/
        form_slug = 'simple-flow'
        workspace_slug = 'A8kt2'
        target_id = 99999  # Non-existent target ID
        self.client.force_login(self.workspace_owner)

        custom_status_data = [
            {
                "id": 10,
                "value": "new_value_10",
                "priority": 10
            },
        ]
        response = self.patch(f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/{target_id}/custom-status-value/",
            data={"custom_status_values": custom_status_data}
        )

        self.assertEqual(response.status_code, status.HTTP_302_FOUND)

        
        target_id = 3
        response = self.patch(f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/{target_id}/custom-status-value/",
            data={"custom_status_values": []}
        )
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
   
    def test_api_can_update_single_custom_status_value(self):
        # http://localhost:8000/api/workspaces/A8kt2/forms/simple-flow/custom-status-key/3/custom-status-value/10/
        form_slug = 'simple-flow'
        workspace_slug = 'A8kt2'
        target_custom_status_key_id = 3
        target_custom_status_value_id = 10
        self.client.force_login(self.workspace_owner)

        custom_status_data = {
            "value": "update_to_3_10",
            "priority": 310,
        }
        response = self.patch(
            f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/{target_custom_status_key_id}/custom-status-value/{target_custom_status_value_id}/",
            data=custom_status_data
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['value'], custom_status_data['value'])
        self.assertEqual(response.data['priority'], custom_status_data['priority']) 
    
    def test_api_cannot_update_single_custom_status_value(self):
        # http://localhost:8000/api/workspaces/A8kt2/forms/simple-flow/custom-status-key/3/custom-status-value/10/
        form_slug = 'simple-flow'
        workspace_slug = 'A8kt2'
        target_custom_status_key_id = 3
        target_custom_status_value_id = 10
        self.client.force_login(self.workspace_owner)

        custom_status_data = {
            "value": "update_to_3_10",
            "priority": "310abc",  # Invalid priority
        }
        response = self.patch(
            f"/api/workspaces/{workspace_slug}/forms/{form_slug}/custom-status-key/{target_custom_status_key_id}/custom-status-value/{target_custom_status_value_id}/",
            data=custom_status_data
        )

        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertTrue('priority' in response.json)
        self.assertNotEqual(CustomStatusKey.objects.get(id=target_custom_status_key_id)
            .customstatusvalue_set.get(id=target_custom_status_value_id).value, custom_status_data['value']) 