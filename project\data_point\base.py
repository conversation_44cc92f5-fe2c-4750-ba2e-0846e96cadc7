import logging
from typing import Any
import requests
from decision_flow.helper import AVAILABLE_OPERATIONS
from django.apps import apps
from django.template import Context, Template
from django.utils import timezone
from pydash.objects import get
from rest_framework import serializers
from rest_framework.exceptions import APIException

from data_point.fields.base import DataPoint<PERSON><PERSON><PERSON>ield
from data_point.exceptions import (
    DataPointException,
    DataPointInputValidationError
)
from pydash import set_

CATEGORY_DATA_FROM_SUBMISSION = {
    "name": "data_from_submission",
    "label": "Data From Submission",
}
CATEGORY_GLOBAL_DATA_SOURCE = {
    "name": "global_data_source",
    "label": "Global Data Source",
}
CATEGORY_DATA_TYPE = {
    "name": "data_type",
    "label": "Data Type",
}

logger: logging.Logger = logging.getLogger(__name__)


class DataPointBase(serializers.Serializer):
    name = None
    config = {}
    context_data = {}
    data_point_opitons = {}
    data_point_required_list = {}
    title = None
    sub_title = None
    description = None
    icon = None
    option_icon = None
    option_icon_name = None
    category = CATEGORY_GLOBAL_DATA_SOURCE
    required_item_types = []
    required_item_types_auto_connect = False
    form = None
    form_visible_items_type = []
    data_points_config = {}
    data_point_result_obj = None
    is_multiple_input = False
    is_multiple_input_one_list = False
    current_payload_one_list = {}

    data_point_input = {}
    data_point_output = []
    input_payload = {}
    data_point_input_payload = {}
    current_data_point_output = []
    current_payload = {}
    values_list = []
    recompute = False
    extra_fields = {}

    is_cloning = False
    clone_from_data = {}
    log_path = None
    clone_frontend_schema = {}
    log_current_payload_one_list = []

    def __init__(self, *args, **kwargs):
        super(DataPointBase, self).__init__(*args, **kwargs)
        self.config = kwargs.get("config", {})
        self.context_data = kwargs.get("data", {})
        self.data_point_result_obj = None
        self.current_payload_one_list = {}
        self.current_data_point_output = []
        self.data_point_input = {}
        self.data_point_input_payload = {}
        self.data_point_output = []
        self.values_list = []
        self.current_payload = {}
        self.extra_fields = {}
        self.recompute = self.context.get("recompute", {})

        self.is_cloning = False
        self.clone_from_data = {}
        self.log_current_payload_one_list = []

        self.init_context()

    def init_context(self):
        self.data_points_config = self.context.get("data_points_config", {})
        self.form = self.context.get("form", None)
        self.dynamicform = self.context.get("dynamicform", None)
        if not self.form:
            return
        self.form_visible_items_type = self.form.backend_schema.get("visible_items_type", [])  # fmt: skip

    def get_data_result(self, found=False, result_log={}, **kwargs):
        return {}

    def get_profile(self, **kwargs):
        from dynamicform.submodules.decisionflow.models import FormDatapoint
        form = self.context.get("form", None)
        if form is None:
            raise Exception("form is required in get profile")
        active = kwargs.get("active")
        
        data_connect_list = []
        connect = False

        decision_flow_datapoints = FormDatapoint.objects.filter(
            form=form,
            data_point_name=self.name
        )

        if decision_flow_datapoints:
            connect = True

        for decision_flow_datapoint in decision_flow_datapoints:
            data_connect_list.append({
                "id": decision_flow_datapoint.id,
                "field": decision_flow_datapoint.fields
            })

        if (
            (active == "true" and connect is True)
            or (active == "false" and connect is False)
            or active is None
        ):
            profile = {
                "title": self.title,
                "sub_title": self.sub_title,
                "description": self.description,
                "icon": self.icon,
                "connect": connect,
                "data_point_name": self.name,
                "data_connect": data_connect_list,
                "is_multiple_input": self.is_multiple_input,
            }
            return profile
        return {}

    def set_input_payload(self, **kwargs):
        self.input_payload = self.set_current_payload_list()

        if not self.input_payload:
            self.current_payload = {}
            self.set_current_payload()
            self.input_payload = self.get_current_payload()
            logger.info(f"DataPointBase set_input_payload", {
                "input_payload": self.input_payload
            })

        return self.input_payload
    
    def get_input_payload(self, **kwargs):
        return {}
    
    def set_current_payload(self, **kwargs):
        return self.current_payload

    def get_current_payload(self):
        return self.current_payload.copy()

    def set_current_payload_list(self, **kwargs):
        current_payload_one_list = self.get_pre_current_payload_one_list()
        
        input_payload = []
        log_input_payload = []
        for pre_current_payload in current_payload_one_list:
            self.current_payload_one_list = pre_current_payload
            # reset current_payload
            self.current_payload = {}
            self.set_current_payload()
            _current_payload = self.get_current_payload()
            input_payload.append(_current_payload)
            log_input_payload.append(self.get_log_current_payload(_current_payload))

        logger.info(f"DataPointBase set_current_payload_list", {
            "current_payload_one_list": self.log_current_payload_one_list,
            "input_payload": log_input_payload
        })
        return input_payload

    def prepare_error_result_for_save_db(self, save_result, error_result, error_result_tracking:dict):
        data_point_result_obj = self.get_data_point_result_obj()
        data_point_name = self.name
        if not hasattr(data_point_result_obj, data_point_name):
            return
        result_item = {
            "input": self.input_payload,
            "output": save_result,
            "values_list": self.values_list,
            "result": error_result,
            "timestamp": timezone.now().isoformat(),
        }
        error_result_tracking.update({"error_detail": result_item})
        
    def get_data(self, values_list=[], **kwargs):
        input_payload = self.set_input_payload(**kwargs)
        found = False
        result_log = {}
        if input_payload:
            found, result, result_log = self.get_result_from_input(
                input=input_payload,
                values_list=values_list,
                **kwargs
            )
            if found and not self.recompute:
                return result
        
        # reset payload and output
        self.data_point_output = []
        self.current_data_point_output = []
        self.values_list = values_list

        if self.is_multiple_input_one_list:
            result = self.get_data_multiple_input_one_list(
                values_list=values_list,
                found=found,
                result_log=result_log,
                **kwargs
            )
        elif isinstance(input_payload, list):
            # to handle 1 list 1 payload
            for _current_payload in input_payload:
                self.current_payload = _current_payload
                result = self.get_data_result(
                    values_list=values_list,
                    found=found,
                    result_log=result_log,
                    **kwargs
                )
                self.data_point_output = self.current_data_point_output
        else:
            self.current_payload = input_payload
            result = self.get_data_result(
                values_list=values_list,
                found=found,
                result_log=result_log,
                **kwargs
            )
            self.data_point_output = self.current_data_point_output
        if not kwargs.get('error_result_tracking') :
            self.save_data_point_result(result=result.copy(), found=found, input_payload=input_payload)
        return result

    def allow_option(self, **kwargs):
        return True

    def get_is_connected(self):
        # check pass required_item_types
        for required_entry in self.required_item_types:
            if not isinstance(required_entry, list):
                required_entry = [required_entry]

            if all(item not in self.form_visible_items_type for item in required_entry):
                return False
        
        # check required_item_types auto_connect
        if self.required_item_types_auto_connect:
            return True
        
        # check has found in form_data_point in db
        form_data_point_list_unique = self.context.get("form_data_point_list_unique")
        if self.name in form_data_point_list_unique:
            return True
        
        # check has found in data_points_config
        return self.name in self.data_points_config
    
    def get_data_point_name(self):
        return self.name
    
    def get_label(self):
        return self.title

    def options(self, **kwargs):
        if not self.allow_option():
            return {}

        is_connected = self.get_is_connected()
        options_list = self.get_options(**kwargs)
        # form_data_point = self.context.get("current_form_data_point", {})
        data_point_name = self.get_data_point_name()
        label = self.get_label()
        detail = {
            # "decision_flow_data_point_id": form_data_point.get("id"),
            "data_point_name": data_point_name,
            "label": label,
            "icon": self.option_icon,
            "icon_name": self.option_icon_name,
            "category": self.category,
            "options": options_list,
            "is_connected": is_connected,
        }

        return detail

    def is_enable_data_point_option(self, data_point_option:dict):
        # enable_settings:= [[.or.], .and.]
        enable_settings  = data_point_option.get("enable_settings")
        if not enable_settings:
            return True

        form = self.context.get("form", None)
        if not form:
            return False
        
        # level and
        level_and = []
        for setting_set in enable_settings:
            # level or
            level_or = []
            for setting in setting_set:
                found_or_enable = form.get_settings(
                    path=setting
                )

                level_or.append(found_or_enable)

            # level or :; any true = true
            setting_set_enable = any(level_or)
            level_and.append(setting_set_enable)

        # level and :; all true = true
        return all(level_and)

    def get_options(self, **kwargs):
        result = {}
        for key, item in self.data_point_opitons.items():
            data_point_opiton_type = item.get("type")
            operations = AVAILABLE_OPERATIONS.get(data_point_opiton_type, [])

            if not self.is_enable_data_point_option(item):
                continue

            result.update({
                key: {
                    "label": item.get("label"),
                    "operations": operations,
                    "type": data_point_opiton_type
                }
            })

        return result

    def get_required(self, **kwargs):
        result = {k: v.get_field_detail(**kwargs) for k, v in self.fields.items()}

        return result

    def is_valid_payload_connection(self, value, field_name=""):
        from data_point.data_point import DataPoint
        from data_point.provider import AVILABLE_MANUAL_DATA_POINT
        field = self.fields.get(field_name)

        category = value.get("category")
        payload_data_point = value.get("data_point")

        if not field:
            raise serializers.ValidationError(f"Field {field_name} is not found.")
        
        # check key has category and data point
        if field.required and (not (category and payload_data_point is not None)):
            raise serializers.ValidationError("category and data_point are required.")
        elif not field.required and (not (category and payload_data_point is not None)):
            return {}

        # check not connect itself
        data_point_name = self.context.get("data_point_name", None)
        if category == data_point_name:
            raise serializers.ValidationError("Category can not connect with itself.")

        field_type = field.type
        is_array = field.is_array

        # check category
        if category in AVILABLE_MANUAL_DATA_POINT:
            if category != field_type:
                raise serializers.ValidationError(
                    f"{data_point_name} is required type ({field_type}) not ({category})."
                )
        elif category not in DataPoint.avilable_data_point:
            raise serializers.ValidationError("Category is not found.")
        else:
            # check exists data point payload
            if isinstance(payload_data_point, list) and is_array is False:
                raise serializers.ValidationError("The array type is not allowed")

            category_data_point = DataPoint(category, context=self.context)
            data_point_options = category_data_point.get_options()

            # Handle in dict pattern payload
            if isinstance(payload_data_point, dict):
                # list of data point options from value
                if is_array is False:
                    question_key = payload_data_point.get('value', '')
                    check_exists_data_point = [data_point_options.get(question_key)]
                # list of data point options from value in optional field
                elif is_array is True and field.required is False :
                    filtered_list = [payload for payload in payload_data_point if payload is not None]
                    check_exists_data_point = [data_point_options.get(i.get('value', '')) for i in filtered_list]
                # list of data point options from value in require field
                else: 
                    question_key = payload_data_point
                    check_exists_data_point = [data_point_options.get(i.get('value', '')) for i in payload_data_point]
            # Handle in list with dict pattern
            elif isinstance(payload_data_point, list) and len(payload_data_point) > 0 and isinstance(payload_data_point[0], dict) and 'value' in payload_data_point[0]:
                check_exists_data_point = []
                for payload_info in payload_data_point:
                    if is_array is True and field.required is False and not payload_info.get('value'):
                        continue
                    else:
                        value_info = payload_info.get('value', '')
                        check_exists_data_point.append(data_point_options.get(value_info))
            elif is_array is False:
                check_exists_data_point = [data_point_options.get(payload_data_point)]
            elif is_array is True and field.required is False :
                filtered_list = [payload for payload in payload_data_point if payload is not None]
                check_exists_data_point = [data_point_options.get(i) for i in filtered_list]
            else:
                check_exists_data_point = [data_point_options.get(i) for i in payload_data_point]
            if not all(check_exists_data_point) and field.required is True:
                raise serializers.ValidationError("Data point does not exists.")

            # validate type
            for i in check_exists_data_point:
                
                # Skip None value
                if not i:
                    continue
                
                payload_data_point_type = i.get("type")

                if payload_data_point_type != field_type:
                    message = f"{data_point_name} is required type ({field_type}) not ({payload_data_point_type})."

                    raise serializers.ValidationError(message)

        return value
    
    def get_data_point_result_obj(self):
        from data_point.models import DataPointResult
        application = self.context.get("application")
        if self.data_point_result_obj:
            return self.data_point_result_obj
        self.data_point_result_obj, _ = DataPointResult.objects.get_or_create(
            application=application
        )
        return self.data_point_result_obj

    def get_result_from_input(self, input, values_list=[], **kwargs):
        found = False
        result = {}
        result_log = {}
        self.data_point_result_obj = self.get_data_point_result_obj()

        data_point_result = []
        if hasattr(self.data_point_result_obj, self.name):
            data_point_result = getattr(self.data_point_result_obj, self.name)

        if not data_point_result:
            return found, result, result_log
        elif isinstance(data_point_result, list):
            for i in data_point_result:
                search_inputs = [input] if not isinstance(input, list) else input
                for i in data_point_result:
                    for single_input in search_inputs:
                        if i.get("input") == single_input and i.get("values_list") == values_list:
                            found = True
                            result = i.get("result")
                            result_log = i
                            return found, result, result_log
                
        return found, result, result_log
    
    
    def get_result(self, data_point_name, **kwargs):
        self.data_point_result_obj = self.get_data_point_result_obj()

        if hasattr(self.data_point_result_obj, data_point_name):
            return getattr(self.data_point_result_obj, data_point_name)
        
        return {}

    def set_current_data_point_output(self, data_point_output=None, **kwargs):
        self.current_data_point_output = data_point_output

    def get_save_save_data_point_result_input(self, current_input_payload={}):
        # to handle new format of input_payload
        # input_payload <= {"field_name": {"value": "full name value"}}
        # change to
        # input_payload <= {"field_name": "full name value"}
        input_payload = {}

        def __set_input_payload(payload):
            if not isinstance(payload, dict):
                return
            
            def get_value_data(value):
                if isinstance(value, dict) and "value" in value:
                    return value.get("value")
                else:
                    return value

            for field_name, value in payload.items():
                field = self.fields.get(field_name)
                if field and field.secure_log_mask_string and callable(field.secure_log_mask_string):
                    input_payload.update({
                        field_name: field.secure_log_mask_string(value)
                    })
                elif field and field.is_array or isinstance(value, list):
                    input_payload.update({
                        field_name: [get_value_data(v) for v in value]
                    })
                else:
                    input_payload.update({
                        field_name: get_value_data(value)
                    })

        if isinstance(current_input_payload, list):
            for payload in current_input_payload:
                __set_input_payload(payload)
        else:
            __set_input_payload(current_input_payload)

        return input_payload
    
    def save_data_point_result(self, result, **kwargs):
        data_point_result_obj = self.get_data_point_result_obj()
        data_point_name = self.name
        data_point_data = self.get_data_point_output()

        if not hasattr(data_point_result_obj, data_point_name):
            return
        
        data_point_result_obj.refresh_from_db()
        db_result = getattr(data_point_result_obj, data_point_name, [])

        input_payload = self.get_save_save_data_point_result_input(
            current_input_payload=self.input_payload.copy()
        )

        result_item = {
            "input": input_payload,
            "output": data_point_data,
            "values_list": self.values_list,
            "result": result,
            "timestamp": timezone.now().isoformat(),
        }

        if db_result == {}:
            db_result = []
        elif not isinstance(db_result, list):
            db_result = [db_result]
        
        found_with_input_payload = False
        for _item in db_result:
            if _item.get("input") == input_payload:
                found_with_input_payload = True
                _item.update(result_item)
                break
        
        if not found_with_input_payload:
            db_result.append(result_item)

        setattr(data_point_result_obj, data_point_name, db_result)
        data_point_result_obj.save(update_fields=[data_point_name])

        return data_point_result_obj
    
    def get_logging_context(self):
        """
        eg. self.context
        {
            "config": {
                "payload": {
                "telephone_number": "{{answer.tel_no}}"
                }
            },
            "applied_form": "<AppliedForm: O7QtS7UW (decision-flow)>",
            "application": "<Application: 619 (O7QtS7UW)>",
            "decision_flow": "<DecisionFlow: decision-flow - onSubmit (64)>",
            "current_decision_flow": "<CurrentDecisionFlow: CurrentDecisionFlow object (12)>",
            "answer": {
                "tel_no": "111"
            }
        }
        """
        context = self.context
        return {
            "applied_form": str(get(context, "applied_form", None)),
            "application": str(get(context, "application", None)),
            "decision_flow": str(get(context, "decision_flow", None)),
            "current_decision_flow": str(get(context, "current_decision_flow", None)),
        }

    def log_info(self, message, detail={}):
        detail.update(
            {
                "context": self.get_logging_context(),
            }
        )
        logger.info(message, detail)

    def set_payload(self, field):
        from data_point.provider import AVILABLE_MANUAL_DATA_POINT

        logger.info(f"DataPointBase set_payload", {
            "field": field,
            "current_payload_one_list": self.get_log_current_payload(
                payload=self.current_payload_one_list
            ),
            "context_config": self.context.get("config")
        })
        if self.current_payload_one_list:
            result = self.current_payload_one_list.get(field)
            self.current_payload.update({field:result})
            return result
        
        context = self.context
        payload = get(context, f"config.payload.{field}")
        data_connect_category = get(
            context,
            f"config.data_connect.{field}.category",
        )
        data_connect_data_point = get(
            context,
            f"config.data_connect.{field}.data_point",
        )
        
        # Get answer from value in data_connect_data_point (new version)
        if isinstance(data_connect_data_point, dict) and 'value' in data_connect_data_point:
            data_connect_data_point = get(
                context,
                f"config.data_connect.{field}.data_point.value",
            )

        def _get_result(data_connect_category, payload, context):

            if (
                data_connect_category in AVILABLE_MANUAL_DATA_POINT
                and data_connect_category != "string"
            ):
                result = data_connect_data_point
            elif payload is None:
                result = get(context, f"{data_connect_category}.{data_connect_data_point}", None)
            else:
                template = Template(payload)
                context = Context(context)
                result = template.render(context)

            if result == "None":
                result = None
            
            return result
        
        if isinstance(payload, list):
            result = []
            for payload_item in payload:
                _result = _get_result(data_connect_category, payload_item, context)
                if isinstance(_result, str):
                    _result = _result.strip()
                if _result:
                    result.append(_result)
        else:
            result = _get_result(data_connect_category, payload, context)

        self.current_payload.update({field:result})
        return result

    def validate_payload_value(self, field, payload_value):
        declared_field = self._declared_fields.get(field, None)
        if not hasattr(declared_field, "payload_validation_rule"):
            return
        
        # if not getattr(declared_field, "payload_validation_rule"):
        #     return
        
        error_message = declared_field.validate_payload(field, payload_value)
        if error_message:
            error_message = f"{self.title}: {error_message}: {payload_value}"
            self.current_data_point_output = {
                "input": self.input_payload,
                "output": self.invalid_input_error_result(error_message, DataPointInputValidationError)
            }
            raise DataPointInputValidationError(error_message)

    def get_payload(
            self, 
            field: str, 
            extra_fields_required: bool = False
        ) -> Any:
        
        field_value = self.current_payload.get(field)
        
        if field_value is None:
            self.validate_payload_value(field, payload_value=field_value)
            return None
        
        payload_value = field_value

        self.validate_payload_value(field, payload_value=payload_value)

        if extra_fields_required:
            extra_fields = self.extra_fields.get(field)
            if isinstance(field_value, list):
                payload_value_list = []
                for index, item in enumerate(payload_value):
                    _extra_fields = extra_fields[index] if extra_fields and (index < len(extra_fields)) else None
                    payload_value_list.append({
                        "value": item,
                        "extra_fields": _extra_fields
                    })
                return payload_value_list
            else:
                return {
                    "value": payload_value,
                    "extra_fields": extra_fields
                }
        else:
            return payload_value

    def _data_point_log_response(self, log_json_data=None, *args, **kwargs):
        '''
        Log response from data point
        input data 
        - pipeline_id: extract id of DecisionFlowPipeline from decision_flow_trigger_url(required)
        - url: url of data point (required)
        - status_code: status code of response (required)
        - result: result of response (required)
        - data_point_function_name: name of function that call data point (required)
        - params: params of request (optional)
        - json: json of request (optional)
        '''
        from data_point.models import DataPointLogResult
        url = kwargs.get('url', None)
        status_code = kwargs.get('status_code', None)
        result = kwargs.get('result', None)
        data_point_function_name = kwargs.get('data_point_function_name', None)
        params = kwargs.get('params', None)
        if log_json_data:
            json = log_json_data
        else:
            json = kwargs.get('json', None)
        payload = {
            "params": params,
            "json": json
        }
        logger.info(f"DataPointBase data_point_log_response start")
        data_point_log_result = DataPointLogResult.objects.create(
            decision_flow_pipeline=self.context.get('decision_flow_pipeline'),
            data_source_name=self.name,
            data_point_function=data_point_function_name,
            url=url,
            request_payload=payload,
            result_status_code=status_code,
            raw_result=result
        )
        logger.info(f"DataPointBase data_point_log_response finish", {
            "data_point_log_result_id": data_point_log_result.id
        })

    def _request(self, *args, **kwargs):
        '''
        Request to data point
        input data
        - pipeline_id: extract id of DecisionFlowPipeline from decision_flow_trigger_url(required)
        - url: url of data point (required)
        - data_point_function_name: name of function that call data point (required)
        - params: params of request (optional)
        - json: json of request (optional)
        '''
        status_code = None
        result = None
        error = None
        result_template = {
            "status_code": status_code,
            "result": result,
            "error": error,
        }
        log_json_data = kwargs.pop('log_json_data', None)

        try:
            data_point_function_name = kwargs.pop('data_point_function_name', None)
            response = requests.request(*args, **kwargs)
            status_code = response.status_code
            try:
                result = response.json()
                kwargs['result'] = result
                kwargs['status_code'] = status_code
                kwargs['data_point_function_name'] = data_point_function_name
                self._data_point_log_response(log_json_data=log_json_data, *args, **kwargs)
            except Exception as _:
                result = response.text
                kwargs['result'] = result
                kwargs['status_code'] = status_code
                kwargs['data_point_function_name'] = data_point_function_name
                self._data_point_log_response(log_json_data=log_json_data, *args, **kwargs)

        except Exception as e:
            error = f"{e.__class__.__name__} - {e}"

        finally:
            result_template["status_code"] = status_code
            result_template["result"] = result
            result_template["error"] = error
        print('error ', error)
        return result_template
    
    def get_log_current_payload(self, payload:dict):
        if not isinstance(payload, dict):
            return payload
        log_payload = payload.copy()
        for field_name, value in payload.items():
            field = self.fields.get(field_name)
            if field and field.secure_log_mask_string and callable(field.secure_log_mask_string):
                log_payload.update({
                    field_name: field.secure_log_mask_string(value)
                })
        return log_payload

    def set_log_current_payload_one_list(self, payload:dict):
        payload = self.get_log_current_payload(payload)
        self.log_current_payload_one_list.append(payload)

    def get_pre_current_payload_one_list(self):
        from dynamicform.submodules.decisionflow.models import FormDatapoint
        from data_point.provider import AVILABLE_MANUAL_DATA_POINT

        form = self.context.get("form", None)

        decision_flow_datapoints = FormDatapoint.objects.filter(
            form=form,
            data_point_name=self.name
        )
        payloads = []
        for decision_flow_datapoint in decision_flow_datapoints:
            fields = decision_flow_datapoint.fields
            if not fields:
                continue
            """
            {
                "juristic_id": {
                    "category": "answer",
                    "data_point": "jid_2"
                }
            }
            """
            payload = {}
            def __update_payload_value(field, field_config):
                if isinstance(field_config, list):
                    category = field_config[0].get("category")
                    data_point = [field.get("data_point", None) for field in field_config]
                else :
                    category = field_config.get("category")
                    data_point = field_config.get("data_point")

                # Get DataPointJSONField variable by field to validate "static" type 
                data_point_field: DataPointJSONField | None = self._declared_fields.get(field, None)

                if isinstance(data_point, list):
                    # Set value to data_point list as answer payload if data_point_field type is static (AVILABLE_MANUAL_DATA_POINT).
                    if data_point_field is not None and data_point_field.category in AVILABLE_MANUAL_DATA_POINT:
                        value = data_point
                    else:
                        value_list = []
                        extra_fields_list = []
                        for _data_point in data_point:
                            _extra_fields = {}
                            # Get question key in value (new version) or use directly (older version)
                            if isinstance(_data_point, dict) and "value" in _data_point:
                                question_key = get(_data_point, "value")
                                if _data_point.get('extra_fields') :
                                    _extra_fields = _data_point.get('extra_fields')
                            else:
                                question_key = _data_point

                            _value = get(self.context, f"{category}.{question_key}")

                            value_list.append(_value)
                            extra_fields_list.append(_extra_fields)

                        self.extra_fields.update({
                            field: extra_fields_list
                        })
                        value = value_list

                # Set value to data_point as answer payload if data_point_field type is static (AVILABLE_MANUAL_DATA_POINT).
                elif data_point_field is not None and data_point_field.category in AVILABLE_MANUAL_DATA_POINT:
                    value = get(data_point, "value")
                else:
                    if isinstance(data_point, dict) and "value" in data_point:
                        question_key = get(data_point, "value")
                    else:
                        question_key = data_point
                    value = get(self.context, f"{category}.{question_key}")
                
                payload.update(
                    {field: value}
                )
            
            for field, field_config in decision_flow_datapoint.fields.items():
                __update_payload_value(field, field_config)
                sub_fields = field_config.get("fields" , {})
                if isinstance(sub_fields, list):
                    splited_field_list: dict[str, list] = {}
                    for sub_field in field_config.get("fields"):
                        for key, value in sub_field.items():
                            if key not in splited_field_list:
                                splited_field_list[key] = []
                            splited_field_list[key].append(value)
                    sub_fields = splited_field_list
                for splited_field, splited_fields_config in sub_fields.items():
                    __update_payload_value(splited_field, splited_fields_config)
                """
                {"juristic_id": "1234567890123"}
                """

            if not payload:
                continue

            self.set_log_current_payload_one_list(payload)
            payloads.append(payload)
        
        """
        payloads = [{"juristic_id": "1234567890123"}]
        """
        return payloads
    
    def get_data_multiple_input_one_list(self, result_log=[], **kwargs):
        self.reset_data_result_multiple_input_one_list()
        error_result = None
        for current_payload in self.input_payload:
            # reset data
            self.current_payload = current_payload
            self.current_data_point_output = []

            result_log_item = self.get_result_log_item(result_log, current_payload)
            result = self.get_data_result(
                result_log=result_log_item,
                **kwargs
            )

            # If result from get_data_result from child class is none
            # Do not append as result
            if result is not None :
                # Remove exception_class before setup data_point_output for save database
                result_copy = result.copy()
                result_copy.pop('exception_class', None)
                self.data_point_output.append({
                    "input": self.get_save_save_data_point_result_input(
                        current_input_payload=self.current_payload.copy()
                    ),
                    "result": result_copy,
                    "output": self.current_data_point_output.copy()
                })
            # If got error in result return that result
            if result is not None and 'error' in result.keys():
                error_result = result
        
        self.current_payload = {}
        if error_result is not None and 'error_result_tracking' in kwargs.keys():
            self.prepare_error_result_for_save_db(self.data_point_output, error_result, kwargs['error_result_tracking'])
            return error_result
        return self.get_data_result_multiple_input_one_list()
    
    def get_data_result_multiple_input_one_list(self, **kwargs):
        return {}

    def get_data_point_output(self):
        return self.data_point_output

    def get_result_log_item(self, result_log, current_payload):
        return {}

    def reset_data_result_multiple_input_one_list(self, **kwargs):
        return
    
    def validate_input(self) -> tuple[bool, str | None]:
        ''' this method need to override to custom each validation in providers
        
        Validate input before call API service to reduce unnecessary request.
        
        Args:
            (depend on override method)
        
        Returns:
            tuple: A tuple containing:
                - bool: return True if pass validate orElse return False,
                - str: Error message to display on report page
        '''
        # Return as default in base method
        return True, None
    
    def invalid_input_error_result(
            self, 
            error_message: str | None, 
            exception_class: APIException = DataPointException) -> dict:
        if not error_message:
            error_message = "Something went wrong."
        return {"error": error_message, 'exception_class': exception_class}

    def set_clone(self, log_path, log_key, value):
        # set data for convert
        set_(self.initial_data, log_key, value)
        # set clone_frontend_schema
        set_(self.clone_frontend_schema, f"{log_path}.{log_key}", value)

    def clone(self):
        """
        clone_from_data := {
            "webhooks": {"clone_from_id": "clone_to_id"},
            "custom_status_keys": {"clone_from_id": "clone_to_id"},
            "custom_status_values": {"clone_from_id": "clone_to_id"},
            "integrations": {"clone_from_id": "clone_to_id"},
        }
        log_path := path.to.current.item
        clone_frontend_schema := {frontend schema to override cloned data}
        """
        # self.set_clone(self.log_path, log_key, value)
        return
    
class DynamicformBase(DataPointBase):
    category = CATEGORY_DATA_FROM_SUBMISSION

    def init_context(self):
        from dynamicform.submodules.appliedform.dynamicform import Dynamicform

        self.data_points_config = self.context.get("data_points_config", {})
        self.form = self.context.get("form", None)
        self.applied_form = self.context.get("applied_form", None)
        self.dynamicform = None

        if self.form:
            self.form_visible_items_type = self.form.backend_schema.get("visible_items_type", [])  # fmt: skip

        if self.applied_form and not self.dynamicform:
            if self.applied_form.dynamicform:
                self.dynamicform = self.applied_form.dynamicform
            elif self.form:
                self.dynamicform = Dynamicform(
                    applied_form=self.applied_form,
                    form=self.form,
                )


class EKYCBase(DynamicformBase):
    ekyc = None

    def get_ekyc(self):
        if self.ekyc:
            return self.ekyc

        ref = self.applied_form.slug
        Ekyc = apps.get_model("ekyc", "Ekyc") # NOSONAR
        self.ekyc = Ekyc.objects.filter(ref=ref).first()
        
        if self.ekyc and self.ekyc.liveness:
            self.ekyc.liveness.fetch_report()

        return self.ekyc


class DataTypeBase(DataPointBase):
    category = CATEGORY_DATA_TYPE
    category_name = None
    value_type = None

    def get_is_connected(self):
        return True
