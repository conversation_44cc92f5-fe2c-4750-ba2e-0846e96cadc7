from django.conf import settings as base_settings
from django.core.signals import setting_changed


class Defaults:
    LOCMEMCACHE_ENABLED = True
    LOCMEMCACHE = {}


class Settings(object):
    def __getattr__(self, name):
        res = getattr(base_settings, name, getattr(Defaults, name))
        self.__dict__[name] = res
        return res

settings = Settings()
setting_changed.connect(lambda setting, **kw: settings.__dict__.pop(setting, None), weak=False)
