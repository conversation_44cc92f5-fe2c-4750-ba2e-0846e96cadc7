import json
from django.utils import timezone
from django.conf import settings
from dynamicform.models import Answer, PartitionAnswer
from dynamicform.schema.getapp import GetApp
from dynamicform.submodules.appliedform.util import check_disabled_save
from dynamicform.util import objects_get_mapping_value
from pydash import get


SEARCHABLE_QUESTIONS = settings.DYNAMICFORM_SEARCHABLE_QUESTIONS
SEARCHABLE_DEFAULT = {
    "contact_full_name_first_name": "first_name",
    "contact_full_name_last_name": "last_name",
    "nid": "nid",
    "document_number": "document_number",
    "telephone_otp_address": "mobile_number",
    "email_otp_address_email": "email",
}


def get_country_info_dict():
    # Get country info dict for get country full
    frontend_country_json_path = "dynamicform/helpers/country_information.json"

    country_info_dict: dict | None = None
    try:
        with open(frontend_country_json_path, "r", encoding="utf8") as file:
            country_info_dict = json.load(file)
    except FileNotFoundError:
        print("country_information.json not found")

    return country_info_dict


class DynamicformSave:

    def has_custom_save_in_app(self, item_type):
        has_custom_save = False
        app_class = None
        if not item_type:
            return has_custom_save, app_class

        app_class = GetApp.get_item_app_from_type(item_type)
        if not hasattr(app_class, "custom_save_answers"):
            return has_custom_save, app_class

        if not app_class.custom_save_answers:
            return has_custom_save, app_class

        if hasattr(app_class, "save_answers"):
            has_custom_save = True
            return has_custom_save, app_class

        if hasattr(app_class, "save"):
            has_custom_save = True
            return has_custom_save, app_class

        has_custom_save = False
        return has_custom_save, app_class

    def check_is_encrypt(self, question):
        is_encrypt = False
        if encrypt_answers := self.backend_schema.get("encrypt_answers", []):
            if question in encrypt_answers:
                is_encrypt = True
        return is_encrypt
    
    def save(self, invalid_questions={}):
        allow_save_answers = {}
        app_answers = {}
        app_answers_auto_collect = {}

        # self.answers is input answers
        for question, answer in self.answers.items():
            item = self.backend_schema_items.get(question)
            if not item:
                continue
            if question in invalid_questions:
                continue
            if not self.is_create:
                if check_disabled_save(item, self.answers, self.applied_form, self.backend_schema):
                    continue
            
            item_type = item.get("type", None)
            if not item_type:
                continue

            has_custom_save, app_class = self.has_custom_save_in_app(item_type)
            if has_custom_save:
                _answer = {question: {"value": answer}}
                if item_type not in app_answers:
                    app_answers[item_type] = {"app_class": app_class, "questions": _answer}
                else:
                    app_answers[item_type]["questions"].update(_answer)
            else:
                allow_save_answers.update({question: answer})

            self.collect_auto_compute_questions(question, app_answers_auto_collect)

        if app_answers_auto_collect:
            self.set_auto_compute_value(
                app_answers=app_answers_auto_collect,
                updated_answers=allow_save_answers,
            )

        if allow_save_answers:
            # Add address full answer by custom address
            allow_save_answers = self.custom_address_full_from_address_element(allow_save_answers)
            self.save_answers(answers=allow_save_answers)

        if app_answers:
            self.save_answers_app(app_answers=app_answers)

        timezone_now = timezone.now()
        self.applied_form.step = self.step
        self.applied_form.section = self.section
        self.applied_form.updated_at = timezone_now
        self.application.updated_at = timezone_now

        self.applied_form_update_fields = self.applied_form_update_fields + ["step", "section", "updated_at"]
        self.application_update_fields = self.application_update_fields + ["updated_at"]

    def save_answers(self, answers):
        questions = list(answers.keys())
        answer_partition, partition_mapping = self.answer_for_partitioning(answers)
        questions_partition = list(answer_partition.keys())

        existing_answers = Answer.objects.filter(applied_form=self.applied_form, question__in=questions)
        existing_searchable_answers = PartitionAnswer.objects.filter(
            application=self.application, question__in=questions_partition
        )

        log_section = self.section

        # update
        update_answers = []
        update_answers_searchable = []
        answers_user_log = []
        answers_log = []
        existing_searchable_answers_question = []
        existing_answers_question = []

        for existing_searchable_answer in existing_searchable_answers:
            question = existing_searchable_answer.question
            existing_searchable_answers_question.append(question)
            value = answer_partition.get(question, None)
            is_encrypt = self.check_is_encrypt(question)
            if is_encrypt:
                value = Answer.encrypt_value(self.applied_form, value)
            if existing_searchable_answer.value == value:
                continue
            existing_searchable_answer.value = self.get_partition_answer_value(value)
            update_answers_searchable.append(existing_searchable_answer)

        for existing_answer in existing_answers:
            question = existing_answer.question
            existing_answers_question.append(question)
            value = answers.get(question, None)
            is_encrypt = self.check_is_encrypt(question)

            old_value = existing_answer.value
            old_decrypted_value = existing_answer.decrypted_value

            if existing_answer.is_encrypt and old_decrypted_value == value:
                continue

            if old_value == value:
                continue
            
            if is_encrypt:
                value = Answer.encrypt_value(self.applied_form, value)
            

            answers_user_log.append(
                {
                    "section": log_section,
                    "action": "Update",
                    "detail": f"Update {question} answer",
                    "old_value": old_value,
                    "new_value": value,
                }
            )
            answers_log.append(
                {
                    "step": self.step,
                    "section": self.section,
                    "question": question,
                    "value": value,
                    "action": "update",
                }
            )
            existing_answer.value = value
            existing_answer.is_encrypt = is_encrypt
            update_answers.append(existing_answer)

        if update_answers:
            Answer.objects.bulk_update(update_answers, ["_value", "is_encrypt"])
        if update_answers_searchable:
            PartitionAnswer.objects.bulk_update(update_answers_searchable, ["value"])

        # ##########################
        # create
        create_answers = []
        create_answers_searchable = []

        for question, value in answers.items():
            if value is None:
                continue

            is_encrypt = self.check_is_encrypt(question)
            if is_encrypt:
                value = Answer.encrypt_value(self.applied_form, value)

            if question not in existing_answers_question:
                answer = Answer(
                    applied_form=self.applied_form,
                    question=question,
                    value=value,
                    step=self.step,
                    section=self.section,
                    is_encrypt=is_encrypt,
                )
                create_answers.append(answer)
                answers_user_log.append(
                    {
                        "section": log_section,
                        "action": "Add",
                        "detail": f"Add {question} answer",
                        "old_value": None,
                        "new_value": value,
                    }
                )
                answers_log.append(
                    {
                        "step": self.step,
                        "section": self.section,
                        "question": question,
                        "value": value,
                        "action": "create",
                    }
                )

            # create partition answer
            question_to_partiton = partition_mapping.get(question, question)
            if (question_to_partiton in SEARCHABLE_QUESTIONS) and (
                question_to_partiton not in existing_searchable_answers_question
            ):
                answer = PartitionAnswer(
                    application=self.application,
                    question=question_to_partiton,
                    value=self.get_partition_answer_value(value),
                )
                create_answers_searchable.append(answer)

        if create_answers:
            Answer.objects.bulk_create(
                create_answers,
                update_conflicts=True,
                unique_fields=["applied_form", "question"],
                update_fields=["_value", "is_encrypt"],
            )
        if create_answers_searchable:
            PartitionAnswer.objects.bulk_create(create_answers_searchable)

        self.answers_user_log = answers_user_log
        self.answers_log = answers_log

    def get_backend_schema_items(self) -> dict:
        return getattr(self, "backend_schema_items", {})

    def get_address_answer_to_build_full_address(self, current_allow_save_answers: dict):
        """Find address element and modify result as list of address set from database (old save) and current_allow_save_answers [new save]
        to use for build address full in next step

        Args:
            current_allow_save_answers (dict): Answer from user submit

        Returns:
            list[dict]: List of address set, each address set contain
            * 'fields' value is dict that mapping between base key (key) and form key (value)
            * key-value between base key and answer from 'current_allow_save_answers'
        """
        backend_schema = self.get_backend_schema_items()

        # Get all address_key from each item in backend schema
        # Ex: [ {'address_1': {'type': 'AddressAutofill'}}}, {'address_2': {'type': 'AddressAutofill'}} ]
        all_address_map = {k: v for k, v in backend_schema.items() if get(v, "type") == "AddressAutofill"}

        def get_answer_from_current_or_db(answer_key: str):
            if not answer_key:
                return None

            if current_allow_save_answers.get(answer_key):
                return current_allow_save_answers.get(answer_key)

            answer = Answer.objects.filter(
                applied_form=getattr(self, "applied_form", None), question=answer_key
            ).first()
            if answer:
                return answer.value

            return None

        # Loop all address items
        build_address_map: dict[str, dict[str, dict]] = {}
        for address_item in all_address_map.values():
            # Get field from backend_schema by address_key
            # then add field to use in later step
            fields: dict[str, str] = address_item.get("fields", {})

            # Skip if "full" answer is already filled
            full_question = fields.pop("full")
            if get_answer_from_current_or_db(full_question):
                continue

            # Skip if "country" answer is not valid
            country_question = fields.get("country")
            country = get_answer_from_current_or_db(country_question)
            if not country or type(country) != str:
                continue

            # Loop existing answer to find address value by fields
            answer_set: dict[str, dict] = {}
            for field, question in fields.items():
                answer = get_answer_from_current_or_db(question)

                answer_set[field] = answer

            build_address_map.update({full_question: answer_set})

        return build_address_map

    def custom_address_full_from_address_element(self, current_allow_save_answers: dict) -> dict:
        """Generates a full address from individual address components and updates
        the `current_allow_save_answers` dictionary with the concatenated address.

        Args:
            current_allow_save_answers (dict): A dictionary containing questions and their
                corresponding values that are allowed to be saved.

        Returns:
            dict: The updated dictionary with the full address concatenated and added
            under the corresponding key.
        """
        # Get address list from database and current_allow_save_answers
        build_address_map = self.get_address_answer_to_build_full_address(current_allow_save_answers)

        country_info_dict = get_country_info_dict()

        # Loop 'build_address_map' to create address full for each 'answer_set'
        for full_question, answer_set in build_address_map.items():
            address_dict = {}

            # Define address order by country
            country_full = str(answer_set.get("country")).upper()
            if country_full == "THA":
                address_pattern = (" ").join(
                    [
                        "{address}",
                        "{subdistrict_prefix}{subdistrict}",
                        "{district_prefix}{district}",
                        "จังหวัด{province}",
                        "{zipcode}",
                    ]
                )

                if get(answer_set, "province") == "กรุงเทพมหานคร":
                    address_dict["subdistrict_prefix"] = "แขวง"
                    address_dict["district_prefix"] = "เขต"
                else:
                    address_dict["subdistrict_prefix"] = "ตำบล"
                    address_dict["district_prefix"] = "อำเภอ"
            else:
                address_pattern = (" ").join(
                    [
                        "{address_1_common}",
                        "{address_2_common}",
                        "{subdistrict}",
                        "{district}",
                        "{city_common}",
                        "{zone_common}",
                        "{province}",
                        "{postal_code_common}",
                        "{country_full}",
                    ]
                )

                # Find country full from country code
                if country_info_dict:
                    country_full = country_info_dict[str(answer_set["country"]).upper()]["en"]
                address_dict["country_full"] = country_full

            # Filter address that keep only item that have key in 'address_order'
            # Then concat 'address_dict' with 'address_pattern'
            for key in [
                "address",
                "subdistrict",
                "district",
                "province",
                "zipcode",
                "address_1_common",
                "address_2_common",
                "city_common",
                "zone_common",
                "postal_code_common",
            ]:
                if answer_set.get(key):
                    address_dict[key] = answer_set[key]
                else:
                    address_dict[key] = "-"

            address_full = address_pattern.format(**address_dict)

            # Add address_full base on form_input_question
            current_allow_save_answers[full_question] = address_full

        return current_allow_save_answers

    def get_answer_searchable(self):
        # Not support custom searchable
        # answer_for_partitioning = self.get_form_settings(path="answer_for_partitioning", default=SEARCHABLE_DEFAULT)
        # answer_searchable = SEARCHABLE_DEFAULT.copy()
        # answer_searchable.update(answer_for_partitioning)
        # return answer_searchable
        return {}

    def answer_for_partitioning(self, answers):
        """
        "answer_for_partitioning" : {
            "first_name_abc": "first_name",
            "question_in_builder": "searchable_question"
        }
        return
        {
            "first_name" : {
                "value": "Kiyotaka",
                "item": {...}
            },
            "last_name" : {
                "value": "Ayanokouji",
                "item": {...}
            }
        },
        {
            "first_name_abc": "first_name",
            "question_in_builder": "last_name"
        }
        """
        # encrypt ในนี้ด้วย
        answer_searchable = self.get_answer_searchable()
        answer_partition = {}
        for question, answer in answers.items():
            if question in SEARCHABLE_QUESTIONS:
                answer_partition[question] = answer
                continue

            question_partition = answer_searchable.get(question, None)
            if question_partition:
                answer_partition[question_partition] = answer

        return answer_partition, answer_searchable

    def save_answers_app(self, app_answers, **kwargs):
        """Example app_answers payload
        app_answers = {
            "itemType": {
                "app_class": ItemComponentClass,
                "questions": {"question": "answers"}
            }
        }
        """
        for _, app in app_answers.items():
            app_class = app.get("app_class", None)
            if not app_class:
                continue

            answers = app.get("questions", {})
            if hasattr(app_class, "auto_compute_value"):
                app_class.auto_compute_value(applied_form=self.applied_form, answers=answers, **kwargs)

            if hasattr(app_class, "save_answers"):
                app_class.save_answers(applied_form=self.applied_form, answers=answers, **kwargs)
            elif hasattr(app_class, "save"):
                for question, item in answers.items():
                    answer = item.get("value", None)
                    app_class.save(
                        applied_form=self.applied_form,
                        step=self.step,
                        section=self.section,
                        question=question,
                        answer=answer,
                        **kwargs,
                    )

    def pre_save(self):
        return self.applied_form.pre_save_section(
            frontend_schema=self.frontend_schema, step=self.step, section=self.section, answers=self.answers
        )

    def post_save(self):
        return self.applied_form.post_save_section(
            frontend_schema=self.frontend_schema, step=self.step, section=self.section, answers=self.answers
        )

    def set_up_mapped_answers(self, answers):
        # backend keep answers and init answer
        mapping_init_answers = self.backend_schema.get("mapping_init_answers", {})
        keep_answers = self.backend_schema.get("keep_answers", {})

        mapping_answers_result = {}
        if bool(mapping_init_answers.get("data", {})):
            mapping_answers_result = objects_get_mapping_value(answers, mapping_init_answers.get("data", {}))

        keep_answers_result = {}
        if bool(keep_answers):
            keep_answers_result = objects_get_mapping_value(answers, keep_answers)

        self.answers = {**answers, **mapping_answers_result, **keep_answers_result}

    def collect_auto_compute_questions(self, question, app_answers_auto_collect=None):
        if app_answers_auto_collect is None:
            app_answers_auto_collect = {}

        releted_questions = self.backend_schema.get("releted_questions", {})
        auto_compute_value = self.backend_schema.get("auto_compute_value", {})
        items = self.backend_schema_items

        releted_question = releted_questions.get(question)

        if not releted_question:
            return

        for force_compute_question in releted_question:
            compute_from_questions = auto_compute_value.get(force_compute_question)
            if not compute_from_questions:
                continue

            item = items.get(force_compute_question)
            item_type = item.get("type", "")
            if item_type not in app_answers_auto_collect:
                app_answers_auto_collect[item_type] = {}

            app_answers_auto_collect[item_type].update(
                {
                    force_compute_question: {
                        "value": None,
                        "item": item,
                        "compute_from_questions": compute_from_questions,
                    }
                }
            )
            self.collect_auto_compute_questions(force_compute_question, app_answers_auto_collect)

    def set_auto_compute_value(self, app_answers, updated_answers):
        all_saved_answers = self.get_answers()
        all_saved_answers.update(updated_answers)

        for app_type, answers in app_answers.items():
            item_app = GetApp.get_item_app_from_type(app_type)
            if hasattr(item_app, "auto_compute_value"):
                item_app.auto_compute_value(
                    applied_form=self.applied_form, answers=answers, updated_answers=all_saved_answers
                )

        for _, answers in app_answers.items():
            for question, answer in answers.items():
                updated_answers.update({question: answer.get("value", None)})

    def pre_save_app(self):
        step_name: str = self.step
        section_name: str = self.section
        if not step_name or not section_name:
            return

        step = get(self.backend_schema, ["steps", step_name], {})
        step_app = GetApp.get_step_app(step)

        if not hasattr(step_app, "pre_save"):
            return

        steps_sections_item_types = self.backend_schema.get("steps_sections_item_types", {})
        item_types = get(steps_sections_item_types, [step_name, section_name], {})

        if len(item_types.items()) <= 0:
            return

        return step_app.pre_save(
            self.applied_form,
            item_types=item_types,
            step_name=step_name,
            section_name=section_name,
            current_answers=self.answers,
        )

    def set_default_answers(self):
        # to set default to answers.question when self.answers.question is None
        """
        {
            "value": "system_id",
            "value_type": "string"
        }
        """
        default_values = self.backend_schema.get("default_values", {})
        if not default_values:
            return

        for question, default_config in default_values.items():
            # it has value, so skip the defult
            if self.answers.get(question) is not None:
                continue

            value = default_config.get("value")
            value_type = default_config.get("value_type")

            if value_type == "number" and isinstance(value, str):
                try:
                    value = int(value)
                except ValueError:
                    try:
                        value = float(value)
                    except ValueError:
                        pass
            elif value_type == "string":
                value = str(value)

            self.answers.update({question: value})
