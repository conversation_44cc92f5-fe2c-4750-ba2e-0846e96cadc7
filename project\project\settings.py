"""
Django settings for project project.

Generated by 'django-admin startproject' using Django 2.2.5.

For more information on this file, see
https://docs.djangoproject.com/en/2.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.2/ref/settings/
"""
import base64
import json
import os

from django.conf import global_settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from google.cloud import logging
from google.oauth2 import service_account

from project.utils import get_secret_value
from .dynamicform_searchable_questions import DYNAMICFORM_SEARCHABLE_QUESTIONS_DEFAULT

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = get_secret_value("SECRET_KEY_SM_KEY", "SECRET_KEY")
AES_GCM_SECRET_KEY = get_secret_value("AES_GCM_SECRET_KEY_SM_KEY", "AES_GCM_SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = int(os.environ.get("DEBUG", default=0))

ALLOWED_HOSTS = ["127.0.0.1"] + os.environ.get("CURRENT_HOST", "*").split(",")

CORS_ORIGIN_REGEX_WHITELIST = [
    r"^https?://localhost(:\d+)?$",
    r"^https?://127\.0\.0\.1(:\d+)?$",
    r"^https?://192.168\.2\.\d+(:\d+)?$",
]

SECURE_HSTS_SECONDS = int(os.environ.get("SECURE_HSTS_SECONDS", 3600))

# MEMORY SIZE (2621440 = 2.5 MB)
DATA_UPLOAD_MAX_MEMORY_SIZE = int(os.environ.get("DATA_UPLOAD_MAX_MEMORY_SIZE", default=5242880))
FILE_UPLOAD_MAX_MEMORY_SIZE = int(os.environ.get("FILE_UPLOAD_MAX_MEMORY_SIZE", default=5242880))

DATA_UPLOAD_MAX_NUMBER_FIELDS = int(os.environ.get("DATA_UPLOAD_MAX_NUMBER_FIELDS", default=10240))

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.humanize",
    "django.contrib.postgres",
    # =================================
    "rest_framework",
    "rest_framework.authtoken",
    # "rest_auth",
    "dj_rest_auth",
    "rest_framework_nested",
    "rest_framework_simplejwt.token_blacklist",
    # =================================
    "health_check",
    "health_check.db",
    # =================================
    "crequest",
    "corsheaders",
    "social_django",
    # =================================
    "psqlextra",
    # =================================
    "django_vite",
    # =================================
    "app.apps.AppConfig",
    "applog.apps.ApplogConfig",
    # =================================
    "style.apps.StyleConfig",
    "ekyc.apps.EkycConfig",
    "appsetting.apps.AppSettingConfig",
    "bankstatement.apps.BankStatementConfig",
    "dynamicform.apps.DynamicformConfig",
    "otp.apps.OtpConfig",
    "services.apps.ServicesConfig",
    "webhook.apps.WebhookConfig",
    "workspace.apps.WorkspaceConfig",
    "me.apps.MeConfig",
    "information.apps.InformationConfig",
    "payment_gateway.apps.PaymentGatewayConfig",
    "utility_bill.apps.UtilityBillConfig",
    "smartuploader.apps.SmartUploaderConfig",
    "application_user_log.apps.ApplicationUserLogConfig",
    "credit_system.apps.CreditSystemConfig",
    "data_point.apps.DataPointConfig",
    "known_face.apps.KnownFaceConfig",
    "integration.apps.IntegrationConfig",
    "comply_advantage.apps.ComplyAdvantageConfig",
    "asiaverify.apps.AsiaverifyConfig",
    # =================================
    "loc_mem_cache.apps.LocMemCacheConfig",

]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "app.middleware.RequestSizeLimitMiddleware",
    "app.middleware.CustomHeaderMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    # 'django.middleware.clickjacking.XFrameOptionsMiddleware', # Prevent iframe
    "crequest.middleware.CrequestMiddleware",
    "applog.middleware.LogResponseTimeMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "style.middleware.CssMiddleware",
    "appsetting.middleware.AppSettingMiddleware",
    "django.middleware.locale.LocaleMiddleware",
    "applog.middleware.RequestResponseLogMiddleware",
    "dynamicform.middleware.PageSchemaMiddleware",
    "app.middleware.NoCacheMiddleware",
    "app.middleware.LogUUIDMiddleware",
    "app.middleware.JWTAuthenticationMiddleware",
    "dynamicform.middleware.CheckCanViewFileMiddleware",
    "dynamicform.middleware.SchemaConfigMiddleware",
    "dynamicform.middleware.ObjectMiddleware",
    "dynamicform.middleware.WorkspaceResolverMiddleware",
    "app.auth.social_django.middleware.ExceptionMiddleware",
    "decision_flow.middleware.decision_flow.DecisionFlowMiddleware",
    "workspace.middleware.WorkspaceRolePermissionMiddleware",
    "base.middleware.JSONErrorMiddleware",
]

ROOT_URLCONF = "project.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            os.path.join(BASE_DIR, "public/dist"),
            "app/templates",
            os.path.join(BASE_DIR, "workspace/templates"),
            os.path.join(BASE_DIR, 'project/frontend/public'),
        ],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                "social_django.context_processors.backends",
                "social_django.context_processors.login_redirect",
            ],
            "builtins": [
                "dynamicform.templatetags.application",
                "dynamicform.templatetags.host",
                "dynamicform.templatetags.isoformat",
                "dynamicform.templatetags.language",
                "dynamicform.templatetags.pydash_get",
                "dynamicform.templatetags.replace",
                "dynamicform.templatetags.b64encode",
                "dynamicform.templatetags.b64decode",
                "dynamicform.templatetags.html_unescape",
                "dynamicform.templatetags.bc_year",
                "dynamicform.templatetags.strptime",
                "dynamicform.templatetags.relativedelta",
                "dynamicform.templatetags.json_dumps",
                "dynamicform.templatetags.age",
            ],
        },
    },
]

WSGI_APPLICATION = "project.wsgi.application"
APPEND_SLASH = False
AUTH_USER_MODEL = "app.User"

# Database
# https://docs.djangoproject.com/en/2.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": os.environ.get("SQL_ENGINE", "django.db.backends.sqlite3"),
        "NAME": os.environ.get("SQL_DATABASE", os.path.join(BASE_DIR, "db.sqlite3")),
        "USER": os.environ.get("SQL_USER", "postgres"),
        "PASSWORD": get_secret_value("SQL_PASSWORD_SM_KEY", "SQL_PASSWORD", "password"),
        "HOST": os.environ.get("SQL_HOST", "localhost"),
        "PORT": os.environ.get("SQL_PORT", "5432"),
        "TEST": {
            "NAME": "test_" + os.environ.get("SQL_DATABASE", os.path.join(BASE_DIR, "db.sqlite3")),
        },
    }
}

MULTI_DATABASES = json.loads(get_secret_value("MULTI_DATABASES_SM_KEY", "MULTI_DATABASES", "{}"))
DATABASES.update(MULTI_DATABASES)

# DATABASE_ROUTERS = json.loads(os.environ.get("DATABASE_ROUTERS", '[]'))
DATABASE_MODEL_USE_DB = json.loads(os.environ.get("DATABASE_MODEL_USE_DB", "{}"))

CONN_MAX_AGE = int(os.environ.get("CONN_MAX_AGE", "0"))
DEFAULT_AUTO_FIELD = "django.db.models.AutoField"

PSQLEXTRA_PARTITIONING_MANAGER = 'dynamicform.submodules.search.manager.partitioning.manager'

# Password validation
# https://docs.djangoproject.com/en/2.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
        "OPTIONS": {
            "max_similarity": 1,
        },
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
    {
        "NAME": "app.auth.validators.UppercaseValidator",
    },
    {
        "NAME": "app.auth.validators.LowercaseValidator",
    },
    {
        "NAME": "app.auth.validators.DigitValidator",
    },
    {
        "NAME": "app.auth.validators.SymbolValidator",
    },
]

CLIENT_AUTH_PASSWORD_VALIDATORS = [
    # {
    #     "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    #     "OPTIONS": {
    #         "max_similarity": 1,
    #     },
    # },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    # {
    #     'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    # },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
    {
        "NAME": "app.auth.validators.UppercaseValidator",
    },
    {
        "NAME": "app.auth.validators.LowercaseValidator",
    },
    {
        "NAME": "app.auth.validators.DigitValidator",
    },
    # {
    #     'NAME': 'app.auth.validators.SymbolValidator',
    # },
]

ALLOW_DISPOSABLE_EMAIL = bool(int(os.environ.get("ALLOW_DISPOSABLE_EMAIL", 0)))
ALLOW_FREE_EMAIL = bool(int(os.environ.get("ALLOW_FREE_EMAIL", 1)))
ALLOW_SIGNUP = bool(int(os.environ.get("ALLOW_SIGNUP", 1)))


# Internationalization
# https://docs.djangoproject.com/en/2.2/topics/i18n/

TIME_ZONE = os.environ.get("TIME_ZONE", "Asia/Bangkok")

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.2/howto/static-files/

COMMIT_SHA = os.environ.get("COMMIT_SHA", "COMMIT_SHA")

STATICFILES_DIRS = [os.path.join(BASE_DIR, "public", "dist")]

STATIC_URL_TEMPLATE = os.environ.get("STATIC_URL_TEMPLATE")

if STATIC_URL_TEMPLATE:
    STATIC_URL = STATIC_URL_TEMPLATE.format(COMMIT_SHA=COMMIT_SHA)
else:
    STATIC_URL = "/dist/"

STATIC_ROOT = os.path.join(BASE_DIR, "dist/")

# X-Content-Type-Options: nosniff, To prevent the browser from guessing the content type
SECURE_CONTENT_TYPE_NOSNIFF = True

# Compress and cache static files
# https://whitenoise.evans.io/en/stable/django.html#add-compression-and-caching-support
STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"

SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

REST_FRAMEWORK = {
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": 7,
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
        "payment_gateway.renderers.FormURLEnclosedRenderer",
    ],
    "DEFAULT_PARSER_CLASSES": [
        "rest_framework.parsers.JSONParser",
        "rest_framework.parsers.MultiPartParser",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework.authentication.BasicAuthentication",
        "app.auth.authentication.APITokenAuthentication",
        "app.auth.authentication.TokenAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ),
    "EXCEPTION_HANDLER": "app.exception_handler.app_exception_handler",
    "DATETIME_FORMAT": "iso-8601",
}


REST_AUTH = {
    "USER_DETAILS_SERIALIZER": "app.auth.serializer.user.UserDetailsSerializer",
    "JWT_SERIALIZER_WITH_EXPIRATION": "app.auth.serializer.token.JWTSerializer",
    "PASSWORD_RESET_SERIALIZER": "app.auth.serializer.PasswordResetSerializer",
    "PASSWORD_RESET_CONFIRM_SERIALIZER": "app.auth.serializer.password.PasswordResetConfirmSerializer",
    "PASSWORD_CHANGE_SERIALIZER": "app.auth.serializer.PasswordSerializer",
    
    "LOGIN_SERIALIZER": "app.auth.serializer.login.LoginSerializer",

    "OLD_PASSWORD_FIELD_ENABLED": True,
    "SESSION_LOGIN": True,
    "LOGOUT_ON_PASSWORD_CHANGE": True,

    "USE_JWT": True,
    "JWT_AUTH_RETURN_EXPIRATION": True,
    "JWT_AUTH_COOKIE": "app-auth",
    "JWT_AUTH_REFRESH_COOKIE": "app-refresh-token",
}

# REST_SESSION_LOGIN = False
DASHBOARD_SESSION_TIMEOUT = int(os.environ.get('DASHBOARD_SESSION_TIMEOUT', 3 * 60 * 60))
SIMPLE_JWT = {
    "USER_AUTHENTICATION_RULE": "app.auth.authentication.user_authentication_rule",
    "AUTH_HEADER_TYPES": ("Bearer",),
    "ACCESS_TOKEN_LIFETIME": timezone.timedelta(seconds=int(DASHBOARD_SESSION_TIMEOUT)),
    "REFRESH_TOKEN_LIFETIME": timezone.timedelta(seconds=int(DASHBOARD_SESSION_TIMEOUT)),
    "AUTH_TOKEN_CLASSES": ("app.auth.tokens.AccessToken",),
    "BLACKLIST_AFTER_ROTATION": False,
}
LOGIN_URL = os.environ.get("LOGIN_URL", "/admin/")
LOGIN_RATELIMIT_ENABLE = False #bool(int(os.environ.get("LOGIN_RATELIMIT_ENABLE", 1)))
LOGIN_RATELIMIT = os.environ.get("LOGIN_RATELIMIT", "10/h")
LOGIN_RECAPTCHA_ENABLE = int(os.environ.get("LOGIN_RECAPTCHA_ENABLE", 1))
LOGIN_RECAPTCHA_START_CHECK = int(os.environ.get("LOGIN_RECAPTCHA_START_CHECK", 3))
LOGIN_RECAPTCHA_SECRET_KEY = get_secret_value(
    "LOGIN_RECAPTCHA_SECRET_KEY_SM_KEY",
    "LOGIN_RECAPTCHA_SECRET_KEY"
)

USER_TOKEN_JWE_ENABLE = bool(int(os.environ.get("USER_TOKEN_JWE_ENABLE", 1)))
USER_TOKEN_JWE_ALGORITHM = os.environ.get("USER_TOKEN_JWE_ALGORITHM", "dir")
USER_TOKEN_JWE_ENCRYPTION = os.environ.get("USER_TOKEN_JWE_ENCRYPTION", "A128GCM")
USER_TOKEN_JWE_SECRET_KEY = get_secret_value(
    "USER_TOKEN_JWE_SECRET_KEY_SM_KEY",
    "USER_TOKEN_JWE_SECRET_KEY",
    str(SECRET_KEY)[:16]
)
USER_TOKEN_BLACKLIST_ACCESS_TYPE = bool(int(os.environ.get("USER_TOKEN_BLACKLIST_ACCESS_TYPE", 1)))

######################
# CUSTOM HTTP HEADERS
######################
# CUSTOM_HTTP_HEADERS = [{"header":"X-SOMETHING-1","value":"X-VALUE-1"}]
CUSTOM_HTTP_HEADERS = json.loads(os.environ.get("CUSTOM_HTTP_HEADERS", "[]"))

######################
# LOCK PASSWORD CHANGING FAIL
######################
LOCK_PASSWORD_CHANGING_START_ATTEMPT = int(os.environ.get("LOCK_PASSWORD_CHANGING_START_ATTEMPT", 5))
LOCK_PASSWORD_CHANGING_DURATION = int(os.environ.get("LOCK_PASSWORD_CHANGING_START_ATTEMPT", 600))
LOCK_PASSWORD_CHANGING_MAX_DURATION = int(os.environ.get("LOCK_PASSWORD_CHANGING_MAX_DURATION", 86400))

######################
# APP Settings
######################
APPSETTING_AUTO_INIT = int(os.environ.get("APPSETTING_AUTO_INIT", "0"))

######################
# eKYC
######################
EKYC_ENABLE_SANDBOX_MODE = int(os.environ.get("EKYC_ENABLE_SANDBOX_MODE", 0))

EKYC_APP_ID = os.environ.get("EKYC_APP_ID", "")
EKYC_APP_SECRET = get_secret_value("EKYC_APP_SECRET_SM_KEY", "EKYC_APP_SECRET", False)
EKYC_ENDPOINT = os.environ.get("EKYC_ENDPOINT", "")

EKYC_ENDPOINT_LIVENESS_VERSION = os.environ.get("EKYC_ENDPOINT_LIVENESS_VERSION", "/v4")
EKYC_ENDPOINT_OCR_VERSION = os.environ.get("EKYC_ENDPOINT_OCR_VERSION", "/v3")
EKYC_ENDPOINT_FACE_COMPARE_VERSION = os.environ.get("EKYC_ENDPOINT_FACE_COMPARE_VERSION", "")
EKYC_ENDPOINT_BACK_CARD_VERSION = os.environ.get("EKYC_ENDPOINT_BACK_CARD_VERSION", "/v2")

EKYC_CACHE_TIMEOUT = int(os.environ.get("EKYC_CACHE_TIMEOUT", 60 * 9))
EKYC_UPLOAD_EXPIRE_SECONDS = int(os.environ.get("EKYC_UPLOAD_EXPIRE_SECONDS", 60 * 60 * 24 * 7))
EKYC_RESULT_API_TIMEOUT = int(os.environ.get("EKYC_RESULT_IMAGE_TIMEOUT", 60 * 15))
EKYC_RESULT_IMAGE_TIMEOUT = int(os.environ.get("EKYC_RESULT_IMAGE_TIMEOUT", 60 * 15))

EKYC_LIVENESS_MAX_ATTEMPT = int(os.environ.get("EKYC_LIVENESS_MAX_ATTEMPT", 5))
EKYC_FRONT_CARD_MAX_ATTEMPT = int(os.environ.get("EKYC_FRONT_CARD_MAX_ATTEMPT", 5))
EKYC_BACK_CARD_MAX_ATTEMPT = int(os.environ.get("EKYC_BACK_CARD_MAX_ATTEMPT", 5))

EKYC_FACEACTION_EXPIRE_SECONDS = int(os.environ.get("EKYC_FACEACTION_EXPIRE_SECONDS", 5))
EKYC_FACEACTION_POOL_LIST_DEFAULT = json.loads(
    os.environ.get(
        "EKYC_FACEACTION_POOL_LIST_DEFAULT",
        '["idle", "pool_movements", "idle_background"]',
    )
)
EKYC_UPDATE_STATUS_TO_REF_METHOD = os.environ.get(
    "EKYC_UPDATE_STATUS_TO_REF_METHOD",
    "dynamicform.hook.ekyc_update_application_status",
)

######################
# BANK STATEMENT YODLEE
######################
BANK_STATEMENT_YODLEE_APP_ID = os.environ.get("BANK_STATEMENT_YODLEE_APP_ID", EKYC_APP_ID)
BANK_STATEMENT_YODLEE_APP_SECRET = get_secret_value(
    "BANK_STATEMENT_YODLEE_APP_SECRET_SM_KEY",
    "BANK_STATEMENT_YODLEE_APP_SECRET",
    EKYC_APP_SECRET,
)
BANK_STATEMENT_YODLEE_ENDPOINT = os.environ.get("BANK_STATEMENT_YODLEE_ENDPOINT", EKYC_ENDPOINT)
BANK_STATEMENT_YODLEE_CACHE_TIMEOUT = int(os.environ.get("BANK_STATEMENT_YODLEE_CACHE_TIMEOUT", 60 * 9))
BANK_STATEMENT_YODLEE_ENABLE = os.environ.get("BANK_STATEMENT_YODLEE_ENABLE", "")

######################
# BANK STATEMENT INKREDO (Direct)
######################
BANK_STATEMENT_INKREDO_DIRECT_ENDPOINT = os.environ.get(
    "BANK_STATEMENT_INKREDO_DIRECT_ENDPOINT", "https://api.inkredo.in/v1"
)
BANK_STATEMENT_INKREDO_DIRECT_API_KEY = get_secret_value(
    "BANK_STATEMENT_INKREDO_DIRECT_API_KEY_SM_KEY",
    "BANK_STATEMENT_INKREDO_DIRECT_API_KEY",
    "",
)
BANK_STATEMENT_INKREDO_DIRECT_API_SECRET = get_secret_value(
    "BANK_STATEMENT_INKREDO_DIRECT_API_SECRET_SM_KEY",
    "BANK_STATEMENT_INKREDO_DIRECT_API_SECRET",
    "",
)

######################
# BANK STATEMENT INKREDO (MW)
######################
BANK_STATEMENT_INKREDO_ENDPOINT = os.environ.get(
    "BANK_STATEMENT_INKREDO_ENDPOINT",
    "https://inkredo-api-aqfb5nrmvq-de.a.run.app",
)
BANK_STATEMENT_INKREDO_USERNAME = get_secret_value(
    "BANK_STATEMENT_INKREDO_USERNAME_SM_KEY", "BANK_STATEMENT_INKREDO_USERNAME", ""
)
BANK_STATEMENT_INKREDO_PASSWORD = get_secret_value(
    "BANK_STATEMENT_INKREDO_PASSWORD_SM_KEY", "BANK_STATEMENT_INKREDO_PASSWORD", ""
)
BANK_STATEMENT_RESULT_CLIENT_TIMEOUT = int(os.environ.get("BANK_STATEMENT_RESULT_CLIENT_TIMEOUT", 60 * 5))
BANK_STATEMENT_RESULT_REPORT_TIMEOUT = int(os.environ.get("BANK_STATEMENT_RESULT_REPORT_TIMEOUT", 60 * 15))

######################
# BANK STATEMENT GATEWAY
######################
BANK_STATEMENT_GATEWAY_ENDPOINT = os.environ.get(
    "BANK_STATEMENT_GATEWAY_ENDPOINT",
    "https://document-extraction-gateway-uat-wq6m4hjl2a-uc.a.run.app",
)
BANK_STATEMENT_GATEWAY_USERNAME = get_secret_value(
    "BANK_STATEMENT_GATEWAY_USERNAME_SM_KEY", "BANK_STATEMENT_GATEWAY_USERNAME", ""
)
BANK_STATEMENT_GATEWAY_PASSWORD = get_secret_value(
    "BANK_STATEMENT_GATEWAY_PASSWORD_SM_KEY", "BANK_STATEMENT_GATEWAY_PASSWORD", ""
)
BANK_STATEMENT_GATEWAY_TOKEN_USER_ID = int(os.environ.get("BANK_STATEMENT_GATEWAY_TOKEN_USER_ID", ****************))
BANK_STATEMENT_GATEWAY_TOKEN_COMPANY_NAME = os.environ.get("BANK_STATEMENT_GATEWAY_TOKEN_COMPANY_NAME", "UpPass")
BANK_STATEMENT_GATEWAY_JWT_PASSWORD = get_secret_value(
    "BANK_STATEMENT_GATEWAY_JWT_PASSWORD_SM_KEY", "BANK_STATEMENT_GATEWAY_JWT_PASSWORD", ""
)
BANK_STATEMENT_GATEWAY_ACCEPT_LANGUAGE = json.loads(os.environ.get("BANK_STATEMENT_GATEWAY_ACCEPT_LANGUAGE", '["TH","EN"]'))

######################
# UTILITY BILL GATEWAY
######################
UTILITY_BILL_GATEWAY_ENDPOINT = os.environ.get(
    "UTILITY_BILL_GATEWAY_ENDPOINT",
    BANK_STATEMENT_GATEWAY_ENDPOINT,
)
UTILITY_BILL_GATEWAY_TOKEN_USER_ID = int(
    os.environ.get(
        "UTILITY_BILL_GATEWAY_TOKEN_USER_ID", 
        BANK_STATEMENT_GATEWAY_TOKEN_USER_ID
    )
)
UTILITY_BILL_GATEWAY_TOKEN_COMPANY_NAME = os.environ.get(
    "UTILITY_BILL_GATEWAY_TOKEN_COMPANY_NAME", 
    BANK_STATEMENT_GATEWAY_TOKEN_COMPANY_NAME
)
UTILITY_BILL_GATEWAY_JWT_PASSWORD = get_secret_value(
    "UTILITY_BILL_GATEWAY_JWT_PASSWORD_SM_KEY",
    "UTILITY_BILL_GATEWAY_JWT_PASSWORD", 
    BANK_STATEMENT_GATEWAY_JWT_PASSWORD
)
######################
# SERVICES
######################
SERVICES_APP_ID = os.environ.get("SERVICES_APP_ID", EKYC_APP_ID)
SERVICES_APP_SECRET = get_secret_value("SERVICES_APP_SECRET_SM_KEY", "SERVICES_APP_SECRET", EKYC_APP_SECRET)
SERVICES_ENDPOINT = os.environ.get("SERVICES_ENDPOINT", EKYC_ENDPOINT)
SERVICES_CACHE_TIMEOUT = int(os.environ.get("SERVICES_CACHE_TIMEOUT", 60 * 9))
SERVICES_EXTERNAL_WHITELIST = json.loads(
    os.environ.get(
        "SERVICES_EXTERNAL_WHITELIST",
        json.dumps(
            [
                "https://api.creditok.co",
                "https://identity-api.uppass.io",
                "https://uat.api.creditok.co",
                "https://dev.api.creditok.co",
                "https://us-central1-collective-wisdom.cloudfunctions.net",
            ]
        ),
    )
)
SERVICES_MS_ENDPOINT = os.environ.get("SERVICES_MS_ENDPOINT")
SERVICES_MS_USERNAME = os.environ.get("SERVICES_MS_USERNAME")
SERVICES_MS_PASSWORD = os.environ.get("SERVICES_MS_PASSWORD")
SERVICES_MS_FORMS = json.loads(os.environ.get("SERVICES_MS_FORMS", "{}"))
SERVICES_GET_SECRET_VALUE = "project.utils.get_secret_value"

######################
# EVENT TRACKING
######################
EVENT_TRACKING_ENDPOINT = os.environ.get("EVENT_TRACKING_ENDPOINT", "https://httpbin.org/post")
EVENT_TRACKING_USERNAME = os.environ.get("EVENT_TRACKING_USERNAME", "")
EVENT_TRACKING_PASSWORD = get_secret_value("EVENT_TRACKING_PASSWORD_SM_KEY", "EVENT_TRACKING_PASSWORD", "")
EVENT_TRACKING_ENDPOINT_GET_RESULT = os.environ.get("EVENT_TRACKING_ENDPOINT_GET_RESULT")
EVENT_TRACKING_ENDPOINT_GET_LOG_LIST = os.environ.get("EVENT_TRACKING_ENDPOINT_GET_LOG_LIST")

######################
# WEBHOOK
######################
# Endpoint and authen info use same as service
WEBHOOK_API = os.environ.get("WEBHOOK_API", "/webhook/send")
WEBHOOK_ALLOW_ACTIONS = os.environ.get("WEBHOOK_ALLOW_ACTIONS", "")
WEBHOOK_GET_SECRET_VALUE = "project.utils.get_secret_value"

######################
# PSYCHOMETRICS
######################
PSYCHOMETRIC_SOURCE = os.environ.get("PSYCHOMETRIC_SOURCE", "")

######################
# DYNAMICFORM
######################
APP_NAME = os.environ.get("APP_NAME", "dynamicform-python")
APP_ENV = os.environ.get("APP_ENV", "local")
DEFAULT_FILE_STORAGE = os.environ.get("DEFAULT_FILE_STORAGE", "storages.backends.gcloud.GoogleCloudStorage")
DYNAMICFORM_SUBMIT_URL = os.environ.get("DYNAMICFORM_SUBMIT_URL")
DYNAMICFORM_APPLIED_FORM_TIMEOUT = int(os.environ.get("DYNAMICFORM_APPLIED_FORM_TIMEOUT", 60 * 60 * 2))
DYNAMICFORM_USER_ADMIN_EMAIL = os.environ.get("DYNAMICFORM_USER_ADMIN_EMAIL", False)
DYNAMICFORM_USER_ADMIN_PASSWORD = os.environ.get("DYNAMICFORM_USER_ADMIN_PASSWORD", False)
DYNAMICFORM_USER_STAFF_EMAIL = os.environ.get("DYNAMICFORM_USER_STAFF_EMAIL", False)
DYNAMICFORM_USER_STAFF_PASSWORD = os.environ.get("DYNAMICFORM_USER_STAFF_PASSWORD", False)
DYNAMICFORM_RUNNING_NUMBER_FORMAT = os.environ.get("DYNAMICFORM_RUNNING_NUMBER_FORMAT", "UPPASS{0:{1}{2}d}")
DYNAMICFORM_SUBMIT_ACTION = os.environ.get("DYNAMICFORM_SUBMIT_ACTION", None)
DYNAMICFORM_MS_HOST = os.environ.get("DYNAMICFORM_MS_HOST")
DYNAMICFORM_MS_APP_ID = os.environ.get("DYNAMICFORM_MS_APP_ID")
DYNAMICFORM_MS_APP_SECRECT = get_secret_value("DYNAMICFORM_MS_APP_SECRECT_SM_KEY", "DYNAMICFORM_MS_APP_SECRECT")
DYNAMICFORM_MS_SUBMIT_API = os.environ.get("DYNAMICFORM_MS_SUBMIT_API", "/submit")
DYNAMICFORM_PAGE_TIMEOUT = int(os.environ.get("DYNAMICFORM_PAGE_TIMEOUT", 30 * 60))
DYNAMICFORM_SLUG_RANDOM_CHANCE = int(os.environ.get("DYNAMICFORM_SLUG_RANDOM_CHANCE", 10**6))
DYNAMICFORM_SLUG_MIN_LENGTH = int(os.environ.get("DYNAMICFORM_SLUG_MIN_LENGTH", 8))
DYNAMICFORM_SLUG_FIX_LENGTH = int(os.environ.get("DYNAMICFORM_SLUG_FIX_LENGTH", "-1"))
FORMS_MICROSERVICE = json.loads(os.environ.get("FORMS_MICROSERVICE", "{}"))
DYNAMICFORM_APP_USERNAME = os.environ.get("DYNAMICFORM_APP_USERNAME", "dynamicform-api")
DYNAMICFORM_APP_PASSWORD = get_secret_value("DYNAMICFORM_APP_PASSWORD_SM_KEY", "DYNAMICFORM_APP_PASSWORD")
DYNAMICFORM_ADMIN_USERNAME = os.environ.get("DYNAMICFORM_ADMIN_USERNAME", "admin")
DYNAMICFORM_ADMIN_PASSWORD = get_secret_value("DYNAMICFORM_ADMIN_PASSWORD_SM_KEY", "DYNAMICFORM_ADMIN_PASSWORD")
DYNAMICFORM_DOCUMENT_TIMEOUT = int(os.environ.get("DYNAMICFORM_DOCUMENT_TIMEOUT", 30 * 60))
DYNAMICFORM_AUTO_DELETE_TIMEOUT = int(os.environ.get("DYNAMICFORM_AUTO_DELETE_TIMEOUT", 10))
DYNAMICFORM_DELETE_QUERY_LIMIT = int(os.environ.get("DYNAMICFORM_DELETE_QUERY_LIMIT", 200))
FORM_SETTINGS = json.loads(os.environ.get("FORM_SETTINGS", "{}"))
DYNAMICFORM_EKYC_FACE_COMPARE_SCORE_CRITERIA = json.loads(
    os.environ.get(
        "DYNAMICFORM_EKYC_FACE_COMPARE_SCORE_CRITERIA",
        '{"webhook_extra":{"70":"match","61":"need_review","0":"not_match"},"application_dashboard":{"70":"pass","61":"need_review","0":"fail"} }',
    )
)
DYNAMICFORM_SEARCHABLE_QUESTIONS = json.loads(
    os.environ.get("DYNAMICFORM_SEARCHABLE_QUESTIONS", '[]')
)
if not DYNAMICFORM_SEARCHABLE_QUESTIONS:
    DYNAMICFORM_SEARCHABLE_QUESTIONS = DYNAMICFORM_SEARCHABLE_QUESTIONS_DEFAULT

######################
# GOOGLE CLOUD STORAGE
######################
GOOGLE_SERVICE_ACCOUNT_BASE64_KEY = get_secret_value(
    "GOOGLE_SERVICE_ACCOUNT_BASE64_SM_KEY", "GOOGLE_SERVICE_ACCOUNT_BASE64", False
)
service_account_info = json.loads(base64.standard_b64decode(GOOGLE_SERVICE_ACCOUNT_BASE64_KEY))
if GOOGLE_SERVICE_ACCOUNT_BASE64_KEY:
    cloud_storage_credential = service_account.Credentials.from_service_account_info(service_account_info)
    GS_CREDENTIALS = cloud_storage_credential
    GS_BUCKET_NAME = os.environ.get("GS_BUCKET_NAME", "dynamicform-testing")
    GS_PROJECT_ID = os.environ.get("GS_PROJECT_ID", cloud_storage_credential.project_id)
    FILE_CACHE_EXPIRATION = int(os.environ.get("FILE_CACHE_EXPIRATION", 900))  # - expired in 15 mins (900 sec)
    GS_EXPIRATION = timezone.timedelta(seconds=FILE_CACHE_EXPIRATION)
    GS_UPLOAD_TO = os.environ.get("GS_UPLOAD_TO", APP_NAME + "/" + APP_ENV)


######################
# RECAPTCHA
######################
RECAPTCHA_ENABLED = os.environ.get("RECAPTCHA_ENABLED", False)  # Will be overridden by 'appsettings' anyway
RECAPTCHA_SECRET_KEY = get_secret_value("RECAPTCHA_SECRET_KEY_SM_KEY", "RECAPTCHA_SECRET_KEY")

######################
# USER FILES
######################
MEDIA_URL = "/media/"
MEDIA_ROOT = APP_NAME + "/" + APP_ENV


######################
# Multilingual
######################
LANGUAGE_CODE = os.environ.get("LANGUAGE_CODE", "th")
LOCALE_PATHS = ["locale"]
LANGUAGES_ADDED = [
       ('lo', ('Lao')),
]
LANGUAGES = global_settings.LANGUAGES + LANGUAGES_ADDED

######################
# Admin View
######################
ENABLE_ADMIN = bool(int(os.environ.get("ENABLE_ADMIN", 0)))
ENABLE_ACCESS_ADMIN_PATH = bool(int(os.environ.get("ENABLE_ACCESS_ADMIN_PATH", 1)))

######################
# Speech to Text
######################
SPEECH_TO_TEXT_APP_ID = os.environ.get("SPEECH_TO_TEXT_APP_ID", False)
SPEECH_TO_TEXT_APP_SECRET = get_secret_value(
    "SPEECH_TO_TEXT_APP_SECRET_SM_KEY", "SPEECH_TO_TEXT_APP_SECRET", EKYC_APP_SECRET
)
SPEECH_TO_TEXT_ENDPOINT = os.environ.get("SPEECH_TO_TEXT_ENDPOINT", False)
SPEECH_TO_TEXT_API = os.environ.get("SPEECH_TO_TEXT_API", False)
SPEECH_TO_TEXT_CACHE_TIMEOUT = os.environ.get("SPEECH_TO_TEXT_CACHE_TIMEOUT", 60 * 9)


######################
# LOGGING
######################
if GOOGLE_SERVICE_ACCOUNT_BASE64_KEY:
    logging_credential = service_account.Credentials.from_service_account_info(service_account_info)
    logging_client = logging.Client(project=logging_credential.project_id, credentials=logging_credential)
    logging_client.setup_logging()
else:
    logging_client = logging.Client()

LOG_LEVEL = os.environ.get("LOG_LEVEL", "DEBUG")
LOG_LEVEL_STACKDRIVER = os.environ.get("LOG_LEVEL_STACKDRIVER", "INFO")
LOG_CHANNEL = os.environ.get("LOG_CHANNEL", "stackdriver")
STACKDRIVER_PROJECT_ID = os.environ.get("STACKDRIVER_PROJECT_ID", "credit-ok-testing")
STACKDRIVER_RESOURCE_TYPE = os.environ.get("STACKDRIVER_RESOURCE_TYPE", "global")
STACKDRIVER_RESOURCE_LABEL = json.loads(os.environ.get("STACKDRIVER_RESOURCE_LABEL", "{}"))

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "stackdriver": {
            "class": "applog.stackdriver.LogHandler",
            # 'class': 'google.cloud.logging.handlers.CloudLoggingHandler',
            "client": logging_client,
        }
    },
    "loggers": {
        "": {"handlers": ["stackdriver"], "level": LOG_LEVEL_STACKDRIVER},
        "django": {"handlers": ["stackdriver"], "level": "ERROR"},
    },
}

ALLOW_LOG_URL_NAMES = [
    "form-apply-new",
    "appliedform-info",
    "appliedform-save",
    "appliedform-submit",
    "appliedform-report",
    "appliedform-list",
    "form-detail",
    "speech_to_text",
    # ekyc
    "faceaction",
    "liveness",
    "frontcard",
    "backcard",
    "compare",
    "result",
    "log",
    "answer",
    # bankstatement
    "banks",
    "submit",
    "info",
    "idps",
    "consent",
    "result",
    "cancel",
]

ALLOW_LOG_DATA_NAMES = ["answers"]
DISABLE_LOG_DATA = int(os.environ.get("DISABLE_LOG_DATA", 0))

######################
# Cloud Task
######################
# GOOGLE_SERVICE_ACCOUNT_BASE64 = os.environ.get('GOOGLE_SERVICE_ACCOUNT_BASE64')
QUEUE_PROJECT_ID = os.environ.get("QUEUE_PROJECT_ID")
QUEUE_PATH_LOCATION = os.environ.get("QUEUE_PATH_LOCATION")
QUEUE_ID = os.environ.get("QUEUE_ID")

######################
# NOTIFICATION
######################
NOTIFICATION_SERVICE_URL = os.environ.get("NOTIFICATION_SERVICE_URL")
SUBMIT_FORM_NOTIFICATION_CHANNEL = os.environ.get("SUBMIT_FORM_NOTIFICATION_CHANNEL", "internal_notification")

######################
# OTP
######################
OTP_NOTIFICATION_METHOD = json.loads(os.environ.get("OTP_NOTIFICATION_METHOD", '["otp.util.notification"]'))
OTP_NOTIFICATION_CHANNEL = os.environ.get("OTP_NOTIFICATION_CHANNEL", "internal_notification")
OTP_EMAIL_NOTIFICATION_CHANNEL = os.environ.get("OTP_EMAIL_NOTIFICATION_CHANNEL", "email")
OTP_LIMIT_TIME_REQUEST = int(os.environ.get("OTP_LIMIT_TIME_REQUEST", 60))
OTP_SLACK_WEB_HOOK_URL = os.environ.get("OTP_SLACK_WEB_HOOK_URL")
OTP_DISCORD_WEB_HOOK_URL = os.environ.get("OTP_DISCORD_WEB_HOOK_URL")
OTP_REQUEST_REF_LEVEL = json.loads(
    get_secret_value("OTP_REQUEST_REF_LEVEL_SM_KEY", "OTP_REQUEST_REF_LEVEL", '{"0":{}}')
)
OTP_REQUEST_REF_MAX = int(os.environ.get("OTP_REQUEST_REF_MAX", 5))
OTP_REQUEST_MOBILE_NO_LIMIT_TIME = int(os.environ.get("OTP_REQUEST_MOBILE_NO_LIMIT_TIME", 60))
OTP_HOOK_REQUEST_REF_OVER_MAX = os.environ.get(
    "OTP_HOOK_REQUEST_REF_OVER_MAX",
    "dynamicform.submodules.appliedform.util.otp_hook_request_ref_over_max",
)
OTP_HOOK_CAN_CHECK_STATE_WITH_REF = os.environ.get(
    "OTP_HOOK_CAN_CHECK_STATE_WITH_REF",
    "dynamicform.submodules.appliedform.util.otp_hook_can_check_state_with_ref",
)
OTP_CODE_EXPIRE_IN = int(os.environ.get("OTP_CODE_EXPIRE_IN", 60 * 2))
OTP_VERIFY_MAX_ATTEMPT = int(os.environ.get("OTP_VERIFY_MAX_ATTEMPT", 5))

######################
# LEDGER
######################
LEDGER_ENDPOINT = os.environ.get("LEDGER_ENDPOINT")
LEDGER_GET_TOKEN_URL = os.environ.get("LEDGER_GET_TOKEN_URL")
LEDGER_USERNAME = get_secret_value("LEDGER_USERNAME_SM_KEY", "LEDGER_USERNAME")
LEDGER_PASSWORD = get_secret_value("LEDGER_PASSWORD_SM_KEY", "LEDGER_PASSWORD")


######################
# SOCIAL AUTH LOGIN
# TO Add new credential at https://console.cloud.google.com/apis/credentials
######################
LOGIN_REDIRECT_URL = "/"
AUTHENTICATION_BACKENDS = (
    "social_core.backends.google.GoogleOAuth2",
    "django.contrib.auth.backends.ModelBackend",
    "app.auth.backends.TwoFactorAuthenticationBackend",
)

SOCIAL_AUTH_JSONFIELD_ENABLED = True
SOCIAL_AUTH_PIPELINE = (
    "social_core.pipeline.social_auth.social_details",
    "social_core.pipeline.social_auth.social_uid",
    "social_core.pipeline.social_auth.auth_allowed",
    # "social_core.pipeline.social_auth.social_user",
    "app.auth.social_django.pipeline.social_user",
    "social_core.pipeline.social_auth.associate_by_email",
    "app.auth.social_django.pipeline.allow_only_existing_user",
    "app.auth.social_django.pipeline.allow_only_work_email",
    "social_core.pipeline.user.get_username",
    "social_core.pipeline.user.create_user",
    "social_core.pipeline.social_auth.associate_user",
    "social_core.pipeline.social_auth.load_extra_data",
    "social_core.pipeline.user.user_details",
    "app.auth.social_django.pipeline.get_user",
)
SOCIAL_AUTH_URL_NAMESPACE = "social"
SOCIAL_AUTH_STRATEGY = "app.auth.social_django.strategy.AppStrategy"
SOCIAL_AUTH_GOOGLE_OAUTH2_WHITELISTED_DOMAINS = json.loads(
    os.environ.get("SOCIAL_AUTH_GOOGLE_OPENID_WHITELISTED_DOMAINS", '[]')
)  # to allow some domain account set '["creditok.co"]'
SOCIAL_AUTH_GOOGLE_OAUTH2_KEY = os.environ.get("SOCIAL_AUTH_GOOGLE_OAUTH2_KEY")
SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET = get_secret_value(
    "SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET_KEY_SM_KEY", "SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET"
)
SOCIAL_AUTH_LOGIN_ERROR_URL = "/dashboard/login/"
SOCIAL_AUTH_LOGIN_URL = "/dashboard/login/"
SOCIAL_AUTH_RAISE_EXCEPTIONS = bool(int(os.environ.get("SOCIAL_AUTH_RAISE_EXCEPTIONS", '0')))
SOCIAL_AUTH_ALLOW_ONLY_EXISTING_USER = bool(int(os.environ.get("SOCIAL_AUTH_ALLOW_ONLY_EXISTING_USER", '0')))
SOCIAL_AUTH_GOOGLE_OAUTH2_IGNORE_DEFAULT_SCOPE = True
SOCIAL_AUTH_GOOGLE_OAUTH2_SCOPE = [
    "https://www.googleapis.com/auth/userinfo.email",
    # 'https://www.googleapis.com/auth/userinfo.profile'
]
SOCIAL_AUTH_USERNAME_IS_FULL_EMAIL = True

######################
# Email
######################
EMAIL_HOST = os.environ.get("EMAIL_HOST", "smtp.mailtrap.io")
EMAIL_HOST_USER = os.environ.get("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = get_secret_value("EMAIL_HOST_PASSWORD_SM_KEY", "EMAIL_HOST_PASSWORD")
EMAIL_PORT = os.environ.get("EMAIL_PORT", 2525)
EMAIL_FROM = os.environ.get("EMAIL_FROM", "<EMAIL>")
EMAIL_SUBJECT_PREFIX = os.environ.get("EMAIL_SUBJECT_PREFIX", f"{APP_NAME} : ")
EMAIL_USE_TLS = os.environ.get("EMAIL_USE_TLS", True)
DEFAULT_FROM_EMAIL = os.environ.get("DEFAULT_FROM_EMAIL", "<EMAIL>")

######################
# Utility Bill
######################
UTILITY_BILL_API_URL = os.environ.get(
    "UTILITY_BILL_API_URL", "https://test-utility-bill-extraction-5l4wvfj3fa-an.a.run.app/extract"
)
UTILITY_BILL_API_USERNAME = os.environ.get("UTILITY_BILL_API_USERNAME", "Cdty8xpjF2")
UTILITY_BILL_API_PASSWORD = get_secret_value("UTILITY_BILL_API_PASSWORD_SM_KEY", "UTILITY_BILL_API_PASSWORD")

######################
# CACHES
######################
CACHES = json.loads(os.environ.get('CACHES', '{"default":{"BACKEND":"django.core.cache.backends.locmem.LocMemCache","LOCATION":"cache-app"}}'))

######################
# LOCMEMCACHE
######################
# LOCMEMCACHE_ENABLED = 0
LOCMEMCACHE_ENABLED = bool(int(os.environ.get("LOCMEMCACHE_ENABLED", 0)))
LOCMEMCACHE_DEFAULT = {
    # the set of ops check at loc_mem_cache.patch
    # 'app_label.model_name': {'ops': <set or 'all'>, 'timeout': <seconds>},
    "appsetting.AppSetting": {'ops': 'all', 'timeout': 15 * 60},
    "app.User": {'ops': 'all', 'timeout': 30},
    "dynamicform.formsetting": {'ops': 'all', 'timeout': 15 * 60},
    "dynamicform.Form": {'ops': 'all', 'timeout': 15 * 60},
    "dynamicform.Page": {'ops': 'all', 'timeout': 15 * 60},
    "dynamicform.Locale": {'ops': 'all', 'timeout': 15 * 60},
    "dynamicform.LocalePage": {'ops': 'all', 'timeout': 15 * 60},
}
LOCMEMCACHE = json.loads(os.environ.get("LOCMEMCACHE", "{}"))
LOCMEMCACHE = {**LOCMEMCACHE_DEFAULT, **LOCMEMCACHE}
######################
# CACHE VIEW
######################
CACHE_FORM_VIEWSET_LIST_TIMEOUT = int(os.environ.get("CACHE_FORM_VIEWSET_LIST_TIMEOUT", 15 * 60))

######################
# SESSION
######################
SESSION_ENGINE = os.environ.get('SESSION_ENGINE', 'django.contrib.sessions.backends.db')
SESSION_CACHE_ALIAS = os.environ.get('SESSION_CACHE_ALIAS', 'default')

######################
# COOKIE
######################
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_SAMESITE = os.environ.get('SESSION_COOKIE_SAMESITE', None)

CSRF_COOKIE_SECURE = True
CSRF_COOKIE_SAMESITE = os.environ.get('CSRF_COOKIE_SAMESITE', None)

UUID_COOKIE_SECURE = True
API_TOKEN_COOKIE_SECURE = True

######################
# SILK
######################
ENABLE_SILKY = bool(int(os.environ.get('ENABLE_SILKY', 0)))
SILKY_AUTHENTICATION = bool(int(os.environ.get('SILKY_AUTHENTICATION', 1)))
SILKY_PYTHON_PROFILER = bool(int(os.environ.get('SILKY_PYTHON_PROFILER', 1)))
SILKY_PYTHON_PROFILER_BINARY = bool(int(os.environ.get('SILKY_PYTHON_PROFILER_BINARY', 1)))
SILKY_STORAGE_CLASS = os.environ.get('SILKY_STORAGE_CLASS', 'silk.storage.ProfilerResultStorage')
SILKY_PYTHON_PROFILER_RESULT_PATH = 'dist/silk'
SILKY_DYNAMIC_PROFILING = [
    {
        'module': 'dynamicform.submodules.appliedform.views.appliedform',
        'function': 'AppliedFormViewSet.info'
    },
    {
        'module': 'dynamicform.submodules.appliedform.views.appliedform',
        'function': 'AppliedFormViewSet.report'
    },
    {
        'module': 'dynamicform.submodules.appliedform.views.appliedform',
        'function': 'AppliedFormViewSet.report_answers'
    },
    {
        'module': 'dynamicform.submodules.appliedform.views.appliedform',
        'function': 'AppliedFormViewSet.report_answers_log'
    },
    {
        'module': 'dynamicform.submodules.appliedform.views.appliedform',
        'function': 'AppliedFormViewSet.save'
    },
    {
        'module': 'dynamicform.submodules.appliedform.views.appliedform',
        'function': 'AppliedFormViewSet.submit'
    },
    {
        'module': 'dynamicform.submodules.appliedform.views.appliedform',
        'function': 'AppliedFormViewSet.hook_submit'
    },
    {
        'module': 'dynamicform.submodules.appliedform.views.appliedform',
        'function': 'AppliedFormViewSet.document'
    },
    {
        'module': 'dynamicform.submodules.appliedform.views.appliedform',
        'function': 'AppliedFormViewSet.save_step_section'
    },
    {
        'module': 'dynamicform.submodules.application.views.application',
        'function': 'ApplicationViewSet.retrieve'
    },
    {
        'module': 'dynamicform.submodules.form.views.form',
        'function': 'FormViewSet.create_applied_form'
    },
    {
        'module': 'dynamicform.submodules.page.views',
        'function': 'PageViewSet.answers'
    },
]
SILKY_MAX_REQUEST_BODY_SIZE =  int(os.environ.get('SILKY_MAX_REQUEST_BODY_SIZE', -1))
SILKY_MAX_RESPONSE_BODY_SIZE = int(os.environ.get('SILKY_MAX_RESPONSE_BODY_SIZE', 1024))
SILKY_META = bool(int(os.environ.get('SILKY_META', 1)))
SILKY_ANALYZE_QUERIES = bool(int(os.environ.get('SILKY_ANALYZE_QUERIES', 1)))
SILKY_EXPLAIN_FLAGS = {'format':'JSON', 'costs': True}
SILKY_SENSITIVE_KEYS = {'username', 'api', 'token', 'key', 'secret', 'password', 'signature'}
if ENABLE_SILKY:
    INSTALLED_APPS += [
        'silk'
    ]
    MIDDLEWARE = [
        "silk.middleware.SilkyMiddleware",
    ] + MIDDLEWARE


######################
# STRIPE
######################
STRIPE_SECRET_API_KEY = get_secret_value("STRIPE_SECRET_API_KEY_SM_KEY", "STRIPE_SECRET_API_KEY")
STRIPE_WEBHOOK_SECRET = get_secret_value("STRIPE_WEBHOOK_SECRET_SM_KEY", "STRIPE_WEBHOOK_SECRET")

######################
# CREDIT SYSTEM
######################
ENABLE_CREDIT_SYSTEM = bool(int(os.environ.get('ENABLE_CREDIT_SYSTEM', 0)))
CREDIT_SYSTEM_PACKAGES = json.loads(os.environ.get('CREDIT_SYSTEM_PACKAGES', '{}'))
CREDIT_SYSTEM_PACKAGES_DISCOUNT = int(os.environ.get('CREDIT_SYSTEM_PACKAGES_DISCOUNT', 10))
CREDIT_SYSTEM_LOW_CREDIT_WARNING_EMAIL_LIMIT = int(os.environ.get('CREDIT_SYSTEM_LOW_CREDIT_WARNING_EMAIL_LIMIT', 100))
CREDIT_SYSTEM_EMPTY_CREDIT_WARNING_EMAIL_LIMIT = int(os.environ.get('CREDIT_SYSTEM_EMPTY_CREDIT_WARNING_EMAIL_LIMIT', 100))

######################
# PUBLIC UPLOADER MS
######################
PUBLIC_UPLOADER_MS_ENDPOINT = os.environ.get('PUBLIC_UPLOADER_MS_ENDPOINT')
PUBLIC_UPLOADER_MS_USERNAME = os.environ.get('PUBLIC_UPLOADER_MS_USERNAME')
PUBLIC_UPLOADER_MS_PASSWORD = get_secret_value('PUBLIC_UPLOADER_MS_PASSWORD_SM_KEY', "PUBLIC_UPLOADER_MS_PASSWORD")

######################
# DASHBOARD API
######################
STATISTICS_DASHBOARD_API_ENDPOINT = os.environ.get('STATISTICS_DASHBOARD_API_ENDPOINT')
STATISTICS_DASHBOARD_API_USERNAME = os.environ.get('STATISTICS_DASHBOARD_API_USERNAME')
STATISTICS_DASHBOARD_API_PASSWORD = get_secret_value('STATISTICS_DASHBOARD_API_PASSWORD_SM_KEY', 'STATISTICS_DASHBOARD_API_PASSWORD')
STATISTICS_DASHBOARD_CACHE_EXPIRED = int(os.environ.get('STATISTICS_DASHBOARD_CACHE_EXPIRED', 3600))

######################
# DATA POINT
######################
DATA_POINT_TRUE_TELCO_HOST = os.environ.get('DATA_POINT_TRUE_TELCO_HOST', 'https://asia-southeast1-uppass-uat.cloudfunctions.net/true_telco_api_uat')
DATA_POINT_TRUE_TELCO_USERNAME = os.environ.get('DATA_POINT_TRUE_TELCO_USERNAME', 'true_telco_api_uat')
DATA_POINT_TRUE_TELCO_PASSWORD = get_secret_value('DATA_POINT_TRUE_TELCO_PASSWORD_SM_KEY', 'DATA_POINT_TRUE_TELCO_PASSWORD')
DATA_POINT_TRUE_TELCO_SALT = get_secret_value('DATA_POINT_TRUE_TELCO_SALT_SM_KEY', 'DATA_POINT_TRUE_TELCO_SALT')
DATA_POINT_COMPLY_ADVANTAGE_HOST = os.environ.get('DATA_POINT_COMPLY_ADVANTAGE_HOST', 'https://api.ap.complyadvantage.com')
DATA_POINT_COMPLY_ADVANTAGE_API_KEY = get_secret_value('DATA_POINT_COMPLY_ADVANTAGE_SM_KEY', 'DATA_POINT_COMPLY_ADVANTAGE_API_KEY')
DATA_POINT_COMPLY_ADVANTAGE_COMPANY_FILTER = json.loads(os.environ.get('DATA_POINT_COMPLY_ADVANTAGE_COMPANY_FILTER', '{}')) # use in business_aml
DATA_POINT_COMPLY_ADVANTAGE_PERSON_FILTER = json.loads(os.environ.get('DATA_POINT_COMPLY_ADVANTAGE_PERSON_FILTER', '{}')) # use in business_aml
DATA_POINT_SEON_HOST = os.environ.get('DATA_POINT_SEON_HOST', 'https://api.seon.io')
DATA_POINT_SEON_API_KEY = get_secret_value('DATA_POINT_SEON_SM_KEY', 'DATA_POINT_SEON_API_KEY')
DATA_POINT_BRANKAS_HOST = os.environ.get('DATA_POINT_BRANKAS_HOST', 'https://statement.sandbox.bnk.to')
DATA_POINT_BRANKAS_API_KEY = get_secret_value('DATA_POINT_BRANKAS_SM_KEY', 'DATA_POINT_BRANKAS_API_KEY')
DATA_POINT_XENDIT_KTP_API = os.environ.get('DATA_POINT_XENDIT_KTP_API', 'https://api.iluma.ai/v1/identity/id_card_verifications')
DATA_POINT_XENDIT_API_KEY = get_secret_value('DATA_POINT_XENDIT_API_KEY_SM_KEY', 'DATA_POINT_XENDIT_API_KEY')
DATA_POINT_DBD_HOST = os.environ.get('DATA_POINT_DBD_HOST', 'https://dbd-query-uat-***********.us-central1.run.app')
DATA_POINT_DBD_USERNAME = os.environ.get('DATA_POINT_DBD_USERNAME', 'dbd_query_user')
DATA_POINT_DBD_PASSWORD = get_secret_value('DATA_POINT_DBD_PASSWORD_SM_KEY', 'DATA_POINT_DBD_PASSWORD')
DATA_POINT_IPSTACK_HOST = os.environ.get('DATA_POINT_IPSTACK_HOST', 'https://api.ipstack.com')
DATA_POINT_IPSTACK_API_KEY = get_secret_value('DATA_POINT_IPSTACK_API_KEY_SM_KEY', 'DATA_POINT_IPSTACK_API_KEY')
DATA_POINT_IPSTACK_API_KEY_TYPE = os.environ.get('DATA_POINT_IPSTACK_API_KEY_TYPE', 'free') # free or paid
DATA_POINT_CREDEN_HOST = os.environ.get('DATA_POINT_CREDEN_HOST', 'https://api2.hjkl.ninja/get_uat_shareholder_uppass')
DATA_POINT_CREDEN_API_KEY = get_secret_value('DATA_POINT_CREDEN_API_KEY_SM_KEY', 'DATA_POINT_CREDEN_API_KEY')
# DATA_POINT_KNOWN_FACE_HOST = os.environ.get('DATA_POINT_KNOWN_FACE_HOST', 'https://dev.api.creditok.co')
# DATA_POINT_KNOWN_FACE_APP_ID = os.environ.get('DATA_POINT_KNOWN_FACE_APP_ID', '1550060109')
# DATA_POINT_KNOWN_FACE_APP_SECRET = get_secret_value('DATA_POINT_KNOWN_FACE_APP_SECRET_SM_KEY', 'DATA_POINT_KNOWN_FACE_APP_SECRET')
DATA_POINT_KNOWN_FACE_HOST = EKYC_ENDPOINT
DATA_POINT_KNOWN_FACE_APP_ID = EKYC_APP_ID
DATA_POINT_KNOWN_FACE_APP_SECRET = EKYC_APP_SECRET
DATA_POINT_COJ_HOST = os.environ.get('DATA_POINT_COJ_HOST', 'https://coj-query-taiwan-region-tz7upncgja-de.a.run.app')
DATA_POINT_COJ_USERNAME = os.environ.get('DATA_POINT_COJ_USERNAME', 'coj_query_user')
DATA_POINT_COJ_PASSWORD = get_secret_value('DATA_POINT_COJ_PASSWORD_SM_KEY', 'DATA_POINT_COJ_PASSWORD')
DATA_POINT_DAP_HOST = os.environ.get('DATA_POINT_DAP_HOST', 'https://dap-microservice-438815635558.asia-east1.run.app')
DATA_POINT_DAP_USERNAME = os.environ.get('DATA_POINT_DAP_USERNAME', 'dap-service-uat')
DATA_POINT_DAP_PASSWORD = get_secret_value('DATA_POINT_DAP_PASSWORD_V2_SM_KEY', 'DATA_POINT_DAP_PASSWORD_V2')

DATA_POINT_DOPA_HOST = os.environ.get('DATA_POINT_DOPA_HOST', 'https://dap-microservice-438815635558.asia-east1.run.app')
DATA_POINT_DOPA_BASIC_AUTH = get_secret_value('DATA_POINT_DOPA_BASIC_AUTH_V2_SM_KEY', 'DATA_POINT_DOPA_BASIC_AUTH_V2')
DATA_POINT_LED_HOST = os.environ.get('DATA_POINT_LED_HOST', 'https://dap-microservice-438815635558.asia-east1.run.app')
DATA_POINT_LED_BASIC_AUTH = get_secret_value('DATA_POINT_LED_BASIC_AUTH_V2_SM_KEY', 'DATA_POINT_LED_BASIC_AUTH_V2')
DATA_POINT_LOCAL_THAI_PEP_LIST_HOST =  os.environ.get('DATA_POINT_LOCAL_THAI_PEP_LIST_HOST', 'https://thai-pep-list-uat-***********.us-central1.run.app')
DATA_POINT_LOCAL_THAI_PEP_LIST_USERNAME = os.environ.get('DATA_POINT_LOCAL_THAI_PEP_LIST_USERNAME', 'thai-pep-list-uat')
DATA_POINT_LOCAL_THAI_PEP_LIST_PASSWORD = get_secret_value('DATA_POINT_LOCAL_THAI_PEP_LIST_PASSWORD_SM_KEY', 'DATA_POINT_LOCAL_THAI_PEP_LIST_PASSWORD')
DATA_POINT_ASIA_VERIFY_HOST = os.environ.get('DATA_POINT_ASIA_VERIFY_HOST', 'https://sandbox.api.asiaverify.com')
DATA_POINT_ASIA_VERIFY_AUTH = get_secret_value('DATA_POINT_ASIA_VERIFY_AUTH_SM_KEY', 'DATA_POINT_ASIA_VERIFY_AUTH')
DATA_POINT_ASIA_VERIFY_SIGN = get_secret_value('DATA_POINT_ASIA_VERIFY_SIGN_SM_KEY', 'DATA_POINT_ASIA_VERIFY_SIGN')
DATA_POINT_ASIA_VERIFY_V2_HOST = os.environ.get('DATA_POINT_ASIA_VERIFY_V2_HOST', 'https://sandbox.api.asiaverify.com')
DATA_POINT_ASIA_VERIFY_V2_AUTH = get_secret_value('DATA_POINT_ASIA_VERIFY_AUTH_SM_KEY', 'DATA_POINT_ASIA_VERIFY_V2_AUTH')
DATA_POINT_ASIA_VERIFY_V2_SIGN = get_secret_value('DATA_POINT_ASIA_VERIFY_SIGN_SM_KEY', 'DATA_POINT_ASIA_VERIFY_V2_SIGN')
DATA_POINT_ASIA_VERIFY_WEBHOOK_IP = json.loads(os.environ.get('DATA_POINT_ASIA_VERIFY_WEBHOOK_IP', '["*"]')) # NOSONAR
DATA_POINT_ASIA_VERIFY_WEBHOOK_TIMEOUT = int(os.environ.get('DATA_POINT_ASIA_VERIFY_WEBHOOK_TIMEOUT', 20)) # NOSONAR
DATA_POINT_UBO_HOST = os.environ.get('DATA_POINT_UBO_HOST', 'https://ubo-mircoservice-sit-771620029082.asia-southeast1.run.app')
DATA_POINT_UBO_USERNAME = os.environ.get('DATA_POINT_UBO_USERNAME', 'ubo-microservice-sit')
DATA_POINT_UBO_PASSWORD = get_secret_value('DATA_POINT_UBO_PASSWORD_SM_KEY', 'DATA_POINT_UBO_PASSWORD')
DATA_POINT_UBO_POOL_PROCESSES = int(os.environ.get('DATA_POINT_UBO_POOL_PROCESSES', 4))
DATA_POINT_DBD_VAT_HOST = os.environ.get('DATA_POINT_DBD_VAT_HOST', 'https://dbd-query-temporary-***********.us-central1.run.app')
DATA_POINT_DBD_VAT_BASIC_AUTH = get_secret_value('DATA_POINT_DBD_VAT_BASIC_AUTH_SM_KEY', 'DATA_POINT_DBD_VAT_BASIC_AUTH')
######################
# GITLAB
######################
GITLAB_ACCESS_TOKEN = get_secret_value("GITLAB_ACCESS_TOKEN_SM_KEY", "GITLAB_ACCESS_TOKEN")
GITLAB_SCHEMA_REPO_ID = os.environ.get("GITLAB_SCHEMA_REPO_ID")
GITLAB_LINKED_ENV = os.environ.get("GITLAB_LINKED_ENV")
********************* = os.environ.get("*********************")

######################
# EXPORT PDF
######################
EXPORT_PDF_API_ENDPOINT = os.environ.get("EXPORT_PDF_API_ENDPOINT", "https://asia-east1-verifio-b2b.cloudfunctions.net/export-pdf-micro-service")
EXPORT_PDF_API_USERNAME = os.environ.get("EXPORT_PDF_API_USERNAME", "uppass")
EXPORT_PDF_API_PASSWORD = get_secret_value('EXPORT_PDF_API_PASSWORD_SM_KEY', "EXPORT_PDF_API_PASSWORD")

######################
# Integration
######################
DOCUSIGN_HOST = os.environ.get('DOCUSIGN_HOST', 'https://account-d.docusign.com')
DOCUSIGN_JWT_PRIVATE_KEY_BASE64 = get_secret_value('DOCUSIGN_JWT_PRIVATE_KEY_BASE64_SM_KEY', 'DOCUSIGN_JWT_PRIVATE_KEY_BASE64')
DOCUSIGN_INTEGRATION_KEY = get_secret_value('DOCUSIGN_INTEGRATION_KEY_SM_KEY', 'DOCUSIGN_INTEGRATION_KEY')

######################
# DJANGO VITE
######################
DJANGO_VITE = {
  "default": {
    "dev_mode": DEBUG,
    # "dev_server_protocol": os.environ.get('LOCAL_DEV_SERVER_PROTOCOL', 'https'),
    # "dev_server_host": os.environ.get('LOCAL_DEV_SERVER_HOST', 'my-local-server.com'),
    # "dev_server_port": os.environ.get('LOCAL_DEV_SERVER_PORT', 443),
    "manifest_path": "public/dist/manifest.json",
  }
}
# DECISION FLOW
######################
DECISION_FLOW_TRIGGER_HOST = os.environ.get("DECISION_FLOW_TRIGGER_HOST")
DECISION_FLOW_MAX_RERUN = int(os.environ.get("DECISION_FLOW_MAX_RERUN", 100))

######################
# Document Extraction Gateway
######################
DOCUMENT_EXTRACTION_GATEWAY_HOST = os.environ.get('DOCUMENT_EXTRACTION_GATEWAY_HOST', BANK_STATEMENT_GATEWAY_ENDPOINT)
DOCUMENT_EXTRACTION_GATEWAY_JWT_USER_ID = os.environ.get('DOCUMENT_EXTRACTION_GATEWAY_JWT_USER_ID', BANK_STATEMENT_GATEWAY_TOKEN_USER_ID)
DOCUMENT_EXTRACTION_GATEWAY_JWT_COMPANY_NAME = os.environ.get('DOCUMENT_EXTRACTION_GATEWAY_JWT_COMPANY_NAME', BANK_STATEMENT_GATEWAY_TOKEN_COMPANY_NAME)
DOCUMENT_EXTRACTION_GATEWAY_JWT_PRIVATE_KEY = get_secret_value('DOCUMENT_EXTRACTION_GATEWAY_JWT_PRIVATE_KEY_SM_KEY', 'DOCUMENT_EXTRACTION_GATEWAY_JWT_PRIVATE_KEY', BANK_STATEMENT_GATEWAY_JWT_PASSWORD)
DOCUMENT_EXTRACTION_GATEWAY_ENABLE_REPORT_MANUALLY_GET_RESULT = os.environ.get('DOCUMENT_EXTRACTION_GATEWAY_ENABLE_REPORT_MANUALLY_GET_RESULT', 0)