from django.db.models.signals import post_save, post_delete
from django.apps import apps

def connect_signals():
    from loc_mem_cache.cache import invalidate_model_cache
    from loc_mem_cache.conf import settings
    for model_path in settings.LOCMEMCACHE.keys():
        model = apps.get_model(model_path)
        post_save.connect(invalidate_model_cache, sender=model)
        post_delete.connect(invalidate_model_cache, sender=model)