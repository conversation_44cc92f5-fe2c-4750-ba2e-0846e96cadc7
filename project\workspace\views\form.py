from dynamicform.util import current_user
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.views.decorators.vary import vary_on_headers
from django.conf import settings
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from dynamicform.submodules.form.views.form import (
    FormViewSet as DynamicformFormViewSet
)
from dynamicform.models import Form
from dynamicform.submodules.form.serializer import FormListSerializer
from workspace.models import Workspace, Status, Form as WorkspaceForm
from django.core.cache import cache


CACHE_FORM_VIEWSET_LIST_PREFIX = "cache_form_viewset_list"
CACHE_FORM_VIEWSET_LIST_TIMEOUT = settings.CACHE_FORM_VIEWSET_LIST_TIMEOUT

class FormViewSet(DynamicformFormViewSet):

    def get_queryset(self):
        user = self.request.user
        workspace_slug = self.kwargs.get("workspace_slug", None)
        if not user.is_api_token:
            forms = Form.objects.filter(
                form_of__workspace__slug=workspace_slug,
                form_of__workspace__member__user=user,
                form_of__workspace__member__status=Status.ACCEPTED,
            )
        else:
            forms = Form.objects.filter(
                form_of__workspace__slug=workspace_slug,
                token__user=user,
            )
        return forms

    def get_workspace(self):
        workspace_slug = self.kwargs.get("workspace_slug", None)
        user = self.request.user
        self.workspace = Workspace.objects.filter(
            slug=workspace_slug, member__user=user
        ).first()
        return self.workspace
    
    @method_decorator(cache_page(CACHE_FORM_VIEWSET_LIST_TIMEOUT, key_prefix=CACHE_FORM_VIEWSET_LIST_PREFIX))
    @method_decorator(vary_on_headers("Authorization"))
    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        user = current_user()
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            user_highlight_form_ids = user.formusers_set.filter(
                highlight=True,
                form__in=page
            ).values_list('form_id', flat=True)
            serializer = FormListSerializer(
                page,
                many=True,
                context={
                    "user_highlight_form_ids": user_highlight_form_ids
                }
            )
            return self.get_paginated_response(serializer.data)

        user_highlight_form_ids = user.formusers_set.filter(highlight=True).values_list('form_id', flat=True)
        serializer = FormListSerializer(
            queryset,
            many=True,
            context={
                "user_highlight_form_ids": user_highlight_form_ids
            }
        )
        
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        workspace = self.get_workspace()
        if not workspace:
            return Response(status=status.HTTP_404_NOT_FOUND)
        
        is_valid, _, _ = workspace.valid_forms_limits()
        if not is_valid:
            return Response({
                "detail": "The number of flows exceeded the limits."
            }, status=status.HTTP_426_UPGRADE_REQUIRED)

        return super(FormViewSet, self).create(request, *args, **kwargs)

    def perform_create(self, serializer):
        form = serializer.save()
        workspace = self.get_workspace()
        if not workspace:
            return Response(status=status.HTTP_404_NOT_FOUND)
        workspace_form = WorkspaceForm(form=form, workspace=workspace)
        workspace_form.save()
    
    # permission in ACTION_CREATE_EDIT_DUPLICATE_FLOWDynamicformFormViewSet
    @action(detail=True, methods=['POST'], url_path='clone')
    def clone(self, request, workspace_slug=None, slug=None, *args, **kwargs):
        workspace = self.get_workspace()
        if not workspace:
            return Response(status=status.HTTP_404_NOT_FOUND)
        
        is_valid, _, _ = workspace.valid_forms_limits()
        if not is_valid:
            return Response({
                "detail": "The number of flows exceeded the limits."
            }, status=status.HTTP_426_UPGRADE_REQUIRED)

        return super(FormViewSet, self).clone(request, slug=slug, workspace=workspace, *args, **kwargs)

    def initialize_request(self, request, *args, **kwargs):
        method = request.method.lower()
        if method in ["delete", "post", "put", "patch"]:
            self.clear_list_cache()
        return super(FormViewSet, self).initialize_request(request, *args, **kwargs)

    def clear_list_cache(self, workspace_slug=None):
        cache.clear()