import json

from django.conf import settings
from django.db import models
from django.utils import timezone
from dynamicform.util import mapping_params


class FormProperties(models.Model):
  class Meta:
    abstract = True

  @property
  def trans_frontend_schema(self):
    return self.trans()

  @property
  def frontend_schema(self):
    return json.loads(self._frontend_schema)

  @frontend_schema.setter
  def frontend_schema(self, value):
    self._frontend_schema = json.dumps(value)

  @property
  def backend_schema(self):
    return json.loads(self._backend_schema)

  @backend_schema.setter
  def backend_schema(self, value):
    self._backend_schema = json.dumps(value)

  @property
  def report_schema(self):
    try:
      return json.loads(self._report_schema)
    except:
      return {}

  @report_schema.setter
  def report_schema(self, value):
    self._report_schema = json.dumps(value)

  @property
  def trans_report_schema(self):
    return self.trans(schema=self.report_schema)
  
  @property
  def secret_key(self):
    return f"{settings.SECRET_KEY}{self.slug}"
  
  @property
  def secret_key_256(self):# return string lenght is 32 (256bit)
    form_id = f'{self.id}'
    secrect_lenght = 32 - len(form_id)
    secrect = settings.SECRET_KEY[:secrect_lenght]
    return f"{secrect}{form_id}"
  
  @property
  def mockup_answers(self):
    items = self.backend_schema.get('items', {})
    input_files = self.backend_schema.get('input_files', {})
    result = []
    for index in items:
      if index in input_files:
        value = 'https://picsum.photos/100/80'
      else:
        value = 'Lorem Ipsum' 
      result.append({
        'questions': index,
        'value' : value
      })
    return result

  @property
  def mockup_answers_version_2(self):
    items = self.backend_schema.get('items', {})
    input_files = self.backend_schema.get('input_files', {})
    result = {}

    for i in items:
      if i in input_files:
        value = 'https://picsum.photos/100/80'
      else:
        value = 'Lorem Ipsum'

      result.update(
        {
          i: {
            'value' : value,
            'created_at': timezone.now().isoformat(),
            'updated_at': timezone.now().isoformat(),
          }
        }
      )

    return result

  @property
  def default_webhook_submit_body_template(self):
    return {
      "event": {
        "nounce": "{{event.nounce}}",
        "type": "{{event.event_trigger}}",
        "created_at": "{{event.created_at|isoformat}}",
      },
      "application": {
        "form": "{{applied_form.form.slug}}",
        "id": "{{applied_form.id}}",
        "no": "{{applied_form.no_format}}",
        "slug": "{{applied_form.slug}}",
        "submitted_at": "{{applied_form.submitted_at|isoformat|default:'null'}}",
        "status": "{{applied_form.application.status}}",
        "other_status": "{% autoescape off %}{% json %}{% application applied_form.application 'get_other_status(suffix=False)' %}{% endjson %}{% endautoescape %}",
      },
      "answers": "{{applied_form.all_app_submit_answers}}",
      "extra": "{{applied_form.extra}}"
      # "ekyc": {
      #   "ekyc": "{{applied_form.ekyc_result}}"
      # }
    }

  @property
  def default_webhook_submit_body_template_version_2(self):
    return {
      "event": {
        "nounce": "{{event.nounce}}",
        "type": "{{event.event_trigger}}",
        "version": 2,
        "created_at": "{{event.created_at|isoformat}}",
      },
      "application": {
        "form": "{{applied_form.form.slug}}",
        "id": "{{applied_form.id}}",
        "no": "{{applied_form.no_format}}",
        "slug": "{{applied_form.slug}}",
        "submitted_at": "{{applied_form.submitted_at|isoformat|default:'null'}}",
        "status": "{{applied_form.application.status}}",
        "other_status": "{% autoescape off %}{% json %}{% application applied_form.application 'get_other_status(suffix=False)' %}{% endjson %}{% endautoescape %}",
      },
      "answers": "{{applied_form.all_app_submit_answers_version_2}}",
      "extra": "{{applied_form.extra}}"
    }

  def mockup_webhook_submit_body(self, event):
    from ...models import Application, AppliedForm
    body_template = self.default_webhook_submit_body_template
    appliedform = AppliedForm(
                    id=1, 
                    slug='RANDOM01', 
                    form=self,
                    submitted_at=timezone.now())
    appliedform.is_mockup = True
    data = {
      'event': event,
      'applied_form': appliedform,
      'application': Application(id=1, first_applied_form=appliedform),
    }
    return mapping_params(data, body_template, map_request=False)

  def mockup_webhook_submit_body_version_2(self, event):
    from ...models import Application, AppliedForm
    body_template = self.default_webhook_submit_body_template_version_2
    appliedform = AppliedForm(
                    id=1, 
                    slug='RANDOM01', 
                    form=self,
                    submitted_at=timezone.now())
    appliedform.is_mockup = True
    data = {
      'event': event,
      'applied_form': appliedform,
      'application': Application(id=1, first_applied_form=appliedform),
    }
    return mapping_params(data, body_template, map_request=False)
  
  @property
  def setting(self):
    try:
      return self.formsetting.settings
    except:
      return {}
  
  @property
  def pages_list(self):
    pages_list = {}
    pages = self.page_set.all()
    for page in pages:
      pages_list.update({page.category: page.name})
    return pages_list
