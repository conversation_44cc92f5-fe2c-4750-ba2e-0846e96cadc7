from django.test import override_settings
import pytest
from unittest import mock
from dynamicform.tests.case import TestDatabaseTestCase
from dynamicform.models import AppliedForm, Form, Answer


@override_settings(ENABLE_CREDIT_SYSTEM=False)
class SaveGetAnswer(TestDatabaseTestCase):
    def setUp(self):
        super(SaveGetAnswer, self).setUp()
        form = Form(
            name="test",
            slug="slug-test-a",
            frontend_schema={
                "steps": {
                    "step1": {
                        "sections": {
                            "s1": {
                                "items": {
                                    "item_button": {"name": "item_button", "type": "Button"},
                                    "item_inputtext": {"name": "item_inputtext", "type": "InputText"},
                                    "item_inputtelephone": {"name": "item_inputtelephone", "type": "InputTelephone"},
                                    "item_textarea": {"name": "item_textarea", "type": "Textarea"},
                                    "item_inputnumber": {"name": "item_inputnumber", "type": "InputNumber"},
                                    "item_slider": {"name": "item_slider", "type": "Slider"},
                                    "item_sliderchoice": {"name": "item_sliderchoice", "type": "SliderChoice"},
                                    "item_inputradio": {"name": "item_inputradio", "type": "InputRadio"},
                                    "item_inputradiostaticicon": {"name": "item_inputradiostaticicon", "type": "InputRadioStaticIcon"},
                                    "item_singleselectbutton": {"name": "item_singleselectbutton", "type": "SingleSelectButton"},
                                    "item_popupchoice": {"name": "item_popupchoice", "type": "PopupChoice"},
                                    "item_select": {"name": "item_select", "type": "Select"},
                                    "item_advanceselect": {"name": "item_advanceselect", "type": "AdvanceSelect"},
                                    "item_choicetree": {"name": "item_choicetree", "type": "ChoiceTree"},
                                    "item_choicemodal": {"name": "item_choicemodal", "type": "ChoiceModal"},
                                    "item_inputcheckbox": {"name": "item_inputcheckbox", "type": "InputCheckbox"},
                                    "item_inputcheckbox_boolean": {"name": "item_inputcheckbox", "type": "InputCheckbox", "boolean": True},
                                    "item_multipleselectbutton": {"name": "item_multipleselectbutton", "type": "MultipleSelectButton"},
                                    "item_multiselect": {"name": "item_multiselect", "type": "Multiselect"},
                                    "item_countryselect": {"name": "item_countryselect", "type": "CountrySelect"},
                                    "item_nationalityselect": {"name": "item_nationalityselect", "type": "NationalitySelect"},
                                    "item_datafield": {"name": "item_datafield", "type": "DataField"},
                                    "item_inputdate": {"name": "item_inputdate", "type": "InputDate"},
                                    "item_inputdatetext": {"name": "item_inputdatetext", "type": "InputDateText"},
                                }
                            }
                        }
                    }
                }
            },
        )
        form.save()
        self.form = form
        self.applied_form = form.apply()

        Answer.objects.create(applied_form=self.applied_form, question="item_button", value="ans")
        Answer.objects.create(applied_form=self.applied_form, question="item_inputtext", value="ans")
        Answer.objects.create(applied_form=self.applied_form, question="item_inputtelephone", value="+661234567890")
        Answer.objects.create(applied_form=self.applied_form, question="item_textarea", value="ans")
        Answer.objects.create(applied_form=self.applied_form, question="item_inputnumber", value=1)
        Answer.objects.create(applied_form=self.applied_form, question="item_slider", value=2)
        Answer.objects.create(applied_form=self.applied_form, question="item_sliderchoice", value=3)
        Answer.objects.create(applied_form=self.applied_form, question="item_inputradio", value="ans")
        Answer.objects.create(applied_form=self.applied_form, question="item_inputradiostaticicon", value="ans")
        Answer.objects.create(applied_form=self.applied_form, question="item_singleselectbutton", value="ans")
        Answer.objects.create(applied_form=self.applied_form, question="item_popupchoice", value="ans")
        Answer.objects.create(applied_form=self.applied_form, question="item_select", value="ans")
        Answer.objects.create(applied_form=self.applied_form, question="item_advanceselect", value="ans")
        Answer.objects.create(applied_form=self.applied_form, question="item_choicetree", value="ans")
        Answer.objects.create(applied_form=self.applied_form, question="item_choicemodal", value="ans")
        Answer.objects.create(applied_form=self.applied_form, question="item_inputcheckbox", value=["a", "b"])
        Answer.objects.create(applied_form=self.applied_form, question="item_inputcheckbox_boolean", value=True)
        Answer.objects.create(applied_form=self.applied_form, question="item_multipleselectbutton", value=["a", "b"])
        Answer.objects.create(applied_form=self.applied_form, question="item_multiselect", value=["a", "b"])
        Answer.objects.create(applied_form=self.applied_form, question="item_countryselect", value="THA")
        Answer.objects.create(applied_form=self.applied_form, question="item_nationalityselect", value="THA")
        Answer.objects.create(applied_form=self.applied_form, question="item_datafield", value={"a":"b"})
        Answer.objects.create(applied_form=self.applied_form, question="item_inputdate", value="2022-01-22")
        Answer.objects.create(applied_form=self.applied_form, question="item_inputdatetext", value="2022-01-22")

    def test_can_get_answers(self):
        answers = self.applied_form.get_answers()
        # print("answers", answers)
        self.assertEqual(answers,
        {
            "item_button": "ans",
            "item_inputtext": "ans",
            "item_inputtelephone": "+661234567890",
            "item_textarea": "ans",
            "item_inputnumber": 1,
            "item_slider": 2,
            "item_sliderchoice": 3,
            "item_inputradio": "ans",
            "item_inputradiostaticicon": "ans",
            "item_singleselectbutton": "ans",
            "item_popupchoice": "ans",
            "item_select": "ans",
            "item_advanceselect": "ans",
            "item_choicetree": "ans",
            "item_choicemodal": "ans",
            "item_inputcheckbox": [
                "a",
                "b"
            ],
            "item_inputcheckbox_boolean": True,
            "item_multipleselectbutton": [
                "a",
                "b"
            ],
            "item_multiselect": [
                "a",
                "b"
            ],
            "item_countryselect": "THA",
            "item_nationalityselect": "THA",
            "item_datafield": {
                "a": "b"
            },
            "item_inputdate": "2022-01-22",
            "item_inputdatetext": "2022-01-22"
            }
        )