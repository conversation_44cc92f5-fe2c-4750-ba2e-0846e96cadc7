import logging
import re
import inspect
from typing import Optional
from data_point.base import DataPointBase
from data_point.exceptions import DataPointException, DataPointUnprocessableEntity
from data_point.fields.base import DataPoint<PERSON><PERSON><PERSON>ield
from django.conf import settings
from pydash import get
from rest_framework import status

from data_point.utils import ErrorMessage
from rest_framework.exceptions import APIException
from data_point.exceptions import (
    DataPointException,
    DataPointInputValidationError,
    DataPointResponseException,
)
from data_point.services.asia_verify.v2 import AsiaVerifyApiV2


logger: logging.Logger = logging.getLogger(__name__)
DATA_POINT_DBD_HOST = settings.DATA_POINT_DBD_HOST
DATA_POINT_DBD_USERNAME = settings.DATA_POINT_DBD_USERNAME
DATA_POINT_DBD_PASSWORD = settings.DATA_POINT_DBD_PASSWORD
DATA_POINT_CREDEN_HOST = settings.DATA_POINT_CREDEN_HOST
DATA_POINT_CREDEN_API_KEY = settings.DATA_POINT_CREDEN_API_KEY
DATA_POINT_COMPLY_ADVANTAGE_HOST = settings.DATA_POINT_COMPLY_ADVANTAGE_HOST
DATA_POINT_COMPLY_ADVANTAGE_API_KEY = settings.DATA_POINT_COMPLY_ADVANTAGE_API_KEY
DATA_POINT_ASIA_VERIFY_HOST = settings.DATA_POINT_ASIA_VERIFY_HOST
DATA_POINT_ASIA_VERIFY_AUTH = settings.DATA_POINT_ASIA_VERIFY_AUTH
DATA_POINT_ASIA_VERIFY_SIGN = settings.DATA_POINT_ASIA_VERIFY_SIGN
DATA_POINT_DAP_HOST = settings.DATA_POINT_DAP_HOST
DATA_POINT_DAP_USERNAME = settings.DATA_POINT_DAP_USERNAME
DATA_POINT_DAP_PASSWORD = settings.DATA_POINT_DAP_PASSWORD

DATA_POINT_COMPLY_ADVANTAGE_COMPANY_FILTER = (
    settings.DATA_POINT_COMPLY_ADVANTAGE_COMPANY_FILTER
    or {
        "exact_match": True,
    }
)
DATA_POINT_COMPLY_ADVANTAGE_PERSON_FILTER = (
    settings.DATA_POINT_COMPLY_ADVANTAGE_PERSON_FILTER
    or {"fuzziness": 0.5, "filters": {"entity_type": "person"}}
)

DATAPOINT_OPTIONS = {
    # ANY HITS
    "is_company_with_hits": {
        "label": "Based on the JID, chekc if the Company is sanctioned or not",
        "type": "boolean",
    },
    "is_any_directors_with_hits": {
        "label": "Based on the JID, check if any of the directors are found in Comply advantage or DAP(AMLO)",
        "type": "boolean",
    },
    "is_any_25pct_shareholders_with_hits": {
        "label": "Based on the JID, check if any shareholders who own more than 25% share are found in Comply advantage or DAP(AMLO, LED)",
        "type": "boolean",
    },
    "is_any_15pct_shareholders_with_hits": {
        "label": "Based on the JID, check if any shareholders who own more than 15% share are found in Comply advantage or DAP(AMLO, LED)",
        "type": "boolean",
    },
    "is_any_10pct_shareholders_with_hits": {
        "label": "Based on the JID, check if any shareholders who own more than 10% share are found in Comply advantage or DAP(AMLO, LED)",
        "type": "boolean",
    },
    # PEP HIT
    "is_any_directors_th_pep_hits": {
        "label": "Based on the JID, Check if any of the directors are found in Local Thai PEP service",
        "type": "boolean",
    },
    "is_any_25pct_shareholders_th_pep_hits": {
        "label": "Based on the JID, Check if any shareholders who own more than 25% share are found in Local Thai PEP service",
        "type": "boolean",
    },
    'is_any_15pct_shareholders_th_pep_hits': {
        'label': 'Based on the JID, check if any shareholders who own more than 15% share are found in LED',
        'type': 'boolean',
    },
    'is_any_10pct_shareholders_th_pep_hits ': {
        'label': 'Based on the JID, check if any shareholders who own more than 10% share are found in LED',
        'type': 'boolean',
    },
    # AMLO HITS
    "is_any_directors_th_amlo_hits": {
        "label": "Based on the JID, check if any of the directors are found in AMLO",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_25pct_shareholders_th_amlo_hits": {
        "label": "Based on the JID, check if any shareholders who own more than 25% share are found in AMLO",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_15pct_shareholders_th_amlo_hits": {
        "label": "Based on the JID, check if any shareholders who own more than 15% share are found in AMLO",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_10pct_shareholders_th_amlo_hits": {
        "label": "Based on the JID, check if any shareholders who own more than 10% share are found in AMLO",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    # ===== AMLO - DIRECTOR REASON DATAPOINT =====
    "is_any_directors_th_amlo_hits_freeze_05": {
        "label": "Based on the JID, check if any of the directors are found in AMLO that hit by FREEZE-05",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_directors_th_amlo_hits_hr_02": {
        "label": "Based on the JID, check if any of the directors are found in AMLO that hit by HR-02",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_directors_th_amlo_hits_hr_08_risk": {
        "label": "Based on the JID, check if any of the directors are found in AMLO that hit by HR-08",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_directors_th_amlo_hits_un_sanction": {
        "label": "Based on the JID, check if any of the directors are found in AMLO that hit by un sanction or FREEZE-04",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    # ===== AMLO - DIRECTOR REASON DATAPOINT =====
    # ===== AMLO - 25PCT SHAREHOLDERS REASON DATAPOINT =====
    "is_any_25pct_shareholders_th_amlo_hits_freeze_05": {
        "label": "Based on the JID, check if any of the shareholders who own more than 25% share are found in AMLO that hit by FREEZE-05",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_25pct_shareholders_th_amlo_hits_hr_02": {
        "label": "Based on the JID, check if any of the shareholders who own more than 25% share are found in AMLO that hit by HR-02",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_25pct_shareholders_th_amlo_hits_hr_08_risk": {
        "label": "Based on the JID, check if any of the shareholders who own more than 25% share are found in AMLO that hit by HR-08",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_25pct_shareholders_th_amlo_hits_un_sanction": {
        "label": "Based on the JID, check if any of the shareholders who own more than 25% share are found in AMLO that hit by un sanction or FREEZE-04",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    # ===== AMLO - 25PCT SHAREHOLDERS REASON DATAPOINT =====
    # ===== AMLO - 15PCT SHAREHOLDERS REASON DATAPOINT =====
    "is_any_15pct_shareholders_th_amlo_hits_freeze_05": {
        "label": "Based on the JID, check if any of the shareholders who own more than 15% share are found in AMLO that hit by FREEZE-05",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_15pct_shareholders_th_amlo_hits_hr_02": {
        "label": "Based on the JID, check if any of the shareholders who own more than 15% share are found in AMLO that hit by HR-02",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_15pct_shareholders_th_amlo_hits_hr_08_risk": {
        "label": "Based on the JID, check if any of the shareholders who own more than 15% share are found in AMLO that hit by HR-08",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_15pct_shareholders_th_amlo_hits_un_sanction": {
        "label": "Based on the JID, check if any of the shareholders who own more than 15% share are found in AMLO that hit by un sanction or FREEZE-04",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    # ===== AMLO - 15PCT SHAREHOLDERS REASON DATAPOINT =====
    # ===== AMLO - 10PCT SHAREHOLDERS REASON DATAPOINT =====
    "is_any_10pct_shareholders_th_amlo_hits_freeze_05": {
        "label": "Based on the JID, check if any of the shareholders who own more than 10% share are found in AMLO that hit by FREEZE-05",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_10pct_shareholders_th_amlo_hits_hr_02": {
        "label": "Based on the JID, check if any of the shareholders who own more than 10% share are found in AMLO that hit by HR-02",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_10pct_shareholders_th_amlo_hits_hr_08_risk": {
        "label": "Based on the JID, check if any of the shareholders who own more than 10% share are found in AMLO that hit by HR-08",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_10pct_shareholders_th_amlo_hits_un_sanction": {
        "label": "Based on the JID, check if any of the shareholders who own more than 10% share are found in AMLO that hit by un sanction or FREEZE-04",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    # ===== AMLO - 10PCT SHAREHOLDERS REASON DATAPOINT =====
    # LED HITS
    "is_any_directors_th_led_bankrupt": {
        "label": "Based on the JID, check if any directors are currrently filed for bankruptcy.",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_directors_th_led_bankrupt_from_corruption": {
        "label": "Based on the JID, check if any directors are currrently filed for bankruptcy because of corruption.",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_25pct_shareholders_th_led_bankrupt": {
        "label": "Based on the JID, check if any shareholders who own more than 25% share are currrently filed for bankruptcy.",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_25pct_shareholders_th_led_bankrupt_from_corruption": {
        "label": "Based on the JID, check if any shareholders who own more than 25% share are currrently filed for bankruptcy because of corruption.",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_15pct_shareholders_th_led_bankrupt": {
        "label": "Based on the JID, check if any shareholders who own more than 15% share are currrently filed for bankruptcy.",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_15pct_shareholders_th_led_bankrupt_from_corruption": {
        "label": "Based on the JID, check if any shareholders who own more than 15% share are currrently filed for bankruptcy because of corruption.",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_10pct_shareholders_th_led_bankrupt": {
        "label": "Based on the JID, check if any shareholders who own more than 10% share are currrently filed for bankruptcy.",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
    "is_any_10pct_shareholders_th_led_bankrupt_from_corruption": {
        "label": "Based on the JID, check if any shareholders who own more than 10% share are currrently filed for bankruptcy because of corruption.",
        "type": "boolean",
        "enable_settings": [["data_point_service.dap.enable"]],
    },
}


class BusinessAML(DataPointBase):
    # 'Juristic_id' is an older term, but we will continue to use it to ensure compatibility.
    juristic_id = DataPointJSONField(
        label="Business Information",
        type="string",
        required=True,
        split_fields=True,
        allowed_item_builder_types=[
            "short_long_answer",  #  Allow only short_long_answer builder only
            "business_information",
        ],
    )

    name = "business_aml"
    title = "Business AML"
    sub_title = "Comprehensive Business AML Solutions"
    description = "Enhancing Due Diligence: A Deep Dive into Directors' and Shareholders' Anti-Money Laundering Compliance"  # fmt: skip
    icon = (
        "https://cdn.uppass.io/projects/uppass/img/datapoint/icon_64/BusinessAML_64.png"
    )
    option_icon = "https://cdn.uppass.io/projects/uppass/img/datapoint/option-icon_36/BusinessAML_36.png"
    data_point_opitons = DATAPOINT_OPTIONS

    def validate_juristic_id(self, value):
        self.is_valid_payload_connection(value, field_name="juristic_id")
        return value

    def set_current_payload(self):
        self.set_payload("juristic_id")

        # Set payload from business_infomation by split_field feature.
        self.set_payload("id")
        self.set_payload("name")
        self.set_payload("country")
        return self.current_payload

    def get_data_result(self, **kwargs):
        juristic_id = self.get_payload("juristic_id")

        # split field from business infomation
        business_infomation_id = self.get_payload("id")
        business_infomation_name = self.get_payload("name")
        business_infomation_country = self.get_payload("country") or "THA"
        selected_data_point_options = self.values_list
        save_result = {
            "dbd": [],
            "creden": [],
            "comply_advantage": [],
            "asia_verify": [],
            "amlo": [],
            # 'amlo_person_screening': [],
            # 'amlo_person_screening_un_list': [],
            "led": [],
            "local_thai_pep_list": [],
        }

        result = {}  # data point result initial
        dbd_options = []  # data point required dbd service
        creden_options = []  # data point required creden service
        for option in DATAPOINT_OPTIONS.keys():
            result[option] = False
            if "company" in option or "directors" in option:
                dbd_options.append(option)
            elif "shareholders" in option:
                creden_options.append(option)

        # Validate input before call API service
        is_input_valid, error_message = self.validate_input(
            juristic_id, business_infomation_country
        )

        # Prepare error result and Return error result
        # if is_input_valid is False
        if not is_input_valid:
            error_result = self.invalid_input_error_result(
                error_message, DataPointInputValidationError
            )
            if "error_result_tracking" in kwargs.keys():
                self.prepare_error_result_for_save_db(
                    save_result, error_result, kwargs["error_result_tracking"]
                )
            return error_result

        # Use DBD and BDEX shareholder for Thailand country
        if business_infomation_country == "THA":
            # DBD part if in decision flow has data point that require DBD
            if any(i in selected_data_point_options for i in dbd_options):

                # Get DBD result
                dbd_params = {
                    "jdid": juristic_id,
                }
                dbd_result = self.get_dbd_result(dbd_params)
                dbd_result = self.clean_dbd_data(dbd_result)
                save_result["dbd"].append(
                    {
                        "input": dbd_params,
                        "output": dbd_result,
                    }
                )

                # call 1 against the company name from DBD
                # Get CA to validate that company is hit or not
                call_1_comply_advantage_data = {
                    "search_term": dbd_result.get("jp_tname", ""),
                    **DATA_POINT_COMPLY_ADVANTAGE_COMPANY_FILTER,
                }
                call_1_comply_advantage_result = self.get_comply_advantage_result(
                    call_1_comply_advantage_data
                )
                save_result["comply_advantage"].append(
                    {
                        "input": call_1_comply_advantage_data,
                        "output": call_1_comply_advantage_result,
                    }
                )
                call_1_total_hits = get(
                    call_1_comply_advantage_result, "content.data.total_hits"
                )

                # Set 'is_company_with_hits' by CA result
                if call_1_total_hits is not None and call_1_total_hits > 0:
                    result["is_company_with_hits"] = True
                elif call_1_total_hits is not None and call_1_total_hits <= 0:
                    result["is_company_with_hits"] = False

                for director_name in dbd_result.get("partner", []):
                    save_result, result = self.get_data_point_for_director(
                        director_name=director_name,
                        current_data_point_result=result,
                        current_save_result=save_result,
                        data_point_select_list=selected_data_point_options,
                    )

            # Creden part
            if any(i in selected_data_point_options for i in creden_options):
                creden_data = {
                    "id": juristic_id,
                }
                creden_result = self.get_creden_result(creden_data)
                creden_result = self.restructure_creden_result(creden_result)
                save_result["creden"].append(
                    {
                        "input": creden_data,
                        "output": creden_result,
                    }
                )

                # Validate data point with shareholder list in BDEX shareholder
                for shareholder_info in creden_result.get("list_shareholder", []):
                    bdex_shareholder_pct_share = shareholder_info.get("pct_share", 0)
                    shareholder_name = shareholder_info.get("fullname")
                    if isinstance(shareholder_name, str):
                        shareholder_name = shareholder_name.strip()
                    shareholder_job = shareholder_info.get("job")
                    pct_share_data_point = None

                    # Find percent of shareholde by pct_share and selected_data_point_options
                    if bdex_shareholder_pct_share >= 25 and self.is_in_option("25pct"):
                        pct_share_data_point = "25pct"
                    elif bdex_shareholder_pct_share >= 15 and self.is_in_option(
                        "15pct"
                    ):
                        pct_share_data_point = "15pct"
                    elif bdex_shareholder_pct_share >= 10 and self.is_in_option(
                        "10pct"
                    ):
                        pct_share_data_point = "10pct"
                    else:
                        continue

                    save_result, result = self.get_data_point_for_shareholder(
                        shareholder_name=shareholder_name,
                        shareholder_job=shareholder_job,
                        percent_share=pct_share_data_point,
                        current_data_point_result=result,
                        current_save_result=save_result,
                        data_point_select_list=selected_data_point_options,
                    )

        # Use Asia Verify for other support country
        else:
            # Initial body for request body of Asia Verify API
            country_upper = str(business_infomation_country).upper()  # Make country always upper
            body = {
                "input": juristic_id,
                "language": "ALL",
                "country": country_upper,  # This field is not effective in request but need to set to use in bussiness tab in report page
            }
            # Setup function for get data point options result and setup company_id request body by country.
            if country_upper == "JPN":
                data_point_option_result_function = (
                    self.data_point_option_result_for_japan
                )
            elif country_upper == "MYS":
                data_point_option_result_function = (
                    self.data_point_option_result_for_malaysia
                )
            elif country_upper == "SGP":
                data_point_option_result_function = (
                    self.data_point_option_result_for_singapore
                )
            elif country_upper == "HKG":
                data_point_option_result_function = (
                    self.data_point_option_result_for_hongkong
                )
            elif country_upper == "PHL":
                data_point_option_result_function = self.data_point_option_result_for_philippines
            elif country_upper == "AUS":
                data_point_option_result_function = self.data_point_option_result_for_australia
            else :
                # This exception is from develop do not add any configuration for this country
                raise DataPointException(
                    f"{self.title}: This country is not configured for request service: {str(business_infomation_country)}"
                )

            # Call Asia Verify API
            asia_verify_service = AsiaVerifyApiV2(self.context, self.name)

            if str(body["country"]).upper() == "HKG":
                search_result = asia_verify_service.search_asia_verify_webhook_response(
                    request_body=body, country=country_upper
                )
            elif str(body["country"]).upper() == "PHL":
                body["additionalIdentifier"] = {"companyName": "company_name"}
                search_result = asia_verify_service.search_asia_verify_webhook_response(
                    request_body=body, country=country_upper
                )
            else:
                search_result = asia_verify_service.search_asia_verify(
                    request_body=body, country=country_upper
                )
            save_result["asia_verify"].append(
                {
                    "input": body,
                    "output": search_result,
                }
            )

            # Get company_information by validate `search_result` with version of response
            v2_response = ["PHL", "AUS"]
            # Get from Asia Verify V2 response
            if any(True for country_code in v2_response if country_upper == country_code) :
                company_information = get(search_result, 'result', {})
            # Get from Asia Verify V1 response (V1 adapters)
            else:
                company_information = get(search_result, 'result.data', {})

            # Find percent of shareholde by pct_share and selected_data_point_options
            pct_share_data_point_ca_hits_list = []
            pct_share_data_point_th_pep_hits_list = []
            if self.is_in_option("is_any_25pct_shareholders_with_hits"):
                pct_share_data_point_ca_hits_list.append(
                    "is_any_25pct_shareholders_with_hits"
                )
                pct_share_data_point_th_pep_hits_list.append(
                    "is_any_25pct_shareholders_th_pep_hits"
                )
            if self.is_in_option("is_any_15pct_shareholders_with_hits"):
                pct_share_data_point_ca_hits_list.append(
                    "is_any_15pct_shareholders_with_hits"
                )
                pct_share_data_point_th_pep_hits_list.append(
                    "is_any_15pct_shareholders_th_pep_hits"
                )
            if self.is_in_option("is_any_10pct_shareholders_with_hits"):
                pct_share_data_point_ca_hits_list.append(
                    "is_any_10pct_shareholders_with_hits"
                )
                pct_share_data_point_th_pep_hits_list.append(
                    "is_any_10pct_shareholders_th_pep_hits"
                )

            if self.is_in_option("is_any_25pct_shareholders_th_pep_hits") and not self.is_in_option("is_any_25pct_shareholders_with_hits"):
                pct_share_data_point_th_pep_hits_list.append(
                    "is_any_25pct_shareholders_th_pep_hits"
                )
            if self.is_in_option("is_any_15pct_shareholders_th_pep_hits") and not self.is_in_option("is_any_15pct_shareholders_with_hits"):
                pct_share_data_point_th_pep_hits_list.append(
                    "is_any_15pct_shareholders_th_pep_hits"
                )
            if self.is_in_option("is_any_10pct_shareholders_th_pep_hits") and not self.is_in_option("is_any_10pct_shareholders_with_hits"):
                pct_share_data_point_th_pep_hits_list.append(
                    "is_any_10pct_shareholders_th_pep_hits"
                )

            result, comply_adventage_save_result = data_point_option_result_function(company_information, pct_share_data_point_ca_hits_list, pct_share_data_point_th_pep_hits_list)
            save_result['comply_advantage'] = comply_adventage_save_result['comply_advantage']
            save_result['local_thai_pep_list'] = comply_adventage_save_result['local_thai_pep_list']

        self.set_current_data_point_output(save_result)
        return result

    def is_in_option(self, text: str):
        for need_data_point in self.values_list:
            if text in need_data_point:
                return True

    def get_dbd_result(self, params: dict) -> dict:
        data_point_function_name = inspect.currentframe().f_code.co_name
        url = f"{DATA_POINT_DBD_HOST}/company_dbd_query"
        response = self._request(
            method="GET",
            url=url,
            params=params,
            auth=(DATA_POINT_DBD_USERNAME, DATA_POINT_DBD_PASSWORD),
            data_point_function_name=data_point_function_name,
        )

        response_data = response.get("result", [])
        status_code = response.get("status_code")

        logging_context = self.get_logging_context()
        data_point_log_info = {
            "url": url,
            "params": params,
            "context": logging_context,
            "status_code": status_code,
        }
        logger.info(f"DBD response {status_code}", data_point_log_info)

        if not status.is_success(status_code):
            raise DataPointResponseException(
                f"{self.title}: {ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'DBD'})}: {status_code}",
                data_point_log_info=data_point_log_info,
            )
        return response_data

    def get_creden_result(self, data: dict) -> dict:
        data_point_function_name = inspect.currentframe().f_code.co_name
        url = DATA_POINT_CREDEN_HOST
        headers = {
            "apikey": DATA_POINT_CREDEN_API_KEY,
        }
        response = self._request(
            method="POST",
            url=url,
            headers=headers,
            json=data,
            data_point_function_name=data_point_function_name,
        )

        response_data = response.get("result", {})
        status_code = response.get("status_code")

        logging_context = self.get_logging_context()
        data_point_log_info = {
            "url": url,
            "json": data,
            "context": logging_context,
            "status_code": status_code,
        }
        logger.info(f"Creden response {status_code}", data_point_log_info)

        if not status.is_success(status_code):
            raise DataPointResponseException(
                f"{self.title}: {ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'Creden'})}: {status_code}",
                data_point_log_info=data_point_log_info,
            )

        return response_data

    def get_comply_advantage_result(self, data: dict) -> dict:
        data_point_function_name = inspect.currentframe().f_code.co_name
        url = f"{DATA_POINT_COMPLY_ADVANTAGE_HOST}/searches"
        headers = {
            "Authorization": f"Token {DATA_POINT_COMPLY_ADVANTAGE_API_KEY}",
        }
        response = self._request(
            method="POST",
            url=url,
            headers=headers,
            json=data,
            data_point_function_name=data_point_function_name,
        )

        response_data = response.get("result", {})
        status_code = response.get("status_code")

        logging_context = self.get_logging_context()
        data_point_log_info = {
            "url": url,
            "json": data,
            "context": logging_context,
            "status_code": status_code,
        }

        logger.info(f"Comply Advantage response {status_code}", data_point_log_info)
        if not status.is_success(status_code):
            raise DataPointResponseException(
                f"{self.title}: {ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'Comply Advantage'})}: {status_code}",
                data_point_log_info=data_point_log_info,
            )

        # need to remove the `searcher` and `assignee`
        result_data = get(response_data, "content.data", {})
        result_data.pop("searcher", None)
        result_data.pop("assignee", None)

        return response_data

    def clean_dbd_data(self, dbd_data: dict) -> dict:
        partner = dbd_data.get("partner", [])
        raw_data = dbd_data.get("raw_data", {})
        empowerment = raw_data.get("empowerment", [])

        if partner:
            dbd_data["partner"] = [i.replace("/", "") for i in partner]

        if empowerment:
            dbd_data["raw_data"]["empowerment"] = [
                i.replace("/", "").replace("\n", "").strip() for i in empowerment
            ]

        return dbd_data

    def validate_input(
        self, juristic_id: str | None, country: str
    ) -> tuple[bool, str | None]:
        """Validate input that is valid for Business AML connection

        Validate checklist
        - juristic_id must not None or empty string.
        - juristic_id must be 13 digit only for Thailand company.
        - country value must be supported in BusinessAML.

        Args:
            national_id (str | None): national_id that need to validate
            country (str): national_id that need to validate

        Returns:
            tuple:
            * bool: True if inputs valid orElse, return False
            * str|None: Error message for display
        """
        import re

        COUNTRY_SUPPORT = AsiaVerifyApiV2.COUNTRY_SUPPORT 
        COUNTRY_SUPPORT.append('THA')
        
        is_input_valid = True
        error_message = None
        juristic_id_field: DataPointJSONField = self.fields.get("juristic_id")

        if not juristic_id:
            is_input_valid = False
            format_text = {"fields": juristic_id_field.label}
            error_message = (
                f"{self.title}: {ErrorMessage.EMPTY_VALUE.value.format(**format_text)}"
            )
        elif re.search(r"^\d{13}$", juristic_id) is None and country.upper() == "THA":
            is_input_valid = False
            format_text = {
                "field": juristic_id_field.label,
                "pattern_name": f"{country} company ID",
            }
            error_message = f"{self.title}: {ErrorMessage.NOT_MATCH_PATTERN.value.format(**format_text)}: {juristic_id}"
        elif country.upper() not in COUNTRY_SUPPORT:
            is_input_valid = False
            error_message = (
                f"{self.title}: {ErrorMessage.COUNTRY_UNSUPPORT.value}: {country}"
            )
        return is_input_valid, error_message

    def call_th_pep_check_shareholder_hits_non_thai(
            self, 
            shareholders_data_list_check_th_pep: list, 
            maximum_shareholders_list: list, 
            result: dict, 
            save_result: dict
        ):
        shareholders_with_25pct = []
        shareholders_with_15pct = []
        shareholders_with_10pct = []

        for shareholders_data_with_pct in shareholders_data_list_check_th_pep:
            if shareholders_data_with_pct['any_pct_data_point_shaheholders_hit'] == 'is_any_25pct_shareholders_th_pep_hits':
                shareholders_with_25pct = shareholders_data_with_pct['shareholders_name_list']
            elif shareholders_data_with_pct['any_pct_data_point_shaheholders_hit'] == 'is_any_15pct_shareholders_th_pep_hits':
                shareholders_with_15pct = shareholders_data_with_pct['shareholders_name_list']
            elif shareholders_data_with_pct['any_pct_data_point_shaheholders_hit'] == 'is_any_10pct_shareholders_th_pep_hits':
                shareholders_with_10pct = shareholders_data_with_pct['shareholders_name_list']
        
        for name in maximum_shareholders_list:
            director_name_without_prefix = self.remove_prefix(name)
            local_thai_pep_request_body = {
                "full_name": director_name_without_prefix
            }
            local_thai_pep_list_response = self.search_thai_pep_list(local_thai_pep_request_body)
            save_result['local_thai_pep_list'].append({
                'input': local_thai_pep_request_body,
                'output': local_thai_pep_list_response,
                "additional_infomation": {"fullname": name},
            })
            local_thai_pep_info: dict = local_thai_pep_list_response.get('data')
            is_pep_hit = False
            if local_thai_pep_info:
                is_pep_hit = True
            if name in shareholders_with_25pct:
                # skip this condition when the `is_any_{input percent}pct_shareholders_with_hits` is True
                if result['is_any_25pct_shareholders_th_pep_hits'] is True:
                    continue
                result['is_any_25pct_shareholders_th_pep_hits'] = is_pep_hit
            if name in shareholders_with_15pct:
                # skip this condition when the `is_any_{input percent}pct_shareholders_with_hits` is True
                if result['is_any_15pct_shareholders_th_pep_hits'] is True:
                    continue
                result['is_any_15pct_shareholders_th_pep_hits'] = is_pep_hit

            if name in shareholders_with_10pct:
                # skip this condition when the `is_any_{input percent}pct_shareholders_with_hits` is True
                if result['is_any_10pct_shareholders_th_pep_hits'] is True:
                    continue
                result['is_any_10pct_shareholders_th_pep_hits'] = is_pep_hit
            
    
    def call_comply_advantage_check_shareholder_hits_non_thai(self, shareholders_data_list_check_ca: list, maximum_shareholders_list: list, result: dict, save_result: dict):
        '''
        List of shareholders are checked hit by calling comply advantage API and overwritting the result

        input data:

            shareholders_data_list_check_ca = {
                'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                'shareholders_name_list': shareholders_name_list
            }
            maximum_shareholders_list = [shareholder_name, ...]
        '''
        shareholders_with_25pct = []
        shareholders_with_15pct = []
        shareholders_with_10pct = []

        for shareholders_data_with_pct in shareholders_data_list_check_ca:
            if shareholders_data_with_pct['any_pct_data_point_shaheholders_hit'] == 'is_any_25pct_shareholders_with_hits':
                shareholders_with_25pct = shareholders_data_with_pct['shareholders_name_list']
            elif shareholders_data_with_pct['any_pct_data_point_shaheholders_hit'] == 'is_any_15pct_shareholders_with_hits':
                shareholders_with_15pct = shareholders_data_with_pct['shareholders_name_list']
            elif shareholders_data_with_pct['any_pct_data_point_shaheholders_hit'] == 'is_any_10pct_shareholders_with_hits':
                shareholders_with_10pct = shareholders_data_with_pct['shareholders_name_list']

        for name in maximum_shareholders_list:
            shareholders_ca_request_body = {
                'search_term': name,
                **DATA_POINT_COMPLY_ADVANTAGE_PERSON_FILTER,
            }
            shareholders_ca_result = self.get_comply_advantage_result(shareholders_ca_request_body)
            save_result['comply_advantage'].append({
                'input': shareholders_ca_request_body,
                'output': shareholders_ca_result,
            })
            shareholders_total_hits = get(shareholders_ca_result, 'content.data.total_hits')
            shareholder_hit = False
            if shareholders_total_hits is not None and shareholders_total_hits > 0:
                shareholder_hit = True
            if name in shareholders_with_25pct:
                # skip this condition when the `is_any_{input percent}pct_shareholders_with_hits` is True
                if result['is_any_25pct_shareholders_with_hits'] is True:
                    continue
                result['is_any_25pct_shareholders_with_hits'] = shareholder_hit
            if name in shareholders_with_15pct:
                # skip this condition when the `is_any_{input percent}pct_shareholders_with_hits` is True
                if result['is_any_15pct_shareholders_with_hits'] is True:
                    continue
                result['is_any_15pct_shareholders_with_hits'] = shareholder_hit
            if name in shareholders_with_10pct:
                # skip this condition when the `is_any_{input percent}pct_shareholders_with_hits` is True
                if result['is_any_10pct_shareholders_with_hits'] is True:
                    continue
                result['is_any_10pct_shareholders_with_hits'] = shareholder_hit

    def call_comply_advantage_director_not_thai(self, name: str, result: dict, save_result: dict):
        major_person_ca_request_body = {
            'search_term': name,
            **DATA_POINT_COMPLY_ADVANTAGE_PERSON_FILTER,
        }
        major_person_ca_result = self.get_comply_advantage_result(major_person_ca_request_body)
        save_result['comply_advantage'].append({
            'input': major_person_ca_request_body,
            'output': major_person_ca_result,
        })
        major_person_total_hits = get(major_person_ca_result, 'content.data.total_hits')
        
        # skip this condition when the `is_any_directors_with_hits` is True
        if result['is_any_directors_with_hits'] is True:
            return
        if major_person_total_hits is not None and major_person_total_hits > 0:
            result['is_any_directors_with_hits'] = True

    def call_th_pep_director_not_thai(self, name: str, result: dict, save_result: dict):
        director_name_without_prefix = self.remove_prefix(name)
        local_thai_pep_request_body = {
            "full_name": director_name_without_prefix
        }
        local_thai_pep_list_response = self.search_thai_pep_list(local_thai_pep_request_body)
        save_result['local_thai_pep_list'].append({
            'input': local_thai_pep_request_body,
            'output': local_thai_pep_list_response,
            "additional_infomation": {"fullname": name},
        })
        if result['is_any_directors_th_pep_hits'] is True:
            return
        local_thai_pep_info: dict = local_thai_pep_list_response.get('data')
        if local_thai_pep_info:
            result["is_any_directors_th_pep_hits"] = True
            result["is_any_directors_with_hits"] = True

    def call_comply_advantage_company_name_not_thai(self, company_name: str, result: dict, save_result: dict):
        company_ca_request_body = {
            'search_term': company_name,
            **DATA_POINT_COMPLY_ADVANTAGE_COMPANY_FILTER,
        }
        company_ca_result = self.get_comply_advantage_result(company_ca_request_body)
        save_result['comply_advantage'].append({
            'input': company_ca_request_body,
            'output': company_ca_result,
        })
        company_ca_total_hits = get(company_ca_result, 'content.data.total_hits')
        if company_ca_total_hits is not None and company_ca_total_hits > 0:
            result['is_company_with_hits'] = True

    def data_point_option_result_for_japan(self, company_information: dict, pct_share_data_point_ca_hits_list: list, pct_share_data_point_th_pep_hits_list: list) -> tuple[dict, dict[list]] :
        ''' Validate company_infomation to get datapoint options result by Japan rules.
        
        Args:
            company_information (dict): company information that come from api service

        Returns:
            tuple:
            * Data point options result in index 0
            * Comply Adventage result in index 1 for save db
        '''
        # Initial Comply Adventage api result and data point options result
        save_result = {
            'comply_advantage': [],
            'local_thai_pep_list': []
        }

        result = {
            'is_company_with_hits': False,
            'is_any_directors_with_hits': False,
            'is_any_directors_th_pep_hits': False,
            'is_any_25pct_shareholders_with_hits': False,
            'is_any_15pct_shareholders_with_hits': False,
            'is_any_10pct_shareholders_with_hits': False,
            'is_any_25pct_shareholders_th_pep_hits': False,
            'is_any_15pct_shareholders_th_pep_hits': False,
            'is_any_10pct_shareholders_th_pep_hits': False,
        }
        
        if self.is_in_option("is_company_with_hits"):
            # Call Comply adventage(ca) with company name for validate is_company_with_hits
            company_information_data = get(
                company_information, 
                'CompanyRegistrationInformation.data[0]'
            )
            company_name: str = get(company_information_data, 'enName', '')
            if not company_name:
                company_name = get(company_information_data, 'ogName', '')
            self.call_comply_advantage_company_name_not_thai(company_name, result, save_result)
        
        check_director_with_hits = self.is_in_option("is_any_directors_with_hits")
        check_director_with_th_pep_this = self.is_in_option(
            "is_any_directors_th_pep_hits"
        )
        if check_director_with_hits or check_director_with_th_pep_this:
            # Call Comply adventage(ca) with director names for validate is_any_directors_with_hits
            major_person_data: list = get(company_information, "majorPerson.data")
            major_person_name_list = [
                re.search(r"Director (?:and President )?(.+),?", entity["rdPersonnel"])[
                    1
                ]
                for entity in major_person_data
                if re.search(
                    r"Director (?:and President )?(.+),?", entity["rdPersonnel"]
                )
            ]
            if not major_person_name_list:
                for entity in major_person_data:
                    name_split = entity["rdPersonnel"].split()
                    if len(name_split) >= 2:
                        major_person_name_list.append(" ".join(name_split[-2:]))
                    elif name_split:
                         major_person_name_list.append(name_split[-1])

            for name in major_person_name_list:
                if check_director_with_hits:
                    self.call_comply_advantage_director_not_thai(
                        name, result, save_result
                    )
                    self.call_th_pep_director_not_thai(name, result, save_result)
                if check_director_with_th_pep_this and not check_director_with_hits:
                    self.call_th_pep_director_not_thai(name, result, save_result)

        # Call Comply adventage(ca) with shareholder names for validate is_any_25pct_shareholders_with_hits
        shareholders_data: list = get(company_information, "Shareholders.data")
        shareholders_data_list_check_ca = []
        shareholders_data_list_check_th_pep = []
        maximum_shareholders_list = []
        maximum_shareholders_th_pep_list = []
        if (
            "is_any_25pct_shareholders_with_hits" in pct_share_data_point_ca_hits_list
            or "is_any_25pct_shareholders_th_pep_hits"
            in pct_share_data_point_th_pep_hits_list
        ):
            shareholders_name_list = [
                entity["shareholderName"]
                for entity in shareholders_data
                if float(get(entity, "proportion", 0.0)) >= 25
            ]
            if (
                "is_any_25pct_shareholders_with_hits"
                in pct_share_data_point_ca_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_25pct_shareholders_with_hits"
                )
                shareholders_data_list_check_ca.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_list):
                    maximum_shareholders_list = shareholders_name_list
            if (
                "is_any_25pct_shareholders_th_pep_hits"
                in pct_share_data_point_th_pep_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_25pct_shareholders_th_pep_hits"
                )
                shareholders_data_list_check_th_pep.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                    maximum_shareholders_th_pep_list = shareholders_name_list
        if (
            "is_any_15pct_shareholders_with_hits" in pct_share_data_point_ca_hits_list
            or "is_any_15pct_shareholders_th_pep_hits"
            in pct_share_data_point_th_pep_hits_list
        ):
            shareholders_name_list = [
                entity["shareholderName"]
                for entity in shareholders_data
                if float(get(entity, "proportion", 0.0)) >= 15
            ]
            if (
                "is_any_15pct_shareholders_with_hits"
                in pct_share_data_point_ca_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_15pct_shareholders_with_hits"
                )
                shareholders_data_list_check_ca.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_list):
                    maximum_shareholders_list = shareholders_name_list
            if (
                "is_any_15pct_shareholders_th_pep_hits"
                in pct_share_data_point_th_pep_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_15pct_shareholders_th_pep_hits"
                )
                shareholders_data_list_check_th_pep.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                    maximum_shareholders_th_pep_list = shareholders_name_list
        if (
            "is_any_10pct_shareholders_with_hits" in pct_share_data_point_ca_hits_list
            or "is_any_10pct_shareholders_th_pep_hits"
            in pct_share_data_point_th_pep_hits_list
        ):
            shareholders_name_list = [
                entity["shareholderName"]
                for entity in shareholders_data
                if float(get(entity, "proportion", 0.0)) >= 10
            ]
            if (
                "is_any_10pct_shareholders_with_hits"
                in pct_share_data_point_ca_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_10pct_shareholders_with_hits"
                )
                shareholders_data_list_check_ca.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_list):
                    maximum_shareholders_list = shareholders_name_list
            if (
                "is_any_10pct_shareholders_th_pep_hits"
                in pct_share_data_point_th_pep_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_10pct_shareholders_th_pep_hits"
                )
                shareholders_data_list_check_th_pep.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                    maximum_shareholders_th_pep_list = shareholders_name_list

        if shareholders_data_list_check_ca:
            self.call_comply_advantage_check_shareholder_hits_non_thai(
                shareholders_data_list_check_ca,
                maximum_shareholders_list,
                result,
                save_result,
            )
        if shareholders_data_list_check_th_pep:
            self.call_th_pep_check_shareholder_hits_non_thai(
                shareholders_data_list_check_th_pep,
                maximum_shareholders_list,
                result,
                save_result,
            )
        return result, save_result

    def data_point_option_result_for_singapore(
        self,
        company_information: dict,
        pct_share_data_point_ca_hits_list: list,
        pct_share_data_point_th_pep_hits_list: list,
    ) -> tuple[dict, dict[list]]:
        # Initial Comply Adventage api result and data point options result
        save_result = {"comply_advantage": [], "local_thai_pep_list": []}

        result = {
            "is_company_with_hits": False,
            "is_any_directors_with_hits": False,
            "is_any_directors_th_pep_hits": False,
            "is_any_25pct_shareholders_with_hits": False,
            "is_any_15pct_shareholders_with_hits": False,
            "is_any_10pct_shareholders_with_hits": False,
            "is_any_25pct_shareholders_th_pep_hits": False,
            "is_any_15pct_shareholders_th_pep_hits": False,
            "is_any_10pct_shareholders_th_pep_hits": False,
        }

        if self.is_in_option("is_company_with_hits"):
            # Call Comply adventage(ca) with company name for validate is_company_with_hits
            company_information_data = get(
                company_information, "CompanyRegistrationInformation.data[0]"
            )
            company_name: str = get(company_information_data, "companyName", "")
            self.call_comply_advantage_company_name_not_thai(
                company_name, result, save_result
            )

        check_director_with_hits = self.is_in_option("is_any_directors_with_hits")
        check_director_with_th_pep_this = self.is_in_option(
            "is_any_directors_th_pep_hits"
        )
        if check_director_with_hits or check_director_with_th_pep_this:
            # Call Comply adventage(ca) with director names for validate is_any_directors_with_hits
            major_person_data: list = get(company_information, "majorPerson.data")
            major_person_name_list = [
                entity["name"]
                for entity in major_person_data
                if str(entity["position"]).lower() == "director"
            ]
            for name in major_person_name_list:
                if check_director_with_hits:
                    self.call_comply_advantage_director_not_thai(
                        name, result, save_result
                    )
                    self.call_th_pep_director_not_thai(name, result, save_result)
                if check_director_with_th_pep_this and not check_director_with_hits:
                    self.call_th_pep_director_not_thai(name, result, save_result)

        # Call Comply adventage(ca) with shareholder names for validate is_any_25pct_shareholders_with_hits
        shareholders_data: list = get(
            company_information, "Shareholders.data.individual"
        )
        shareholders_data_list_check_ca = []
        shareholders_data_list_check_th_pep = []
        maximum_shareholders_list = []
        maximum_shareholders_th_pep_list = []

        if (
            "is_any_25pct_shareholders_with_hits" in pct_share_data_point_ca_hits_list
            or "is_any_25pct_shareholders_th_pep_hits"
            in pct_share_data_point_th_pep_hits_list
        ):
            shareholders_name_list = [
                entity["name"]
                for entity in shareholders_data
                if float(get(entity, "shareAmount", 0)) >= 25
            ]
            if (
                "is_any_25pct_shareholders_with_hits"
                in pct_share_data_point_ca_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_25pct_shareholders_with_hits"
                )
                shareholders_data_list_check_ca.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_list):
                    maximum_shareholders_list = shareholders_name_list
            if (
                "is_any_25pct_shareholders_th_pep_hits"
                in pct_share_data_point_th_pep_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_25pct_shareholders_th_pep_hits"
                )
                shareholders_data_list_check_th_pep.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                    maximum_shareholders_th_pep_list = shareholders_name_list
        if (
            "is_any_15pct_shareholders_with_hits" in pct_share_data_point_ca_hits_list
            or "is_any_15pct_shareholders_th_pep_hits"
            in pct_share_data_point_th_pep_hits_list
        ):
            shareholders_name_list = [
                entity["name"]
                for entity in shareholders_data
                if float(get(entity, "shareAmount", 0)) >= 15
            ]
            if (
                "is_any_15pct_shareholders_with_hits"
                in pct_share_data_point_ca_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_15pct_shareholders_with_hits"
                )
                shareholders_data_list_check_ca.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_list):
                    maximum_shareholders_list = shareholders_name_list
            if (
                "is_any_15pct_shareholders_th_pep_hits"
                in pct_share_data_point_th_pep_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_15pct_shareholders_th_pep_hits"
                )
                shareholders_data_list_check_th_pep.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                    maximum_shareholders_th_pep_list = shareholders_name_list
        if (
            "is_any_10pct_shareholders_with_hits" in pct_share_data_point_ca_hits_list
            or "is_any_10pct_shareholders_th_pep_hits"
            in pct_share_data_point_th_pep_hits_list
        ):
            shareholders_name_list = [
                entity["name"]
                for entity in shareholders_data
                if float(get(entity, "shareAmount", 0)) >= 10
            ]
            if (
                "is_any_10pct_shareholders_with_hits"
                in pct_share_data_point_ca_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_10pct_shareholders_with_hits"
                )
                shareholders_data_list_check_ca.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_list):
                    maximum_shareholders_list = shareholders_name_list
            if (
                "is_any_10pct_shareholders_th_pep_hits"
                in pct_share_data_point_th_pep_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_10pct_shareholders_th_pep_hits"
                )
                shareholders_data_list_check_th_pep.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                    maximum_shareholders_th_pep_list = shareholders_name_list
        if shareholders_data_list_check_ca:
            self.call_comply_advantage_check_shareholder_hits_non_thai(
                shareholders_data_list_check_ca,
                maximum_shareholders_list,
                result,
                save_result,
            )
        if shareholders_data_list_check_th_pep:
            self.call_th_pep_check_shareholder_hits_non_thai(
                shareholders_data_list_check_th_pep,
                maximum_shareholders_list,
                result,
                save_result,
            )
        # Return result
        return result, save_result

    def data_point_option_result_for_malaysia(
        self,
        company_information: dict,
        pct_share_data_point_ca_hits_list: list,
        pct_share_data_point_th_pep_hits_list: list,
    ) -> tuple[dict, dict[list]]:
        # Initial Comply Adventage api result and data point options result
        save_result = {"comply_advantage": [], "local_thai_pep_list": []}

        result = {
            "is_company_with_hits": False,
            "is_any_directors_with_hits": False,
            "is_any_directors_th_pep_hits": False,
            "is_any_25pct_shareholders_with_hits": False,
            "is_any_15pct_shareholders_with_hits": False,
            "is_any_10pct_shareholders_with_hits": False,
            "is_any_25pct_shareholders_th_pep_hits": False,
            "is_any_15pct_shareholders_th_pep_hits": False,
            "is_any_10pct_shareholders_th_pep_hits": False,
        }

        if self.is_in_option("is_company_with_hits"):
            # Call Comply adventage(ca) with company name for validate is_company_with_hits
            company_information_data = get(
                company_information, "CompanyRegistrationInformation.data[0]"
            )
            company_name: str = get(company_information_data, "companyName", "")
            self.call_comply_advantage_company_name_not_thai(
                company_name, result, save_result
            )

        check_director_with_hits = self.is_in_option("is_any_directors_with_hits")
        check_director_with_th_pep_this = self.is_in_option(
            "is_any_directors_th_pep_hits"
        )
        if check_director_with_hits or check_director_with_th_pep_this:
            # Call Comply adventage(ca) with director names for validate is_any_directors_with_hits
            major_person_data: list = get(company_information, "majorPerson.data")
            major_person_name_list = [
                entity["name"]
                for entity in major_person_data
                if str(entity["position"]).lower() == "director"
            ]
            for name in major_person_name_list:
                if check_director_with_hits:
                    self.call_comply_advantage_director_not_thai(
                        name, result, save_result
                    )
                    self.call_th_pep_director_not_thai(name, result, save_result)
                if check_director_with_th_pep_this and not check_director_with_hits:
                    self.call_th_pep_director_not_thai(name, result, save_result)

        # Call Comply adventage(ca) with shareholder names for validate is_any_25pct_shareholders_with_hits
        shareholders_data: list = get(company_information, "Shareholders.data")

        shareholders_data_list_check_ca = []
        shareholders_data_list_check_th_pep = []
        maximum_shareholders_list = []
        maximum_shareholders_th_pep_list = []

        if (
            "is_any_25pct_shareholders_with_hits" in pct_share_data_point_ca_hits_list
            or "is_any_25pct_shareholders_th_pep_hits"
            in pct_share_data_point_th_pep_hits_list
        ):
            shareholders_name_list = [
                entity["stockName"]
                for entity in shareholders_data
                if float(get(entity, "shareNumber", 0)) >= 25
            ]
            if (
                "is_any_25pct_shareholders_with_hits"
                in pct_share_data_point_ca_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_25pct_shareholders_with_hits"
                )
                shareholders_data_list_check_ca.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_list):
                    maximum_shareholders_list = shareholders_name_list
            if (
                "is_any_25pct_shareholders_th_pep_hits"
                in pct_share_data_point_th_pep_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_25pct_shareholders_th_pep_hits"
                )
                shareholders_data_list_check_th_pep.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                    maximum_shareholders_th_pep_list = shareholders_name_list
        if (
            "is_any_15pct_shareholders_with_hits" in pct_share_data_point_ca_hits_list
            or "is_any_15pct_shareholders_th_pep_hits"
            in pct_share_data_point_th_pep_hits_list
        ):
            shareholders_name_list = [
                entity["stockName"]
                for entity in shareholders_data
                if float(get(entity, "shareNumber", 0)) >= 15
            ]
            if (
                "is_any_15pct_shareholders_with_hits"
                in pct_share_data_point_ca_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_15pct_shareholders_with_hits"
                )
                shareholders_data_list_check_ca.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_list):
                    maximum_shareholders_list = shareholders_name_list
            if (
                "is_any_15pct_shareholders_th_pep_hits"
                in pct_share_data_point_th_pep_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_15pct_shareholders_th_pep_hits"
                )
                shareholders_data_list_check_th_pep.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                    maximum_shareholders_th_pep_list = shareholders_name_list
        if (
            "is_any_10pct_shareholders_with_hits" in pct_share_data_point_ca_hits_list
            or "is_any_10pct_shareholders_th_pep_hits"
            in pct_share_data_point_th_pep_hits_list
        ):
            shareholders_name_list = [
                entity["stockName"]
                for entity in shareholders_data
                if float(get(entity, "shareNumber", 0)) >= 10
            ]
            if (
                "is_any_10pct_shareholders_with_hits"
                in pct_share_data_point_ca_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_10pct_shareholders_with_hits"
                )
                shareholders_data_list_check_ca.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_list):
                    maximum_shareholders_list = shareholders_name_list
            if (
                "is_any_10pct_shareholders_th_pep_hits"
                in pct_share_data_point_th_pep_hits_list
            ):
                any_pct_data_point_shaheholders_hit = (
                    "is_any_10pct_shareholders_th_pep_hits"
                )
                shareholders_data_list_check_th_pep.append(
                    {
                        "any_pct_data_point_shaheholders_hit": any_pct_data_point_shaheholders_hit,
                        "shareholders_name_list": shareholders_name_list,
                    }
                )
                if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                    maximum_shareholders_th_pep_list = shareholders_name_list
        if shareholders_data_list_check_ca:
            self.call_comply_advantage_check_shareholder_hits_non_thai(
                shareholders_data_list_check_ca,
                maximum_shareholders_list,
                result,
                save_result,
            )
        if shareholders_data_list_check_th_pep:
            self.call_th_pep_check_shareholder_hits_non_thai(
                shareholders_data_list_check_th_pep,
                maximum_shareholders_list,
                result,
                save_result,
            )
        # Return result
        return result, save_result

    def data_point_option_result_for_hongkong(
        self,
        company_information: dict,
        pct_share_data_point_ca_hits_list: list,
        pct_share_data_point_th_pep_hits_list: list,
    ) -> tuple[dict, dict[list]]:
        # Initial Comply Adventage api result and data point options result
        save_result = {"comply_advantage": [], "local_thai_pep_list": []}

        result = {
            "is_company_with_hits": False,
            "is_any_directors_with_hits": False,
            "is_any_directors_th_pep_hits": False,
            "is_any_25pct_shareholders_with_hits": False,
            "is_any_15pct_shareholders_with_hits": False,
            "is_any_10pct_shareholders_with_hits": False,
            "is_any_25pct_shareholders_th_pep_hits": False,
            "is_any_15pct_shareholders_th_pep_hits": False,
            "is_any_10pct_shareholders_th_pep_hits": False,
        }

        if self.is_in_option("is_company_with_hits"):
            # Call Comply adventage(ca) with company name for validate is_company_with_hits
            company_information_data = get(
                company_information, "CompanyRegistrationInformation.data"
            )
            company_name: str = get(company_information_data, "enName", "")
            if not company_name:
                company_name = get(company_information_data, "ogName", "")
            self.call_comply_advantage_company_name_not_thai(
                company_name, result, save_result
            )

        check_director_with_hits = self.is_in_option("is_any_directors_with_hits")
        check_director_with_th_pep_this = self.is_in_option(
            "is_any_directors_th_pep_hits"
        )
        if check_director_with_hits or check_director_with_th_pep_this:
            # Call Comply adventage(ca) with director names for validate is_any_directors_with_hits
            major_person_data: list = get(
                company_information,
                'majorPerson.data.details'
            )
            major_person_name_list = [
                entity['enFullName'] for entity in major_person_data 
                if str(entity['position']).lower() == 'director'
            ]
            for name in major_person_name_list:
                if check_director_with_hits:
                    self.call_comply_advantage_director_not_thai(
                        name, result, save_result
                    )
                    self.call_th_pep_director_not_thai(name, result, save_result)
                if check_director_with_th_pep_this and not check_director_with_hits:
                    self.call_th_pep_director_not_thai(name, result, save_result)
        
        # Call Comply adventage(ca) with shareholder names for validate is_any_25pct_shareholders_with_hits
        shareholders_data: list = get(
            company_information, 
            'Shareholders.data.details'
        )
        
        shareholders_data_list_check_ca = []
        shareholders_data_list_check_th_pep = []
        maximum_shareholders_list = []
        maximum_shareholders_th_pep_list = []

        if 'is_any_25pct_shareholders_with_hits' in pct_share_data_point_ca_hits_list or 'is_any_25pct_shareholders_th_pep_hits' in pct_share_data_point_th_pep_hits_list:
            shareholders_name_list = [
                entity['stockName'] for entity in shareholders_data 
                if float(str(get(entity, 'percent', '0')).replace('%', '')) >= 25.0
            ]
            if 'is_any_25pct_shareholders_with_hits' in pct_share_data_point_ca_hits_list:
                any_pct_data_point_shaheholders_hit = 'is_any_25pct_shareholders_with_hits'
                shareholders_data_list_check_ca.append({
                    'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                    'shareholders_name_list': shareholders_name_list
                })
                if len(shareholders_name_list) > len(maximum_shareholders_list):
                    maximum_shareholders_list = shareholders_name_list
            if 'is_any_25pct_shareholders_th_pep_hits' in pct_share_data_point_th_pep_hits_list:
                any_pct_data_point_shaheholders_hit = 'is_any_25pct_shareholders_th_pep_hits'
                shareholders_data_list_check_th_pep.append({
                    'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                    'shareholders_name_list': shareholders_name_list
                })
                if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                    maximum_shareholders_th_pep_list = shareholders_name_list
        if 'is_any_15pct_shareholders_with_hits' in pct_share_data_point_ca_hits_list or 'is_any_15pct_shareholders_th_pep_hits' in pct_share_data_point_th_pep_hits_list:
            shareholders_name_list = [
                entity['stockName'] for entity in shareholders_data 
                if float(str(get(entity, 'percent', '0')).replace('%', '')) >= 15.0
            ]
            if 'is_any_15pct_shareholders_with_hits' in pct_share_data_point_ca_hits_list:
                any_pct_data_point_shaheholders_hit = 'is_any_15pct_shareholders_with_hits'
                shareholders_data_list_check_ca.append({
                    'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                    'shareholders_name_list': shareholders_name_list
                })
                if len(shareholders_name_list) > len(maximum_shareholders_list):
                    maximum_shareholders_list = shareholders_name_list
            if 'is_any_15pct_shareholders_th_pep_hits' in pct_share_data_point_th_pep_hits_list:
                any_pct_data_point_shaheholders_hit = 'is_any_15pct_shareholders_th_pep_hits'
                shareholders_data_list_check_th_pep.append({
                    'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                    'shareholders_name_list': shareholders_name_list
                })
                if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                    maximum_shareholders_th_pep_list = shareholders_name_list
        if 'is_any_10pct_shareholders_with_hits' in pct_share_data_point_ca_hits_list or 'is_any_10pct_shareholders_th_pep_hits' in pct_share_data_point_th_pep_hits_list:
            shareholders_name_list = [
                entity['stockName'] for entity in shareholders_data 
                if float(str(get(entity, 'percent', '0')).replace('%', '')) >= 10.0
            ]
            if 'is_any_10pct_shareholders_with_hits' in pct_share_data_point_ca_hits_list:
                any_pct_data_point_shaheholders_hit = 'is_any_10pct_shareholders_with_hits'
                shareholders_data_list_check_ca.append({
                    'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                    'shareholders_name_list': shareholders_name_list
                })
                if len(shareholders_name_list) > len(maximum_shareholders_list):
                    maximum_shareholders_list = shareholders_name_list
            if 'is_any_10pct_shareholders_th_pep_hits' in pct_share_data_point_th_pep_hits_list:
                any_pct_data_point_shaheholders_hit = 'is_any_10pct_shareholders_th_pep_hits'
                shareholders_data_list_check_th_pep.append({
                    'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                    'shareholders_name_list': shareholders_name_list
                })
                if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                    maximum_shareholders_th_pep_list = shareholders_name_list
        if shareholders_data_list_check_ca:
            self.call_comply_advantage_check_shareholder_hits_non_thai(shareholders_data_list_check_ca, maximum_shareholders_list, result, save_result)
        if shareholders_data_list_check_th_pep:
            self.call_th_pep_check_shareholder_hits_non_thai(shareholders_data_list_check_th_pep, maximum_shareholders_list, result, save_result)
        # Return result
        return result, save_result
    
    def data_point_option_result_for_philippines(self, company_information: dict, pct_share_data_point_ca_hits_list: list, pct_share_data_point_th_pep_hits_list: list) -> tuple[dict, dict[list]] :
        # Initial Comply Adventage api result and data point options result
        save_result = {
            'comply_advantage': [],
            'local_thai_pep_list': []
        }

        result = {
            'is_company_with_hits': False,
            'is_any_directors_with_hits': False,
            'is_any_directors_th_pep_hits': False,
            'is_any_25pct_shareholders_with_hits': False,
            'is_any_15pct_shareholders_with_hits': False,
            'is_any_10pct_shareholders_with_hits': False,
            'is_any_25pct_shareholders_th_pep_hits': False,
            'is_any_15pct_shareholders_th_pep_hits': False,
            'is_any_10pct_shareholders_th_pep_hits': False,
        }
        
        if self.is_in_option("is_company_with_hits"):
            # Call Comply adventage(ca) with company name for validate is_company_with_hits
            company_information_data = get(company_information, 'CompanyInformation.data.0', {})
            company_name = get(company_information_data, 'name.companyName_other.0.otherName')
            self.call_comply_advantage_company_name_not_thai(company_name, result, save_result)
        
        check_director_with_hits = self.is_in_option("is_any_directors_with_hits")
        check_director_with_th_pep_this = self.is_in_option("is_any_directors_th_pep_hits")
        if check_director_with_hits or check_director_with_th_pep_this:
            # Call Comply adventage(ca) with director names for validate is_any_directors_with_hits
            major_person_data: list = get(
                company_information,
                'majorPerson.data'
            )
            major_person_name_list = [
                entity['enName'] for entity in major_person_data 
                if str(entity['role']).lower() == 'director'
            ]
            for name in major_person_name_list:
                if check_director_with_hits:
                    self.call_comply_advantage_director_not_thai(name, result, save_result)
                if check_director_with_th_pep_this:
                    self.call_th_pep_director_not_thai(name, result, save_result)
        
        # Call Comply adventage(ca) with shareholder names for validate is_any_25pct_shareholders_with_hits
        shareholders_data_list: list = get(
            company_information, "Shareholders.data.individual"
        )
        total_shares = 0
        shareholders_data_list_check_ca = []
        shareholders_data_list_check_th_pep = []
        maximum_shareholders_list = []
        maximum_shareholders_th_pep_list = []

        shareholders_name_25pct_list = []
        shareholders_name_15pct_list = []
        shareholders_name_10pct_list = []
        shareholders_name_25pct_list_th_pep = []
        shareholders_name_15pct_list_th_pep = []
        shareholders_name_10pct_list_th_pep = []

        for shareholders_data in shareholders_data_list:
            total_shares += self.__convert_money_to_float(get(shareholders_data, 'numberOfShares', 0))

        for shareholders_data in shareholders_data_list:
            total_shares += self.__convert_money_to_float(
                get(shareholders_data, "numberOfShares", 0)
            )

        for shareholders_data in shareholders_data_list:
            number_of_shares = self.__convert_money_to_float(
                get(shareholders_data, "numberOfShares", 0)
            )
            if total_shares == 0:
                continue

            # separate shareholder of philippines 25% => 0.25, 15% => 0.15, 10% => 0.10
            if '25pct' in pct_share_data_point_ca_hits_list and number_of_shares/total_shares >= 0.25:
                shareholders_name_25pct_list.append(get(shareholders_data, 'shareholderNameEN', ''))
            if 'is_any_15pct_shareholders_with_hits' in pct_share_data_point_ca_hits_list and number_of_shares/total_shares >= 0.15:
                shareholders_name_15pct_list.append(get(shareholders_data, 'shareholderNameEN', ''))
            if 'is_any_10pct_shareholders_with_hits' in pct_share_data_point_ca_hits_list and number_of_shares/total_shares >= 0.10:
                shareholders_name_10pct_list.append(get(shareholders_data, 'shareholderNameEN', ''))
            if '25pct' in pct_share_data_point_th_pep_hits_list and number_of_shares/total_shares >= 0.25:
                shareholders_name_25pct_list_th_pep.append(get(shareholders_data, 'shareholderNameEN', ''))
            if 'is_any_15pct_shareholders_th_pep_hits' in pct_share_data_point_th_pep_hits_list and number_of_shares/total_shares >= 0.15:
                shareholders_name_15pct_list_th_pep.append(get(shareholders_data, 'shareholderNameEN', ''))
            if 'is_any_10pct_shareholders_th_pep_hits' in pct_share_data_point_th_pep_hits_list and number_of_shares/total_shares >= 0.10:
                shareholders_name_10pct_list_th_pep.append(get(shareholders_data, 'shareholderNameEN', ''))

        if len(shareholders_name_25pct_list) > 0:
            any_pct_data_point_shaheholders_hit = 'is_any_25pct_shareholders_with_hits'
            shareholders_name_list = shareholders_name_25pct_list
            shareholders_data_list_check_ca.append({
                'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                'shareholders_name_list': shareholders_name_list
            })
            if len(shareholders_name_list) > len(maximum_shareholders_list):
                maximum_shareholders_list = shareholders_name_list

        if len(shareholders_name_25pct_list_th_pep) > 0:
            any_pct_data_point_shaheholders_hit = 'is_any_25pct_shareholders_th_pep_hits'
            shareholders_name_list = shareholders_name_25pct_list_th_pep
            shareholders_data_list_check_ca.append({
                'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                'shareholders_name_list': shareholders_name_list
            })
            if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                maximum_shareholders_th_pep_list = shareholders_name_list
        if len(shareholders_name_15pct_list) > 0:

            any_pct_data_point_shaheholders_hit = 'is_any_15pct_shareholders_with_hits'
            shareholders_name_list = shareholders_name_15pct_list
            shareholders_data_list_check_ca.append({
                'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                'shareholders_name_list': shareholders_name_list
            })
            if len(shareholders_name_list) > len(maximum_shareholders_list):
                maximum_shareholders_list = shareholders_name_list

        if len(shareholders_name_15pct_list_th_pep) > 0:
            any_pct_data_point_shaheholders_hit = 'is_any_15pct_shareholders_th_pep_hits'
            shareholders_name_list = shareholders_name_15pct_list_th_pep
            shareholders_data_list_check_ca.append({
                'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                'shareholders_name_list': shareholders_name_list
            })
            if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                maximum_shareholders_th_pep_list = shareholders_name_list

        if len(shareholders_name_10pct_list) > 0:
            any_pct_data_point_shaheholders_hit = 'is_any_10pct_shareholders_with_hits'
            shareholders_name_list = shareholders_name_10pct_list
            shareholders_data_list_check_ca.append({
                'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                'shareholders_name_list': shareholders_name_list
            })
            if len(shareholders_name_list) > len(maximum_shareholders_list):
                maximum_shareholders_list = shareholders_name_list

        if len(shareholders_name_10pct_list_th_pep) > 0:
            any_pct_data_point_shaheholders_hit = 'is_any_10pct_shareholders_th_pep_hits'
            shareholders_name_list = shareholders_name_10pct_list_th_pep
            shareholders_data_list_check_ca.append({
                'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                'shareholders_name_list': shareholders_name_list
            })
            if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                maximum_shareholders_th_pep_list = shareholders_name_list

        if shareholders_data_list_check_ca:
            self.call_comply_advantage_check_shareholder_hits_non_thai(shareholders_data_list_check_ca, maximum_shareholders_list, result, save_result)
        if shareholders_data_list_check_th_pep:
            self.call_th_pep_check_shareholder_hits_non_thai(shareholders_data_list_check_th_pep, maximum_shareholders_list, result, save_result)
        # Return result
        return result, save_result

    def data_point_option_result_for_australia(
        self, 
        company_information: dict, 
        pct_share_data_point_ca_hits_list: list, 
        pct_share_data_point_th_pep_hits_list: list
    ) -> tuple[dict, dict[list]] :
        # Initial Comply Adventage api result and data point options result
        save_result = {
            'comply_advantage': [],
            'local_thai_pep_list': []
        }
        result = {
            'is_company_with_hits': False,
            'is_any_directors_with_hits': False,
            'is_any_directors_th_pep_hits': False,
            'is_any_25pct_shareholders_with_hits': False,
            'is_any_15pct_shareholders_with_hits': False,
            'is_any_10pct_shareholders_with_hits': False,
            'is_any_25pct_shareholders_th_pep_hits': False,
            'is_any_15pct_shareholders_th_pep_hits': False,
            'is_any_10pct_shareholders_th_pep_hits': False,
        }
        
        # ? Company Validate
        # [Company] Validate with Comply Advantage When 'is_company_with_hits' is selected from Decision flow.
        if self.is_in_option("is_company_with_hits"):
            company_path = "CompanyInformation.data.0.name.companyName"
            company_name = get(company_information, company_path)
            self.call_comply_advantage_company_name_not_thai(company_name, result, save_result)
        
        # ? Director Validate
        check_director_with_hits = self.is_in_option("is_any_directors_with_hits")
        check_director_with_th_pep_this = self.is_in_option("is_any_directors_th_pep_hits")
        # [Director] Validate Director when any '...director...' is selected from Decision flow.
        if check_director_with_hits or check_director_with_th_pep_this:
            # [Director] Filter with director role only
            major_person_data: list = get(
                company_information,
                'majorPerson.data',
                default=[],
            )
            major_person_name_list = [
                get(entity, 'enName') for entity in major_person_data 
                if str(get(entity, 'role')).lower() == 'director'
            ]
            # [Director] Validate each of Director
            for name in major_person_name_list:
                # [Director] Validate with Comply Advantage When 'is_any_directors_with_hits' is selected from Decision flow.
                if check_director_with_hits:
                    self.call_comply_advantage_director_not_thai(name, result, save_result)
                # [Director] Validate with Local Thai PEP When 'is_any_directors_th_pep_hits' is selected from Decision flow.
                if check_director_with_th_pep_this:
                    self.call_th_pep_director_not_thai(name, result, save_result)
        
        # ? Shareholders Validate
        shareholders_data_list: list = get(
            company_information, 
            'Shareholders.data.individual',
            [],
        )
        total_shares = 0
        shareholders_data_list_check_ca = []
        shareholders_data_list_check_th_pep = []
        maximum_shareholders_list = []
        maximum_shareholders_th_pep_list = []

        shareholders_name_25pct_list = []
        shareholders_name_15pct_list = []
        shareholders_name_10pct_list = []
        shareholders_name_25pct_list_th_pep = []
        shareholders_name_15pct_list_th_pep = []
        shareholders_name_10pct_list_th_pep = []

        # [Shareholders] Calculate total_share by sum of numberOfShares in each of shareholder
        for shareholders_data in shareholders_data_list:
            total_shares += self.__convert_money_to_float(get(shareholders_data, 'numberOfShares', 0))
        
        # [Shareholders] Validate each of shareholder
        for shareholders_data in shareholders_data_list:
            if total_shares == 0:
                continue

            # [Shareholders] Separate shareholder of Australia 
            shareholder_name = get(shareholders_data, 'shareholderNameEN', '')
            share_percentage = float(get(shareholders_data, 'percentageOwned', '0.0'))
            if 'is_any_25pct_shareholders_with_hits' in pct_share_data_point_ca_hits_list and share_percentage >= 25:
                shareholders_name_25pct_list.append(shareholder_name)
            if 'is_any_15pct_shareholders_with_hits' in pct_share_data_point_ca_hits_list and share_percentage >= 15:
                shareholders_name_15pct_list.append(shareholder_name)
            if 'is_any_10pct_shareholders_with_hits' in pct_share_data_point_ca_hits_list and share_percentage >= 10:
                shareholders_name_10pct_list.append(shareholder_name)
            if 'is_any_25pct_shareholders_th_pep_hits' in pct_share_data_point_th_pep_hits_list and share_percentage >= 25:
                shareholders_name_25pct_list_th_pep.append(shareholder_name)
            if 'is_any_15pct_shareholders_th_pep_hits' in pct_share_data_point_th_pep_hits_list and share_percentage >= 15:
                shareholders_name_15pct_list_th_pep.append(shareholder_name)
            if 'is_any_10pct_shareholders_th_pep_hits' in pct_share_data_point_th_pep_hits_list and share_percentage >= 10:
                shareholders_name_10pct_list_th_pep.append(shareholder_name)

        # [Shareholders] Build object for validate 25pct shareholder with CA 
        if len(shareholders_name_25pct_list) > 0:
            any_pct_data_point_shaheholders_hit = 'is_any_25pct_shareholders_with_hits'
            shareholders_name_list = shareholders_name_25pct_list
            shareholders_data_list_check_ca.append({
                'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                'shareholders_name_list': shareholders_name_list
            })
            if len(shareholders_name_list) > len(maximum_shareholders_list):
                maximum_shareholders_list = shareholders_name_list

        # [Shareholders] Build object for validate 25pct shareholder with PEP
        if len(shareholders_name_25pct_list_th_pep) > 0:
            any_pct_data_point_shaheholders_hit = 'is_any_25pct_shareholders_th_pep_hits'
            shareholders_name_list = shareholders_name_25pct_list_th_pep
            shareholders_data_list_check_ca.append({
                'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                'shareholders_name_list': shareholders_name_list
            })
            if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                maximum_shareholders_th_pep_list = shareholders_name_list
        
        # [Shareholders] Build object for validate 15pct shareholder with CA
        if len(shareholders_name_15pct_list) > 0:
            any_pct_data_point_shaheholders_hit = 'is_any_15pct_shareholders_with_hits'
            shareholders_name_list = shareholders_name_15pct_list
            shareholders_data_list_check_ca.append({
                'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                'shareholders_name_list': shareholders_name_list
            })
            if len(shareholders_name_list) > len(maximum_shareholders_list):
                maximum_shareholders_list = shareholders_name_list

        # [Shareholders] Build object for validate 15pct shareholder with PEP
        if len(shareholders_name_15pct_list_th_pep) > 0:
            any_pct_data_point_shaheholders_hit = 'is_any_15pct_shareholders_th_pep_hits'
            shareholders_name_list = shareholders_name_15pct_list_th_pep
            shareholders_data_list_check_ca.append({
                'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                'shareholders_name_list': shareholders_name_list
            })
            if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                maximum_shareholders_th_pep_list = shareholders_name_list

        # [Shareholders] Build object for validate 10pct shareholder with CA
        if len(shareholders_name_10pct_list) > 0:
            any_pct_data_point_shaheholders_hit = 'is_any_10pct_shareholders_with_hits'
            shareholders_name_list = shareholders_name_10pct_list
            shareholders_data_list_check_ca.append({
                'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                'shareholders_name_list': shareholders_name_list
            })
            if len(shareholders_name_list) > len(maximum_shareholders_list):
                maximum_shareholders_list = shareholders_name_list

        # [Shareholders] Build object for validate 10pct shareholder with PEP
        if len(shareholders_name_10pct_list_th_pep) > 0:
            any_pct_data_point_shaheholders_hit = 'is_any_10pct_shareholders_th_pep_hits'
            shareholders_name_list = shareholders_name_10pct_list_th_pep
            shareholders_data_list_check_ca.append({
                'any_pct_data_point_shaheholders_hit': any_pct_data_point_shaheholders_hit,
                'shareholders_name_list': shareholders_name_list
            })
            if len(shareholders_name_list) > len(maximum_shareholders_th_pep_list):
                maximum_shareholders_th_pep_list = shareholders_name_list

        # [Shareholders] Validate with Comply adventage when any '...shareholder_hit' is selected in decision flow
        if shareholders_data_list_check_ca:
                self.call_comply_advantage_check_shareholder_hits_non_thai(shareholders_data_list_check_ca, maximum_shareholders_list, result, save_result)
        # [Shareholders] Validate with Comply adventage when any '...shareholders_th_pep_hits' is selected in decision flow
        if shareholders_data_list_check_th_pep:
            self.call_th_pep_check_shareholder_hits_non_thai(shareholders_data_list_check_th_pep, maximum_shareholders_list, result, save_result)
        
        # Return date_point_result and result from API Response
        return result, save_result

    def __convert_money_to_float(self, money: str | None) -> float | None:
        if money:
            # Remove HK currency from money then cast to float
            return float(money.replace(",", ""))
        return None

    def is_comply_adventage_person_avaliable(self) -> bool:
        """Checks whether ***comply_adventage*** is enabled in the configuration.

        This function verifies if comply_adventage has been configured to be available.
        It reads the configuration and returns a boolean indicating the status.

        Returns:
            bool: False if ***comply_adventage*** is not enabled in Form_setting , True otherwise.
        """
        dynamicform = self.context.get("dynamicform", None)

        is_ca_enable = True
        if dynamicform:
            is_ca_enable = dynamicform.get_form_settings(
                "data_point_service.comply_advantage.person.enable", default=True
            )
        return is_ca_enable

    def is_dap_service_avaliable(self) -> bool:
        """Checks whether ***DAP*** is enabled in the configuration.

        This function verifies if comply_adventage has been configured to be available.
        It reads the configuration and returns a boolean indicating the status.

        Returns:
            bool: True if ***DAP*** is enabled, False otherwise.
        """
        dynamicform = self.context.get("dynamicform", None)

        is_dap_enable = False
        if dynamicform:
            is_dap_enable = dynamicform.get_form_settings(
                "data_point_service.dap.enable", default=False
            )
        return is_dap_enable

    def get_dap_host(self) -> tuple[str, str, str]:
        """Retrieves the DAP host from the configuration or get default dap.

        This function checks if DAP has a host value set in the configuration.
        If a value is found, it returns the host as a string; otherwise, it returns Default DAP HOST.

        Returns:
            tuple[str, str, str]: The DAP host, username and password if configured, otherwise get from default value .
        """
        dynamicform = self.context.get("dynamicform", None)
        data_point_dap_host: str = DATA_POINT_DAP_HOST
        data_point_dap_username: str = DATA_POINT_DAP_USERNAME
        data_point_dap_password: str = DATA_POINT_DAP_PASSWORD
        if dynamicform:
            data_point_dap_host = dynamicform.get_form_settings(
                "data_point_service.dap.host", default=DATA_POINT_DAP_HOST
            )
            data_point_dap_username = dynamicform.get_form_settings(
                "data_point_service.dap.username", default=DATA_POINT_DAP_USERNAME
            )
            data_point_dap_password = dynamicform.get_form_settings(
                "data_point_service.dap.password",
                default=DATA_POINT_DAP_PASSWORD,
                secure=False,
            )

        return data_point_dap_host, data_point_dap_username, data_point_dap_password

    def get_data_point_for_director(
        self,
        director_name: str,
        current_data_point_result: dict,
        current_save_result: dict,
        data_point_select_list: list[str],
    ) -> tuple[dict, dict]:

        def validate_hits_with_ca():
            ca_request_body = {
                "search_term": director_name,
                **DATA_POINT_COMPLY_ADVANTAGE_PERSON_FILTER,
            }
            ca_result = self.get_comply_advantage_result(ca_request_body)
            current_save_result["comply_advantage"].append(
                {
                    "input": ca_request_body,
                    "output": ca_result,
                }
            )
            ca_total_hit = get(ca_result, "content.data.total_hits")
            is_ca_hit = False
            if ca_total_hit > 0:
                is_ca_hit = True
            return is_ca_hit

        def validate_hits_with_amlo() -> dict:
            """
            Returns:
               dict: {  "is_amlo_hits": False,
                "is_freeze_05": False,
                "is_hr_02": False,
                "is_hr_08_risk": False,
                "is_un_sanction": False }
            """
            data_point_result = {
                "is_amlo_hits": False,
                "is_freeze_05": False,
                "is_hr_02": False,
                "is_hr_08_risk": False,
                "is_un_sanction": False,
            }
            amlo_request_body = {"fullname": director_name}
            # Get person screening AMLO (amlo_person_screening)
            amlo_result = self.get_amlo_person_screening_result(amlo_request_body)

            # Prepare error result and Return error result
            # if got error response in 200 status
            if type(amlo_result) is dict and "errors" in amlo_result.keys():
                self.set_current_data_point_output(current_save_result)
                error_result = {
                    "error": "national_id is invalid",
                    "exception_class": DataPointUnprocessableEntity,
                }
                return error_result

            # Get person screening AMLO (amlo_person_screening_un_list)
            amlo_un_result = self.get_amlo_person_screening_un_list_result(
                amlo_request_body
            )
            current_save_result["amlo"].append(
                {
                    "amlo_person_screening": {
                        "input": amlo_request_body,
                        "output": amlo_result,
                    },
                    "amlo_person_screening_un_list": {
                        "input": amlo_request_body,
                        "output": amlo_un_result,
                    },
                    "additional_infomation": {"fullname": director_name},
                }
            )
            # Logic flagging must be (is_freeze_05, is_hr_02, is_hr_08_risk) for list_name
            # Update directly from AMLO API
            if amlo_un_result.get("total_record", 0) > 0:
                data_point_result["is_un_sanction"] = True
                data_point_result["is_amlo_hits"] = True

            for i in amlo_result:
                if isinstance(i, dict):
                    amlo_reason_key = str(i.get("list")).upper()
                    return_flag = str(i.get("return_flag"))
                    if return_flag == "Y":
                        data_point_result["is_amlo_hits"] = True
                        if amlo_reason_key == "FREEZE-05":
                            data_point_result["is_freeze_05"] = True
                        if amlo_reason_key == "HR-02":
                            data_point_result["is_hr_02"] = True
                        if amlo_reason_key == "HR-08-RISK":
                            data_point_result["is_hr_08_risk"] = True
                        if amlo_reason_key == "FREEZE-04":
                            data_point_result["is_un_sanction"] = True
            return data_point_result

        def validate_hits_with_led() -> tuple[bool, bool]:
            led_request_body = {"fullname": director_name}

            # Get LED result
            led_result = self.get_led_result(led_request_body)
            additional_infomation = get(led_result, "additional_infomation")
            current_save_result["led"].append(
                {
                    "input": led_request_body,
                    "output": led_result,
                    "additional_infomation": additional_infomation,
                }
            )

            # Validate that is not get invalid error response
            if "errors" in led_result.keys():
                error_message = ErrorMessage.RESPONSE_UNPROCESSABLE.value.format(
                    **{"service": "LED"}
                )
                error_result = self.invalid_input_error_result(
                    error_message, DataPointUnprocessableEntity
                )
                return error_result

            is_led_hits = False
            is_led_corrupt_hits = False

            # Validate all potential bankrupt
            # by bankrupt_status_set must 001 only and not 000 status
            status = str(led_result.get("status", None))
            if status == "001":
                is_led_hits = True
            # Validate are_any_bankrupt_due_to_corruption
            if get(led_result, "data.corrupt"):
                is_led_corrupt_hits = True
            return is_led_hits, is_led_corrupt_hits

        def validate_hits_with_local_thai_pep() -> bool:
            director_name_without_prefix = self.remove_prefix(director_name)
            local_thai_pep_request_body = {"full_name": director_name_without_prefix}
            local_thai_pep_list_response = self.search_thai_pep_list(
                local_thai_pep_request_body
            )
            current_save_result["local_thai_pep_list"].append(
                {
                    "input": local_thai_pep_request_body,
                    "output": local_thai_pep_list_response,
                    "additional_infomation": {"fullname": director_name},
                }
            )
            is_pep_hit = False
            local_thai_pep_info: dict = local_thai_pep_list_response.get("data")
            if local_thai_pep_info:
                is_pep_hit = True
            return is_pep_hit

        # Director's Data point require service
        data_points_all_required = ["is_any_directors_with_hits"]
        data_points_pep_required = ["is_any_directors_th_pep_hits"]
        # AMLO - DIRECTOR REASON DATAPOINT
        data_points_amlo_required = [
            "is_any_directors_th_amlo_hits",
            "is_any_directors_th_amlo_hits_freeze_05",
            "is_any_directors_th_amlo_hits_hr_02",
            "is_any_directors_th_amlo_hits_hr_08_risk",
            "is_any_directors_th_amlo_hits_un_sanction",
        ]
        data_points_led_required = [
            "is_any_directors_th_led_bankrupt",
            "is_any_directors_th_led_bankrupt_from_corruption",
        ]

        # Get CA and DAP configure avaliable
        is_ca_avaliable = self.is_comply_adventage_person_avaliable()
        is_dap_avaliable = self.is_dap_service_avaliable()

        # Get current data point result
        is_any_hit = current_data_point_result["is_any_directors_with_hits"]
        is_pep_hit = current_data_point_result["is_any_directors_th_pep_hits"]
        is_amlo_hit = current_data_point_result["is_any_directors_th_amlo_hits"]
        is_led_hit = current_data_point_result["is_any_directors_th_led_bankrupt"]
        is_led_corruption_hit = current_data_point_result[
            "is_any_directors_th_led_bankrupt_from_corruption"
        ]
        # Get current data point result (AMLO Reason)
        is_amlo_freeze_05_hit = current_data_point_result['is_any_directors_th_amlo_hits_freeze_05']
        is_amlo_hr_02_hit = current_data_point_result['is_any_directors_th_amlo_hits_hr_02']
        is_amlo_hr_08_hit = current_data_point_result['is_any_directors_th_amlo_hits_hr_08_risk']
        is_amlo_un_sanction_hit = current_data_point_result['is_any_directors_th_amlo_hits_un_sanction']
        

        # asia verify use only ca part
        # Request API all if select data_point that in 'data_points_all_required'
        if any(
            data_point
            for data_point in data_point_select_list
            if data_point in data_points_all_required
        ):
            # CA Part
            if is_ca_avaliable:
                is_any_hit = is_any_hit or validate_hits_with_ca()

            # AMLO, LED Part
            if is_dap_avaliable:
                amlo_data_point_result = validate_hits_with_amlo()
                is_amlo_hit |= amlo_data_point_result["is_amlo_hits"]
                is_amlo_freeze_05_hit |= amlo_data_point_result["is_freeze_05"]
                is_amlo_hr_02_hit |= amlo_data_point_result["is_hr_02"]
                is_amlo_hr_08_hit |= amlo_data_point_result["is_hr_08_risk"]
                is_amlo_un_sanction_hit |= amlo_data_point_result["is_un_sanction"]
                is_any_hit |= is_amlo_hit

                led_hit, coruption_hit = validate_hits_with_led()
                is_led_hit = is_led_hit or led_hit
                is_led_corruption_hit = is_led_corruption_hit or coruption_hit
                is_any_hit |= is_led_hit

            # Local Thai PEP Part
            is_pep_hit = is_pep_hit or validate_hits_with_local_thai_pep()
            is_any_hit = is_any_hit or is_pep_hit

        # If "any_hit" is not selected, request each selected item in {service}_hits data point
        # that is included in the decision flow.
        else:
            # AMLO data point
            if self.is_in_option("directors_th_amlo"):
                amlo_data_point_result = validate_hits_with_amlo()
                is_amlo_hit |= amlo_data_point_result["is_amlo_hits"]
                is_amlo_freeze_05_hit |= amlo_data_point_result["is_freeze_05"]
                is_amlo_hr_02_hit |= amlo_data_point_result["is_hr_02"]
                is_amlo_hr_08_hit |= amlo_data_point_result["is_hr_08_risk"]
                is_amlo_un_sanction_hit |= amlo_data_point_result["is_un_sanction"]
                is_any_hit |= is_amlo_hit
            # LED data point
            if self.is_in_option("directors_th_led"):
                led_hit, coruption_hit = validate_hits_with_led()
                is_led_hit = is_led_hit or led_hit
                is_led_corruption_hit = is_led_corruption_hit or coruption_hit
            # Local Thai PEP data point
            if self.is_in_option("directors_th_pep"):
                is_pep_hit = is_pep_hit or validate_hits_with_local_thai_pep()

        # Update data point result
        current_data_point_result["is_any_directors_with_hits"] = is_any_hit
        current_data_point_result["is_any_directors_th_pep_hits"] = is_pep_hit
        current_data_point_result["is_any_directors_th_amlo_hits"] = is_amlo_hit
        current_data_point_result["is_any_directors_th_led_bankrupt"] = is_led_hit
        current_data_point_result[
            "is_any_directors_th_led_bankrupt_from_corruption"
        ] = is_led_corruption_hit
        # Update current data point result (AMLO Reason)
        current_data_point_result["is_any_directors_th_amlo_hits_freeze_05"] = (
            is_amlo_freeze_05_hit
        )
        current_data_point_result["is_any_directors_th_amlo_hits_hr_02"] = (
            is_amlo_hr_02_hit
        )
        current_data_point_result["is_any_directors_th_amlo_hits_hr_08_risk"] = (
            is_amlo_hr_08_hit
        )
        current_data_point_result["is_any_directors_th_amlo_hits_un_sanction"] = (
            is_amlo_un_sanction_hit
        )

        return current_save_result, current_data_point_result

    def get_data_point_for_shareholder(
        self,
        shareholder_name: str,
        shareholder_job: str,
        percent_share: str,
        current_data_point_result: dict,
        current_save_result: dict,
        data_point_select_list: list[str],
    ) -> tuple[dict, dict]:

        def validate_hits_with_ca() -> bool:
            ca_request_body = {
                "search_term": shareholder_name,
                **DATA_POINT_COMPLY_ADVANTAGE_PERSON_FILTER,
            }
            ca_result = self.get_comply_advantage_result(ca_request_body)
            current_save_result["comply_advantage"].append(
                {
                    "input": ca_request_body,
                    "output": ca_result,
                }
            )
            ca_total_hit = get(ca_result, "content.data.total_hits")
            is_ca_hit = False
            if ca_total_hit > 0:
                is_ca_hit = True
            return is_ca_hit

        def validate_hits_with_amlo() -> dict:
            """
            Returns:
               dict: {  "is_amlo_hits": False,
                "is_freeze_05": False,
                "is_hr_02": False,
                "is_hr_08_risk": False,
                "is_un_sanction": False }
            """
            data_point_result = {
                "is_amlo_hits": False,
                "is_freeze_05": False,
                "is_hr_02": False,
                "is_hr_08_risk": False,
                "is_un_sanction": False,
            }
            amlo_request_body = {"fullname": shareholder_name}
            # Get person screening AMLO (amlo_person_screening)
            amlo_result = self.get_amlo_person_screening_result(amlo_request_body)

            # Prepare error result and Return error result
            # if got error response in 200 status
            if type(amlo_result) is dict and "errors" in amlo_result.keys():
                self.set_current_data_point_output(current_save_result)
                error_result = {
                    "error": "national_id is invalid",
                    "exception_class": DataPointUnprocessableEntity,
                }
                return error_result

            # Get person screening AMLO (amlo_person_screening_un_list)
            amlo_un_result = self.get_amlo_person_screening_un_list_result(
                amlo_request_body
            )
            current_save_result["amlo"].append(
                {
                    "amlo_person_screening": {
                        "input": amlo_request_body,
                        "output": amlo_result,
                    },
                    "amlo_person_screening_un_list": {
                        "input": amlo_request_body,
                        "output": amlo_un_result,
                    },
                    "additional_infomation": {"fullname": shareholder_name},
                }
            )

            if amlo_un_result.get("total_record", 0) > 0:
                data_point_result["is_un_sanction"] = True
                data_point_result["is_amlo_hits"] = True

            for i in amlo_result:
                if isinstance(i, dict):
                    amlo_reason_key = str(i.get("list")).upper()
                    return_flag = str(i.get("return_flag"))
                    if return_flag == "Y":
                        data_point_result["is_amlo_hits"] = True
                        if amlo_reason_key == "FREEZE-05":
                            data_point_result["is_freeze_05"] = True
                        if amlo_reason_key == "HR-02":
                            data_point_result["is_hr_02"] = True
                        if amlo_reason_key == "HR-08-RISK":
                            data_point_result["is_hr_08_risk"] = True
                        if amlo_reason_key == "FREEZE-04":
                            data_point_result["is_un_sanction"] = True
            return data_point_result

        def validate_hits_with_led() -> tuple[bool, bool]:
            led_request_body = {
                "fullname": shareholder_name,
            }

            # Get LED result
            led_result = self.get_led_result(led_request_body)
            additional_infomation = get(led_result, "additional_infomation")
            current_save_result["led"].append(
                {
                    "input": led_request_body,
                    "output": led_result,
                    "additional_infomation": additional_infomation,
                }
            )

            # Validate that is not get invalid error response
            if "errors" in led_result.keys():
                error_message = ErrorMessage.RESPONSE_UNPROCESSABLE.value.format(
                    **{"service": "LED"}
                )
                error_result = self.invalid_input_error_result(
                    error_message, DataPointUnprocessableEntity
                )
                return error_result

            is_led_hits = False
            is_led_corrupt_hits = False

            # Validate all potential bankrupt
            # by bankrupt_status_set must 001 only and not 000 status
            status = str(led_result.get("status", None))
            if status == "001":
                is_led_hits = True
            # Validate are_any_bankrupt_due_to_corruption
            if get(led_result, "data.corrupt"):
                is_led_corrupt_hits = True
            return is_led_hits, is_led_corrupt_hits

        def validate_hits_with_local_thai_pep() -> bool:
            # No need to remove prefix due to name from creden alreadys remove
            local_thai_pep_request_body = {"full_name": shareholder_name}
            local_thai_pep_list_response = self.search_thai_pep_list(
                local_thai_pep_request_body
            )
            current_save_result["local_thai_pep_list"].append(
                {
                    "input": local_thai_pep_request_body,
                    "output": local_thai_pep_list_response,
                    "additional_infomation": {"fullname": shareholder_name},
                }
            )
            is_pep_hit = False
            local_thai_pep_info: dict = local_thai_pep_list_response.get("data")
            if local_thai_pep_info:
                is_pep_hit = True
            return is_pep_hit

        # Sharehold's Data point require service
        data_points_all_required = [f"is_any_{percent_share}_shareholders_with_hits"]
        data_points_pep_required = [f"is_any_{percent_share}_shareholders_th_pep_hits"]
        data_points_amlo_required = [
            f"is_any_{percent_share}_shareholders_th_amlo_hits"
        ]
        data_points_led_required = [
            f"is_any_{percent_share}_shareholders_th_led_bankrupt'",
            f"is_any_{percent_share}_shareholders_th_led_bankrupt_from_corruption",
        ]

        # Get CA and DAP configure avaliable
        is_ca_avaliable = self.is_comply_adventage_person_avaliable()
        is_dap_avaliable = self.is_dap_service_avaliable()

        # Exclude Company shareholder without ca check due to dap is for person only
        if not is_ca_avaliable and shareholder_job != "ประกอบธุรกิจ":
            return current_save_result, current_data_point_result

        # Get current data point result
        is_any_hit = current_data_point_result.get(
            f"is_any_{percent_share}_shareholders_with_hits"
        )
        is_pep_hit = current_data_point_result.get(
            f"is_any_{percent_share}_shareholders_th_pep_hits"
        )
        is_amlo_hit = current_data_point_result.get(
            f"is_any_{percent_share}_shareholders_th_amlo_hits"
        )
        is_led_hit = current_data_point_result.get(
            f"is_any_{percent_share}_shareholders_th_led_bankrupt",
        )
        is_led_corruption_hit = current_data_point_result.get(
            f"is_any_{percent_share}_shareholders_th_led_bankrupt_from_corruption"
        )
        # Get current data point result (AMLO Reason)
        is_amlo_freeze_05_hit = current_data_point_result.get(
            f"is_any_{percent_share}_shareholders_th_amlo_hits_freeze_05"
        )
        is_amlo_hr_02_hit = current_data_point_result.get(
            f"is_any_{percent_share}_shareholders_th_amlo_hits_hr_02"
        )
        is_amlo_hr_08_hit = current_data_point_result.get(
            f"is_any_{percent_share}_shareholders_th_amlo_hits_hr_08_risk"
        )
        is_amlo_un_sanction_hit = current_data_point_result.get(
            f"is_any_{percent_share}_shareholders_th_amlo_hits_un_sanction"
        )
        amlo_data_point_result = {}
        # Request API all if select data_point that in 'data_points_all_required'
        if any(
            data_point
            for data_point in data_point_select_list
            if data_point in data_points_all_required
        ):
            # CA Part

            #check check check
            if is_ca_avaliable:
                is_any_hit = is_any_hit or validate_hits_with_ca()

            # AMLO, LED Part
            if is_dap_avaliable:

                amlo_data_point_result = validate_hits_with_amlo()
                is_amlo_hit |= amlo_data_point_result["is_amlo_hits"]
                is_amlo_freeze_05_hit |= amlo_data_point_result["is_freeze_05"]
                is_amlo_hr_02_hit |= amlo_data_point_result["is_hr_02"]
                is_amlo_hr_08_hit |= amlo_data_point_result["is_hr_08_risk"]
                is_amlo_un_sanction_hit |= amlo_data_point_result["is_un_sanction"]
                is_any_hit |= is_amlo_hit

                led_hit, coruption_hit = validate_hits_with_led()
                is_led_hit = is_led_hit or led_hit
                is_led_corruption_hit = is_led_corruption_hit or coruption_hit
                is_any_hit |= is_led_hit

            # Local Thai PEP Part
            is_pep_hit = is_pep_hit or validate_hits_with_local_thai_pep()
            is_any_hit = is_any_hit or is_pep_hit

        # If "any_hit" is not selected, request each selected item in {service}_hits data point
        # that is included in the decision flow.
        else:
            # AMLO data point
            if self.is_in_option("shareholders_th_amlo"):
                amlo_data_point_result = validate_hits_with_amlo()
                is_amlo_hit = is_amlo_hit or amlo_data_point_result.get("is_amlo_hits")
            # LED data point
            if self.is_in_option("shareholders_th_led"):
                led_hit, coruption_hit = validate_hits_with_led()
                is_led_hit = is_led_hit or led_hit
                is_led_corruption_hit = is_led_corruption_hit or coruption_hit
            # Local Thai PEP data point
            if self.is_in_option("shareholders_th_pep"):
                is_pep_hit = is_pep_hit or validate_hits_with_local_thai_pep()

        # Update data point result
        current_data_point_result[f"is_any_{percent_share}_shareholders_with_hits"] = (
            is_any_hit
        )
        current_data_point_result[
            f"is_any_{percent_share}_shareholders_th_pep_hits"
        ] = is_pep_hit
        current_data_point_result[
            f"is_any_{percent_share}_shareholders_th_amlo_hits"
        ] = is_amlo_hit
        current_data_point_result[
            f"is_any_{percent_share}_shareholders_th_led_bankrupt"
        ] = is_led_hit
        current_data_point_result[
            f"is_any_{percent_share}_shareholders_th_led_bankrupt_from_corruption"
        ] = is_led_corruption_hit

        # Update current data point result (AMLO Reason)
        is_amlo_freeze_05_hit = current_data_point_result.get(
            f"is_any_{percent_share}_shareholders_th_amlo_hits_freeze_05"
        )
        is_amlo_hr_02_hit = current_data_point_result.get(
            f"is_any_{percent_share}_shareholders_th_amlo_hits_hr_02"
        )
        is_amlo_hr_08_hit = current_data_point_result.get(
            f"is_any_{percent_share}_shareholders_th_amlo_hits_hr_08_risk"
        )
        is_amlo_un_sanction_hit = current_data_point_result.get(
            f"is_any_{percent_share}_shareholders_th_amlo_hits_un_sanction"
        )

        current_data_point_result[
            f"is_any_{percent_share}_shareholders_th_amlo_hits_freeze_05"
        ] = is_amlo_freeze_05_hit or amlo_data_point_result.get("is_freeze_05")
        current_data_point_result[
            f"is_any_{percent_share}_shareholders_th_amlo_hits_hr_02"
        ] = is_amlo_hr_02_hit or amlo_data_point_result.get("is_hr_02")
        current_data_point_result[
            f"is_any_{percent_share}_shareholders_th_amlo_hits_hr_08_risk"
        ] = is_amlo_hr_08_hit or amlo_data_point_result.get("is_hr_08_risk")
        current_data_point_result[
            f"is_any_{percent_share}_shareholders_th_amlo_hits_un_sanction"
        ] = is_amlo_un_sanction_hit or amlo_data_point_result.get("is_un_sanction")

        return current_save_result, current_data_point_result

    def remove_prefix(self, full_name: str):
        """
        Removes prefixes from a full name.

        Args:
            full_name (str): The full name.

        Returns:
            str: The full name without prefixes.
        """
        prefixes = [
            "นาย",
            "นาง",
            "นางสาว",
            "ดร.",
            "ดอกเตอร์",
            "ศ.",
            "ศาสตราจารย์",
            "รศ.",
            "รองศาสตราจารย์",
            "ผศ.",
            "ผู้ช่วยศาสตราจารย์",
            "ด.ช.",
            "เด็กชาย",
            "ด.ญ.",
            "เด็กหญิง",
            "ม.จ.",
            "หม่อมเจ้า",
            "ม.ร.ว.",
            "หม่อมราชวงศ์",
            "ม.ล.",
            "หม่อมหลวง",
            "พล.อ.",
            "พลเอก",
            "พล.ท.",
            "พลโท",
            "พล.ต.",
            "พลตรี",
            "พลตำรวจตรี",
            "พ.อ.",
            "พันเอก",
            "พ.ท.",
            "พันโท",
            "พ.ต.",
            "พันตรี",
            "ร.อ.",
            "ร้อยเอก",
            "ร.ท.",
            "ร้อยโท",
            "ร.ต.",
            "ร้อยตรี",
            "จ.ส.อ.",
            "จ่าสิบเอก",
            "ส.อ.",
            "สิบเอก",
            "ส.ท.",
            "สิบโท",
            "ส.ต.",
            "สิบตรี",
            "น.อ.",
            "นาวาอากาศเอก",
            "น.ท.",
            "นาวาอากาศโท",
            "น.ต.",
            "นาวาอากาศตรี",
            "ร.น.",
            "เรือเอก",
            "ร.ท.",
            "เรือโท",
            "ร.ต.",
            "เรือตรี",
            "อำมาตย์เอก",
            "อำมาตย์โท",
            "อำมาตย์ตรี",
            "ว่าที่ ร.ต.",
            "ว่าที่ร้อยตรี",
            "ว่าที่ ร.ท.",
            "ว่าที่ร้อยโท",
            "ว่าที่ ร.อ.",
            "ว่าที่ร้อยเอก",
            "Mr.",
            "Mister",
            "Ms.",
            "Miss",
            "Madam",
            "Mrs.",
            "Mistress",
            "Dr.",
            "Doctor",
            "Prof.",
            "Professor",
            "Assoc. Prof.",
            "Associate Professor",
            "Gen.",
            "General",
            "Col.",
            "Colonel",
            "Maj.",
            "Major",
            "Capt.",
            "Captain",
            "Lt.",
            "Lieutenant",
        ]

        for prefix in prefixes:
            if full_name.startswith(prefix):
                return full_name[len(prefix) :].lstrip()
        return full_name

    def get_amlo_person_screening_result(self, data: dict) -> dict:
        data_point_function_name = inspect.currentframe().f_code.co_name

        dap_host, dap_username, dap_password = self.get_dap_host()
        url = f"{dap_host}/api/amlo/person-screening"
        response = self._request(
            method="POST",
            url=url,
            json=data,
            auth=(dap_username, dap_password),
            data_point_function_name=data_point_function_name,
        )

        response_data = response.get("result", {})
        status_code = response.get("status_code")
        logging_context = self.get_logging_context()
        data_point_log_info = {
            "url": url,
            "json": data,
            "context": logging_context,
            "status_code": status_code,
        }
        logger.info(f"AMLO response {status_code}", data_point_log_info)

        # how to get pipeline or pipeline_id
        # pipeline_id # url = f"{DECISION_FLOW_TRIGGER_HOST}/api/workspaces/{workspace_slug}/forms/{form_slug}/decision-pipeline/{target_id}/run/"
        if not status.is_success(status_code):
            raise DataPointResponseException(
                f"{self.title}: {ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'AMLO'})}: {status_code}",
                data_point_log_info=data_point_log_info,
            )

        return response_data

    def get_amlo_person_screening_un_list_result(self, data: dict) -> dict:
        data_point_function_name = inspect.currentframe().f_code.co_name

        dap_host, dap_username, dap_password = self.get_dap_host()
        url = f"{dap_host}/api/amlo/person-screening-un-list"
        response = self._request(
            method="POST",
            url=url,
            json=data,
            auth=(dap_username, dap_password),
            data_point_function_name=data_point_function_name,
        )

        response_data = response.get("result", {})
        status_code = response.get("status_code")
        logging_context = self.get_logging_context()
        data_point_log_info = {
            "url": url,
            "json": data,
            "context": logging_context,
            "status_code": status_code,
        }
        logger.info(f"AMLO UN LIST response {status_code}", data_point_log_info)

        # how to get pipeline or pipeline_id
        # pipeline_id # url = f"{DECISION_FLOW_TRIGGER_HOST}/api/workspaces/{workspace_slug}/forms/{form_slug}/decision-pipeline/{target_id}/run/"
        if not status.is_success(status_code):
            raise DataPointResponseException(
                f"{self.title}: {ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'AMLO'})}: {status_code}",
                data_point_log_info=data_point_log_info,
            )

        return response_data

    def search_thai_pep_list(self, local_thai_pep_input: dict):
        DATA_POINT_LOCAL_THAI_PEP_LIST_HOST = (
            settings.DATA_POINT_LOCAL_THAI_PEP_LIST_HOST
        )
        DATA_POINT_LOCAL_THAI_PEP_LIST_USERNAME = (
            settings.DATA_POINT_LOCAL_THAI_PEP_LIST_USERNAME
        )
        DATA_POINT_LOCAL_THAI_PEP_LIST_PASSWORD = (
            settings.DATA_POINT_LOCAL_THAI_PEP_LIST_PASSWORD
        )

        data_point_function_name = inspect.currentframe().f_code.co_name
        url = f"{DATA_POINT_LOCAL_THAI_PEP_LIST_HOST}/v1/thai_pep_list/search"

        result = self._request(
            method="GET",
            url=url,
            params=local_thai_pep_input,
            auth=(
                DATA_POINT_LOCAL_THAI_PEP_LIST_USERNAME,
                DATA_POINT_LOCAL_THAI_PEP_LIST_PASSWORD,
            ),
            data_point_function_name=data_point_function_name,
        )
        response_data = result.get("result")
        status_code = result.get("status_code")
        logging_context = self.get_logging_context()

        data_point_log_info = {
            "url": url,
            "json": local_thai_pep_input,
            "context": logging_context,
            "status_code": status_code,
        }
        logger.info(f"Local thai pep list response {status_code}", data_point_log_info)
        if not status.is_success(status_code):
            raise DataPointResponseException(
                f"{self.title}: {ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'Local Thai PEP List'})}: {status_code}",
                data_point_log_info=data_point_log_info,
            )
        return response_data

    def get_led_result(self, led_input: dict) -> dict:
        data_point_function_name = inspect.currentframe().f_code.co_name

        dap_host, dap_username, dap_password = self.get_dap_host()
        url = f"{dap_host}/api/led/get-bankrupt"
        result = self._request(
            method="POST",
            url=url,
            json=led_input,
            auth=(dap_username, dap_password),
            data_point_function_name=data_point_function_name,
        )
        response_data = result.get("result")
        status_code = result.get("status_code")
        logging_context = self.get_logging_context()
        data_point_log_info = {
            "url": url,
            "json": led_input,
            "context": logging_context,
            "status_code": status_code,
        }
        logger.info(f"LED response {status_code}", data_point_log_info)
        if not status.is_success(status_code):
            raise DataPointResponseException(
                f"{self.title}: {ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'LED'})}: {status_code}",
                data_point_log_info=data_point_log_info,
            )
        return response_data

    def restructure_creden_result(self, creden_result):
        if not creden_result.get("list_shareholder"):
            raise DataPointUnprocessableEntity(
                f"{self.title}: {ErrorMessage.RESPONSE_UNPROCESSABLE.value.format(**{'service': 'Creden'})}: No Shareholder found in this company.",
            )
        for list_shareholder in creden_result.get("list_shareholder", []):
            if "Job" in list_shareholder:
                list_shareholder["job"] = list_shareholder.pop("Job")
            list_shareholder_list_data_shareholder = []
            for list_data_shareholder in list_shareholder["list_data_shareholder"]:
                list_shareholder_list_data_shareholder.append(
                    {k.lower(): v for k, v in list_data_shareholder.items()}
                )
            list_shareholder["list_data_shareholder"] = (
                list_shareholder_list_data_shareholder
            )
        return creden_result
