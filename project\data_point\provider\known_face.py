import logging

from data_point.base import <PERSON><PERSON><PERSON><PERSON>B<PERSON>
from workspace.events import ALL_EKYC_LIVENESS_ITEM_TYPES
from django.conf import settings
from rest_framework import status

logger: logging.Logger = logging.getLogger(__name__)
DATA_POINT_KNOWN_FACE_HOST = settings.DATA_POINT_KNOWN_FACE_HOST
DATA_POINT_KNOWN_FACE_APP_ID = settings.DATA_POINT_KNOWN_FACE_APP_ID
DATA_POINT_KNOWN_FACE_APP_SECRET = settings.DATA_POINT_KNOWN_FACE_APP_SECRET

DATAPOINT_OPTIONS = {
    'liveness_attempts_count': {
        'label': 'How many time the user has attempted liveness in the past',
        'type': 'number',
    },
    'fail_before_count': {
        'label': 'How many time the user has verified liveness failed in the past',
        'type': 'number',
    },
}


class KnownFace(EKYCBase):
    name = 'known_face'
    title = 'Known Face'
    sub_title = ''
    description = 'Known Face feature, which allows you to store facial images and verify if a user has previously used the application.'
    icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/icon_64/KnownFace_64.png'
    option_icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/uppass_verification_services_36/KnowFace_36.png'
    data_point_opitons = DATAPOINT_OPTIONS
    required_item_types = [ALL_EKYC_LIVENESS_ITEM_TYPES]
    required_item_types_auto_connect = True
    
    def get_data_result(self, **kwargs):
        from known_face.known_face import KnownFaceApp

        selected_data_point_options = kwargs.get('values_list', [])
        form = self.form
        applied_form = self.applied_form
        ekyc = self.get_ekyc()
        known_face_app = KnownFaceApp(form, applied_form, ekyc)
        result = {}

        for i in selected_data_point_options:
            if i == 'liveness_attempts_count':
                attempt_result = known_face_app.search_attempt_face()
                all_attempt_result = attempt_result.get('result', {})
                result['liveness_attempts_count'] = all_attempt_result.get('total')
            else:
                fraud_result = known_face_app.search_fraud_face()
                all_fraud_result = fraud_result.get('result', {})
                result['fail_before_count'] = all_fraud_result.get('total')

        return result
