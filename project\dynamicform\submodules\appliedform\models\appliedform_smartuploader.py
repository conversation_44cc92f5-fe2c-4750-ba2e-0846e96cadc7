import mimetypes
from pydash import get
from django.db import models
from django.core.files.uploadedfile import UploadedFile
from dynamicform.submodules.model import AutoNow
from dynamicform.submodules.answerfile.models import AnswerFile
from dynamicform.submodules.answerfilepreview.models import AnswerFilePreview
from dynamicform.submodules.appliedform.util import convert_pdf_to_images, check_pdf_pages
from smartuploader.models.smartuploader import SmartUploader
from smartuploader.services.document_extraction_gateway import DocumentExtractionGateway
from smartuploader.helpers.get_form import get_form_smart_uploader_item
from smartuploader.types import ValidateExtrationResult, UploadExtractionResult, ResultExtractionResponse
from .appliedform import AppliedForm
from ...answer.models import Answer
from project.custom_logger import logger

ACTION_SMART_UPLOADER_UPLOAD = "smart_uploader_upload"


class AppliedFormSmartUploader(AutoNow):
    applied_form = models.ForeignKey(
        AppliedForm,
        on_delete=models.CASCADE,
    )
    answer = models.ForeignKey(Answer, on_delete=models.CASCADE, null=True)
    smart_uploader = models.ForeignKey(SmartUploader, on_delete=models.CASCADE, null=True)
    page_count = models.IntegerField(null=True, blank=True, help_text="Number of pages in the uploaded document")

    @property
    def answer_files(self) -> list[AnswerFile]:
        return self.answer.answerfile_set.order_by("created_at").all()

    def build_response(self, **kwargs):
        # Report
        response = {}

        # Report & Submitted
        if self.smart_uploader:
            uploader_info = self.smart_uploader.info(**kwargs)
            response.update(uploader_info)

        # Report & Not submit
        else:
            response.update(
                {
                    "status": "created",
                }
            )

        return response

    def info(self, report=False, **kwargs):
        file_infos = []
        for answer_file in self.answer_files:
            info = answer_file.info(report=report, **kwargs)

            # Form
            if not report:
                preview: AnswerFilePreview = answer_file.answerfilepreview_set.first()
                if preview:
                    info.update({"preview": preview.item_url, "type": preview.mime_type})
                if answer_file.password:
                    # use raw pdf type and preview url to prevent expost of the file
                    # if the file is password protected
                    info.update({
                        "need_password": True, 
                        "preview": answer_file.item_url, 
                        "type": answer_file.mime_type
                    })
                file_infos.append(info)

            # Report
            else:
                previews: list[AnswerFilePreview] = answer_file.answerfilepreview_set.order_by("name").all()
                if len(previews) > 0:
                    for preview in previews:
                        preview_info = {**info, **preview.info(report=report)}
                        file_infos.append(preview_info)
                else:
                    file_infos.append(info)

        # Answer
        if not report:
            return file_infos

        # Report
        response = self.build_response(**kwargs)
        response.update({"files": file_infos})

        return response

    def recalculate_page_count(self):
        self.page_count = sum([check_pdf_pages(answer_file.detail) for answer_file in self.answer_files])
        self.save(update_fields=["page_count"])

    def submit_info(self, **kwargs):
        file_infos = []
        for answer_file in self.answer_files:
            answer_file_info = answer_file.submit_info(**kwargs)
            file_infos.append(answer_file_info)

        # Submit Info
        response = self.build_response(**kwargs)
        response.update({"files": file_infos})

        return response

    def upload_file(self, file: UploadedFile, password: str = None):
        import memory_profiler

        initial_memory = memory_profiler.memory_usage()[0]
        logger.debug(
            f"Memory usage at start of upload_file: {initial_memory:.2f} MB",
        )

        # Initial setup and validation config
        extension = "." + file.name.split(".")[-1]
        mime_type = mimetypes.types_map.get(extension, None)
        item_schema = get_form_smart_uploader_item(self.applied_form.form, self.answer.question)
        match_type = item_schema.get("match_type", "any")
        match_keywords = item_schema.get("match_keywords", [])
        match_level = item_schema.get("match_level", 100)
        custom_fields_text_validation = {
            "type": match_type,
            "keyword_list": match_keywords,
            "match_level": match_level,
        }

        config_memory = memory_profiler.memory_usage()[0]
        logger.debug(
            f"Memory after config setup: {config_memory:.2f} MB (diff: {(config_memory - initial_memory):.2f} MB)"
        )

        # OCR Validation
        pass_validation = False
        if len(match_keywords) > 0:
            gateway = DocumentExtractionGateway()
            response = gateway.ocr_validation(file, custom_fields_text_validation)
            try:
                result: ValidateExtrationResult = response.json()
            except Exception:
                result = {}
            pass_validation = result.get("pass_validation", False)

            if not pass_validation:
                fail_reason = get(result, ["error", "message"]) or None
                self.applied_form.save_log(
                    action=ACTION_SMART_UPLOADER_UPLOAD,
                    detail={
                        "answer": self.answer.question if self.answer else None,
                        "pass_validation": pass_validation,
                        "fail_reason": fail_reason,
                        "status_code": response.status_code,
                        "result": result,
                    },
                    is_fail=True,
                )
                return {"pass_validation": pass_validation, "fail_reason": fail_reason}
        else:
            pass_validation = True

        validation_memory = memory_profiler.memory_usage()[0]
        logger.debug(
            f"Memory after OCR validation: {validation_memory:.2f} MB (diff: {(validation_memory - config_memory):.2f} MB)"
        )

        # Create AnswerFile
        answer_file = AnswerFile.objects.create(
            answer=self.answer, 
            name=file.name, 
            mime_type=mime_type, 
            detail=file, 
            password=password
        )
        answer_file_info = answer_file.info()

        file_creation_memory = memory_profiler.memory_usage()[0]
        logger.debug(
            f"Memory after answer file creation: {file_creation_memory:.2f} MB (diff: {(file_creation_memory - validation_memory):.2f} MB)"
        )

        # Create previews
        try:
            image_files = convert_pdf_to_images(file, password=password)

            conversion_memory = memory_profiler.memory_usage()[0]
            logger.debug(
                f"Memory after PDF conversion: {conversion_memory:.2f} MB (diff: {(conversion_memory - file_creation_memory):.2f} MB)"
            )

            if len(image_files) > 0:
                answer_file_previews = AnswerFilePreview.objects.bulk_create(
                    AnswerFilePreview(answerfile=answer_file, name=image_file.name, detail=image_file)
                    for image_file in image_files
                )

                answer_file_preview_info = answer_file_previews[0].info()
                answer_file_info.update(
                    {"preview": answer_file_preview_info.get("preview"), "type": answer_file_preview_info.get("type")}
                )

            preview_memory = memory_profiler.memory_usage()[0]
            logger.debug(
                f"Memory after preview creation: {preview_memory:.2f} MB (diff: {(preview_memory - conversion_memory):.2f} MB)"
            )
            logger.debug(f"Total memory increase: {(preview_memory - initial_memory):.2f} MB")
        except Exception as e:
            logger.debug(f"Error creating previews: {e}")

        return {
            **answer_file_info,
            "pass_validation": pass_validation,
        }

    def get_question_result(self):
        # smart_uploader.info() := get raw_result, update_validation, and get status
        if not self.smart_uploader:
            # return status, detail, data_result
            return None, None, {}

        result = self.smart_uploader.info()
        status = get(result, "status")
        validation = get(result, "data.validation", {})
        data_result = {
            "result": {
                "fields": get(result, "data.result.fields", {}),
                "extracted_at": get(result, "data.result.extracted_at", None),
                "custom_fields": get(result, "data.result.custom_fields", {}),
                "page_count": self.page_count,
            },
            "message": get(result, "data.message", None),
            "validation": validation,
        }
        detail = "" if get(validation, "passed", True) else "validation_fail"
        return status, detail, data_result
