from django.test import override_settings
import pytest
from types import SimpleNamespace
from rest_framework import status
from rest_framework.test import APIRequestFactory, force_authenticate
from unittest import mock
from dynamicform.tests.case import TestDatabaseTestCase
from dynamicform.models import AppliedForm, Form, Answer, FormSetting, PartitionAnswer
from workspace.models import Workspace
from dynamicform.submodules.appliedform.views import AppliedFormViewSet
from workspace.views.application import ApplicationViewSet
from workspace.models import Workspace, Form as WorkspaceForm, Member as WorkspaceMember, Status as MemberStatus, Role
from django.contrib.auth import get_user_model
from data_point.provider import Answer as DataPointAnswer

User = get_user_model()

@mock.patch(
    "dynamicform.submodules.appliedform.functions.AppliedFormFunction.can_update_info", mock.Mock(return_value=True)
)
@mock.patch("dynamicform.submodules.appliedform.dynamicform.Dynamicform.log", mock.Mock())
@mock.patch("dynamicform.submodules.appliedform.views.appliedform.CrequestMiddleware", mock.Mock())
@override_settings(ENABLE_CREDIT_SYSTEM=False)
class SaveTest(TestDatabaseTestCase):
    def setUp(self):
        super(SaveTest, self).setUp()
        form = Form(
            name="test",
            slug="slug-test-a",
            frontend_schema={
                "steps": {
                    "step1": {
                        "sections": {
                            "s1": {
                                "items": {
                                    "item_1": {"name": "item1", "type": "inputText", "validator_rule": "required"},
                                    "item_2": {
                                        "name": "item2",
                                        "type": "inputText",
                                    },
                                    "item_3": {"name": "item3", "type": "inputText", "validator_rule": "min:3"},
                                    "item_4": {
                                        "name": "mockupstepapp_itemb",
                                        "type": "dynamicform.tests.mockupstepapp.ItemB",
                                    },
                                    "item_5": {
                                        "name": "mockupstepapp_itemb",
                                        "type": "inputText",
                                        "props": {"disable": True},
                                    },
                                    "item_6": {"name": "item6", "type": "inputNumber"},
                                    "email": {"name": "email", "type": "inputText"},
                                }
                            }
                        }
                    },
                    "step2": {
                        "sections": {
                            "s2": {
                                "items": {
                                    "item_7": {"name": "item7", "type": "inputText", "validator_rule": "required"},
                                    "item_8": {"name": "item8", "type": "inputText", "validator_rule": "required"},
                                    "item_9": {"name": "item9", "type": "inputNumber", "validator_rule": "required"},
                                    "nid": {
                                            "builder": {
                                                "type": "short_long_answer"
                                            },
                                            "display": {
                                                "placeholder": "ID Card Number",
                                                "label": "ID Card Number",
                                                "mask": "national_id"
                                            },
                                            "name": "nid",
                                            "validator_rule": "required_if:document_number_type,nid|regex:/^[0-9]{13}$/i|pass_checksum",
                                            "props": {
                                                "maxlength": 280
                                            },
                                            "layout": "InputControl",
                                            "visible": {
                                                "document_number_type": "required|is:nid"
                                            },
                                            "type": "InputText"
                                        },
                                    "item_10": {"name": "item10", "type": "inputText"},
                                    "item_11": {"name": "item11", "type": "inputText"},
                                    },
                            }
                        }
                    }
                },
                "encrypt_answers": ["item1", "item2", "item3", "item7", "item8", "item9", "user_id", "updated_data", "email", "mobile_number", "nid"],
                "keep_answers": {
                    "user_id": "{{user_id}}",
                    "updated_data": "{{updated_data}}",
                    "mobile_number": "{{mobile_number}}",
                }
            },
        )
        self.admin = User.objects.create_user(username="owner_admin", is_active=True)
        form.save()
        self.workspace = Workspace.objects.create(slug='test', name='workspace-test', enable_post_paid=True, created_by=self.admin)
        self.member = WorkspaceMember.objects.create(
            workspace=self.workspace, user=self.admin, role=Role.ADMIN, status=MemberStatus.ACCEPTED
        )
        WorkspaceForm.objects.create(workspace=self.workspace, form=form)
        FormSetting.objects.create(settings={}, form=form)
        self.form = form
        self.applied_form = form.apply()

    @mock.patch('dynamicform.submodules.appliedform.dynamicform.info.DynamicformInfo.set_up_crequest', mock.Mock())
    @mock.patch("dynamicform.submodules.appliedform.views.appliedform.AppliedFormViewSet.get_permissions", mock.Mock(return_value=[]))
    @mock.patch("dynamicform.submodules.appliedform.views.appliedform.AppliedFormViewSet.permission_classes", ['AllowAny'])
    @mock.patch("dynamicform.submodules.appliedform.views.appliedform.ACTION_NEED_PERMISSION", [])
    @mock.patch('dynamicform.submodules.appliedform.dynamicform.dynamicform.Dynamicform.partition_application',  mock.Mock())
    @mock.patch('application_user_log.logging.current_user', mock.Mock(return_value=None))
    def test_can_save_encrypt_answers(self):
        # done : add test text virable type, text, number, boolean, array, object
        # done : save, submit(submit), update(`app_update_answers`), create(create), admin(post) -> check text validation (submit)
        # decision flow decrypt answers step compare -> ไม่ให้ log decrypt , หรือ ให้ log decrypt data -> clear requirement
        body = {
            "answers": {
                "step1": {"s1": {"item1": "answer 1", "item2": {"a": "b"}, "mockupstepapp_itemb": "No answer"}}
            }
        }
        factory = APIRequestFactory()
        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "save"})
        response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        answer = Answer.objects.filter(applied_form=self.applied_form, question="item1").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, "answer 1")
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), "answer 1")
        answer = Answer.objects.filter(applied_form=self.applied_form, question="item2").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, {"a": "b"})
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), {"a": "b"})
        exists = Answer.objects.filter(applied_form=self.applied_form, question="mockupstepapp_itemb").exists()
        self.assertFalse(exists)

        body = {
            "answers": {
                "step1": {"s1": {"item1": "answer change", "item2": {"c": "d"}, "item3": 4}}
            }
        }

        # test save have old answer, test encrypt: str, dictionary
        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "save"})
        response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        answer = Answer.objects.filter(applied_form=self.applied_form, question="item1").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, "answer change")
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), "answer change")
        answer = Answer.objects.filter(applied_form=self.applied_form, question="item2").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, {"c": "d"})
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), {"c": "d"})
        answer = Answer.objects.filter(applied_form=self.applied_form, question="item3").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, 4)
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), 4)
        body = {
            "answers": {
                "step2": {"s2": {"item7": ["a", "b", "c"], "item8": False, "item9": 99, "nid": "1103300122802"}}
            }
        }

        # test submit test encrypt: list, boolean, number
        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "submit"})
        response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        answer = Answer.objects.filter(applied_form=self.applied_form, question="item7").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, ["a", "b", "c"])
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), ["a", "b", "c"])
        answer = Answer.objects.filter(applied_form=self.applied_form, question="item8").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, False)
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), False)
        answer = Answer.objects.filter(applied_form=self.applied_form, question="item9").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, 99)
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), 99)

        # test update old answer
        body = {
            "answers": {
                "item1": "new answer 1"
            }
        }

        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "app_update_answers"})
        response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        answer = Answer.objects.filter(applied_form=self.applied_form, question="item1").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, "new answer 1")
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), "new answer 1")

        # test update new answer -> keep answer
        body = {
            "answers": {
                "updated_data": "updated_data"
            }
        }

        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "app_update_answers"})
        response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        answer = Answer.objects.filter(applied_form=self.applied_form, question="updated_data").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, "updated_data")
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), "updated_data")

    @mock.patch("dynamicform.submodules.appliedform.dynamicform.info.DynamicformInfo.set_up_crequest", mock.Mock())
    @mock.patch("dynamicform.submodules.appliedform.views.appliedform.AppliedFormViewSet.get_permissions", mock.Mock(return_value=[]))
    @mock.patch("dynamicform.submodules.appliedform.views.appliedform.AppliedFormViewSet.permission_classes", ['AllowAny'])
    @mock.patch("dynamicform.submodules.appliedform.views.appliedform.ACTION_NEED_PERMISSION", [])
    @mock.patch("workspace.views.application.ApplicationViewSet.get_permissions", mock.Mock(return_value=[]))
    @mock.patch("workspace.views.application.ApplicationViewSet.permission_classes", ['AllowAny'])
    @mock.patch('dynamicform.submodules.appliedform.dynamicform.dynamicform.Dynamicform.partition_application',  mock.Mock())
    @mock.patch('application_user_log.logging.current_user', mock.Mock(return_value=None))
    def test_can_save_encrypt_partition_answers(self):
        # test save partition answer case create
        body = {
            "answers": {
                "updated_data": "test create"
            }
        }
        factory = APIRequestFactory()
        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "create"})
        response = view(request, form_slug=self.form.slug)
        appiled_form_slug = response.data.get('detail').get('applied_form')
        applied_form = AppliedForm.objects.get(slug=appiled_form_slug)
        answer = Answer.objects.filter(applied_form=applied_form, question="updated_data").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, "test create")
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), "test create")

        body = {
            "answers": {
                "step1": {"s1": {"item1": "answer 1", "item2": {"a": "b"}, "email": "myemail", "item6": "xxx"}}
            }
        }
        
        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "save"})
        response = view(request, form_slug=self.form.slug, slug=appiled_form_slug)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        answer = Answer.objects.filter(applied_form=applied_form, question="item1").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, "answer 1")
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), "answer 1")
        answer = Answer.objects.filter(applied_form=applied_form, question="item2").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, {"a": "b"})
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), {"a": "b"})

        answer = Answer.objects.filter(applied_form=applied_form, question="email").first()
        self.assertNotEqual(answer.value, "myemail")
        partition_answer = PartitionAnswer.objects.get(value=answer.value)
        self.assertEqual(applied_form.slug, partition_answer.application.slug)
        self.assertEqual(answer.value, partition_answer.value)
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), "myemail")
        answer = Answer.objects.filter(applied_form=applied_form, question="item6").first()
        self.assertEqual(answer.value, 'xxx')

        # test save partition answer case update
        body = {
            "answers": {
                "step1": {"s1": {"email": {"c": "d"}}}
            }
        }

        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "save"})
        response = view(request, form_slug=self.form.slug, slug=applied_form.slug)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        answer = Answer.objects.filter(applied_form=applied_form, question="email").first()
        self.assertNotEqual(answer.value, {"c": "d"})
        partition_answer = PartitionAnswer.objects.get(value=answer.value)
        self.assertEqual(applied_form.slug, partition_answer.application.slug)
        self.assertEqual(answer.value, partition_answer.value)
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), {"c": "d"})

        body = {
            "answers": {
                "step2": {"s2": {"item7": ["a", "b", "c"], "item8": False, "item9": 99, "nid": "1103300122802"}}
            }
        }

        # submit
        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "submit"})
        response = view(request, form_slug=self.form.slug, slug=appiled_form_slug)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # test update old answer and new answer
        body = {
            "answers": {
                "email": "<EMAIL>",
                "mobile_number": "08xxxxxxxxx"
            }
        }

        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "app_update_answers"})
        response = view(request, form_slug=self.form.slug, slug=appiled_form_slug)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        answer = Answer.objects.filter(applied_form=applied_form, question="email").first()
        self.assertNotEqual(answer.value, "<EMAIL>")
        partition_answer = PartitionAnswer.objects.get(value=answer.value)
        self.assertEqual(appiled_form_slug, partition_answer.application.slug)
        self.assertEqual(answer.value, partition_answer.value)
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), "<EMAIL>")
        answer = Answer.objects.filter(applied_form=applied_form, question="mobile_number").first()
        self.assertNotEqual(answer.value, "08xxxxxxxxx")
        partition_answer = PartitionAnswer.objects.get(value=answer.value)
        self.assertEqual(appiled_form_slug, partition_answer.application.slug)
        self.assertEqual(answer.value, partition_answer.value)
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), "08xxxxxxxxx")

        # ApplicationSearchViewSet.as_view({"get": "list"})

        # test search partition encrypt answer
        params = {
            'page': 1,
            'ordering': '',
            'forms': self.form.slug,
            'page_size':'50',
            'search':'<EMAIL>', # encrypted value\
            'search_in':'email',
            'answers':"item1,item2,item3,item4,item5,item6,item7,item8,item9,email",
            'desc': 1,
            'flat_data': 1,
            'date_arrgreate': 'created_at',
            'custom_status': {}
        }
        request = factory.get("", params)
        force_authenticate(request, user=self.admin)
        view = ApplicationViewSet.as_view({"get": "list"})
        response = view(request, workspace_slug=self.workspace.slug)
        count = response.data.get('count')
        results = response.data.get('results', [])[0].get('data', {})
        self.assertEqual(results.get('email'), '<EMAIL>')
        self.assertEqual(count, 1)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # test search normal encrypt answer
        params = {
            'page': 1,
            'forms': self.form.slug,
            'page_size':'50',
            'search':'answer 1', # encrypted value\
            'search_in':'item1',
            'answers':"item1,item2,item3,item4,item5,item6,item7,item8,item9,email",
        }
        request = factory.get("", params)
        force_authenticate(request, user=self.admin)
        view = ApplicationViewSet.as_view({"get": "list"})
        response = view(request, workspace_slug=self.workspace.slug)
        results = response.data.get('results', [])[0].get('data', {})
        self.assertEqual(results.get('item1'), 'answer 1')
        count = response.data.get('count')
        self.assertEqual(count, 1)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # test search normal answer
        params = {
            'page': 1,
            'forms': self.form.slug,
            'page_size':'50',
            'search': 'xxx',
            'search_in': 'item6',
            'answers':"item1,item2,item3,item4,item5,item6,item7,item8,item9,email",
        }
        request = factory.get("", params)
        force_authenticate(request, user=self.admin)
        view = ApplicationViewSet.as_view({"get": "list"})
        response = view(request, workspace_slug=self.workspace.slug)
        count = response.data.get('count')
        self.assertEqual(count, 1)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # test search partition encrypt answer not found
        params = {
            'page': 1,
            'forms': self.form.slug,
            'search':'<EMAIL>', # encrypted value\
            'search_in':'email',
            'answers':"item1,item2,item3,item4,item5,item6,item7,item8,item9,email",
        }
        request = factory.get("", params)
        force_authenticate(request, user=self.admin)
        view = ApplicationViewSet.as_view({"get": "list"})
        response = view(request, workspace_slug=self.workspace.slug)
        count = response.data.get('count')
        self.assertEqual(count, 0)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # test search normal encrypt answer
        params = {
            'page': 1,
            'forms': self.form.slug,
            'page_size':'50',
            'search':'answer 0', # encrypted value\
            'search_in':'item1',
            'answers':"item1,item2,item3,item4,item5,item6,item7,item8,item9,email",

        }
        
        request = factory.get("", params)
        force_authenticate(request, user=self.admin)
        view = ApplicationViewSet.as_view({"get": "list"})
        response = view(request, workspace_slug=self.workspace.slug)
        count = response.data.get('count')
        self.assertEqual(count, 0)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # test search normal answer
        params = {
            'page': 1,
            'forms': self.form.slug,
            'page_size':'50',
            'search': '5',
            'search_in':'item6',
            'answers':"item1,item2,item3,item4,item5,item6,item7,item8,item9,email",
        }
        request = factory.get("", params)
        force_authenticate(request, user=self.admin)
        view = ApplicationViewSet.as_view({"get": "list"})
        response = view(request, workspace_slug=self.workspace.slug)
        count = response.data.get('count')
        self.assertEqual(count, 0)
        self.assertEqual(response.status_code, status.HTTP_200_OK)


        # >>>>
        # response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)
        # self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        # answer = Answer.objects.filter(applied_form=self.applied_form, question="updated_data").first()
        # # answer is encrypted
        # self.assertNotEqual(answer.value, "updated_data")
        # # check decrypted value
        # self.assertEqual(answer.get_answers_dict('_value').get('_value'), "updated_data")

        # test search partition answer

    def test_can_create_and_save_encrypt_answers(self):
        body = {
            "answers": {
                "updated_data": "test create"
            }
        }
        factory = APIRequestFactory()
        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "create"})
        response = view(request, form_slug=self.form.slug)
        # form_slug = response.data.get('detail').get('form')
        appiled_form_slug = response.data.get('detail').get('applied_form')
        applied_form = AppliedForm.objects.get(slug=appiled_form_slug)
        answer = Answer.objects.filter(applied_form=applied_form, question="updated_data").first()
        # answer is encrypted
        self.assertNotEqual(answer.value, "test create")
        # check decrypted value
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), "test create")

    def test_can_save_with_answers_validation(self):
        body = {"answers": {"step1": {"s1": {"item1": "answer 1", "item3": 1}}}}

        factory = APIRequestFactory()
        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "save"})

        response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        answer = Answer.objects.filter(applied_form=self.applied_form, question="item1").first()
        self.assertNotEqual(answer.value, "answer 1")
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), "answer 1")
        exists = Answer.objects.filter(applied_form=self.applied_form, question="item3").exists()
        self.assertFalse(exists)

    def test_cannot_save_disable_answers(self):
        body = {
            "answers": {
                "step1": {
                    "s1": {
                        "item1": "answer 1",
                        "item5": "answer 5",
                    }
                }
            }
        }

        factory = APIRequestFactory()
        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "save"})
        response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        answer = Answer.objects.filter(applied_form=self.applied_form, question="item1").first()
        self.assertNotEqual(answer.value, "answer 1")
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), "answer 1")
        exists = Answer.objects.filter(applied_form=self.applied_form, question="item5").exists()
        self.assertFalse(exists)

    def test_cannot_overide_disable_answers(self):
        body = {
            "answers": {
                "step1": {
                    "s1": {
                        "item5": "answer 5",
                    }
                }
            }
        }

        factory = APIRequestFactory()
        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "save"})
        Answer.objects.create(applied_form=self.applied_form, question="item5", value="item 5")
        response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        answer = Answer.objects.filter(applied_form=self.applied_form, question="item5").first()
        self.assertEqual(answer.value, "item 5")

    @mock.patch('dynamicform.submodules.appliedform.dynamicform.info.DynamicformInfo.set_up_crequest', mock.Mock())
    @mock.patch('dynamicform.submodules.appliedform.functions.AppliedFormFunction.can_view_report', mock.Mock(return_value=True))
    @mock.patch('dynamicform.submodules.appliedform.functions.AppliedFormFunction.set_can_view_report', mock.Mock())
    @mock.patch('dynamicform.submodules.appliedform.functions.AppliedFormFunction.can_view_info', mock.Mock(return_value=True))
    @mock.patch('dynamicform.submodules.appliedform.functions.AppliedFormFunction.set_can_view_info', mock.Mock())
    @mock.patch("dynamicform.submodules.appliedform.views.appliedform.AppliedFormViewSet.get_permissions", mock.Mock(return_value=[]))
    @mock.patch("dynamicform.submodules.appliedform.views.appliedform.AppliedFormViewSet.permission_classes", ['AllowAny'])
    @mock.patch("dynamicform.submodules.appliedform.views.appliedform.ACTION_NEED_PERMISSION", [])
    @mock.patch('application_user_log.logging.current_user', mock.Mock(return_value=None))
    @mock.patch('dynamicform.submodules.appliedform.dynamicform.dynamicform.Dynamicform.partition_application',  mock.Mock())
    def test_encrypt_and_decrypt_for_report(self):
        factory = APIRequestFactory()
        body = {
            "answers": {
                "step1": {"s1": {"item1": "answer 1", "item2": {"a": "b"}, "item6": 22}}
            }
        }
        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "save"})
        response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)
        answer = Answer.objects.filter(applied_form=self.applied_form, question="item6").first()
        self.assertEqual(answer.value, 22)
        self.assertEqual(answer.get_answers_dict('_value').get('_value'), 22)

        body = {
            "answers": {
                "step2": {"s2": {"item7": ["a", "b", "c"], "item8": False, "item9": "99", "nid": "1103300122802", "item10": {"test": "test"}, "item11": ['a', 'b', 'c']}}
            }
        }

        # submit
        request = factory.post("", body, format="json")
        view = AppliedFormViewSet.as_view({"post": "submit"})
        response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # report data in dashboard
        request = factory.get('')
        view = AppliedFormViewSet.as_view({'get': 'report_data'})
        response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data.get('item1'), 'answer 1')
        self.assertEqual(response.data.get('item2'), {'a': 'b'})
        self.assertEqual(response.data.get('item6'), 22)
        self.assertEqual(response.data.get('item7'), ["a", "b", "c"])
        self.assertEqual(response.data.get('item8'), False)
        self.assertEqual(response.data.get('nid'), '1103300122802')
        self.assertEqual(response.data.get('item10'), {"test": "test"})
        self.assertEqual(response.data.get('item11'), ['a', 'b', 'c'])

        # report answers
        view = AppliedFormViewSet.as_view({'get': 'report_answers'})
        response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)
        dynamicform_list = response.data.get('answers', {}).get('dynamicform', [])
        check_data_dict = {
            'item1': 'answer 1',
            'item2': {'a': 'b'},
            'item6': 22,
            'item7': ["a", "b", "c"],
            'item8': False,
            'nid': '1103300122802',
            'item10': {"test": "test"},
            'item11': ['a', 'b', 'c'],
        }
        for dynamicform in dynamicform_list:
            check_value = check_data_dict.get(dynamicform.get('question', ''))
            if check_value:
                value = dynamicform.get('value')
                self.assertEqual(check_value, value)

        # webhook already test manual -> answer.get_submit_value same (report_answers, applied_form_result)

        # get result
        view = AppliedFormViewSet.as_view({'get': 'applied_form_result'})
        response = view(request, form_slug=self.form.slug, slug=self.applied_form.slug)
        self.assertEqual(response.data.get('answers').get('item1').get('value'), 'answer 1')
        self.assertEqual(response.data.get('answers').get('item2').get('value'), {'a': 'b'})
        self.assertEqual(response.data.get('answers').get('item6').get('value'), 22)
        self.assertEqual(response.data.get('answers').get('item7').get('value'), ["a", "b", "c"])
        self.assertEqual(response.data.get('answers').get('item8').get('value'), False)
        self.assertEqual(response.data.get('answers').get('nid').get('value'), '1103300122802')
        self.assertEqual(response.data.get('answers').get('item10').get('value'), {"test": "test"})
        self.assertEqual(response.data.get('answers').get('item11').get('value'), ['a', 'b', 'c'])

        # decision flow test manual -> get_data_result -> answers
        datapoint_answer = DataPointAnswer()
        datapoint_answer.applied_form = self.applied_form
        datapoint_answer.form = self.form
        dynamicform_mock = mock.Mock()
        dynamicform_mock.answers = None
        datapoint_answer.dynamicform = dynamicform_mock

        result = datapoint_answer.get_data_result(values_list=['item1', 'item2', 'item7', 'item8','item6', 'nid', 'item10', 'item11'])
        self.assertEqual(result.get('item1'), 'answer 1')
        self.assertEqual(result.get('item2'), {'a': 'b'})
        self.assertEqual(result.get('item6'), 22)
        self.assertEqual(result.get('item7'), ["a", "b", "c"])
        self.assertEqual(result.get('item8'), False)
        self.assertEqual(result.get('nid'), '1103300122802')
        self.assertEqual(result.get('item10'), {"test": "test"})
        self.assertEqual(result.get('item11'), ['a', 'b', 'c'])