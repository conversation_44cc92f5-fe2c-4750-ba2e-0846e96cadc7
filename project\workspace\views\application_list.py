from django.conf import settings
from dateutil.relativedelta import relativedelta
from rest_framework.response import Response
from dynamicform.models import PartitionApplication
from dynamicform.submodules.application.serializer_partition_application_list import (
    ApplicationPartitionSerializer,
    ApplicationPartitionDownloadSerializer
)
import datetime
import json
from dynamicform.submodules.form.models import Form
from dynamicform.submodules.answer.models import Answer

DATABASE_MODEL_USE_DB = settings.DATABASE_MODEL_USE_DB.get('application', {})
DYNAMICFORM_SEARCHABLE_QUESTIONS = settings.DYNAMICFORM_SEARCHABLE_QUESTIONS


class ApplicationList:
    def get_custom_status_filter(self, query):
        custom_status = query.get('custom_status', '')
        try:
            return json.loads(custom_status)
        except:
            return
    
    def get_start_end_today(self, start_date=None, end_date=None, **kwargs):
        today = datetime.datetime.now()
        return today, today

    def get_start_end_this_year(self, start_date=None, end_date=None, **kwargs):
        start_date = datetime.datetime(year=end_date.year, month=1, day=1)
        end_date = datetime.datetime(year=end_date.year, month=12, day=31)
        return start_date, end_date

    def get_start_end_last_days(self, start_date=None, end_date=None, **kwargs):
        end_date = end_date + relativedelta(days=1)
        return start_date, end_date

    def get_start_end_last_7_days(self, start_date=None, end_date=None, **kwargs):
        start_date = end_date - relativedelta(days=7)
        return start_date, end_date

    def get_start_end_last_30_days(self, start_date=None, end_date=None, **kwargs):
        start_date = end_date - relativedelta(days=30)
        return start_date, end_date

    def get_start_end_this_month(self, start_date=None, end_date=None, months=1, **kwargs):
        def get_start_end(end):
            next_month = end + relativedelta(months=1)
            end = datetime.datetime(
                year=next_month.year, 
                month=next_month.month, 
                day=1) - relativedelta(days=1)
            start = end - relativedelta(months=months)
            return start, end

        start_date, end_date = get_start_end(end_date)
        return start_date, end_date

    def get_date_range_with_time_span(self, start_date=None, end_date=None, time_span=None):
        if time_span == 'today':
            start_date, end_date = self.get_start_end_today(
                start_date=start_date,
                end_date=end_date
            )
        elif time_span == 'this_year':
            start_date, end_date = self.get_start_end_this_year(
                start_date=start_date,
                end_date=end_date
            )
        elif time_span == 'last_7_days':
            start_date, end_date = self.get_start_end_last_7_days(
                start_date=start_date,
                end_date=end_date
            )
        elif time_span == 'last_30_days':
            start_date, end_date = self.get_start_end_last_30_days(
                start_date=start_date,
                end_date=end_date
            )
        elif time_span == 'this_month':
            start_date, end_date = self.get_start_end_this_month(
                start_date=start_date,
                end_date=end_date, months=1
            )
        elif time_span == 'last_3_months':
            start_date, end_date = self.get_start_end_this_month(
                start_date=start_date,
                end_date=end_date, months=3
            )
        elif not (start_date and end_date):
            start_date, end_date = self.get_start_end_today(  # NOSONAR
                start_date=start_date,
                end_date=end_date
            )

        return start_date, end_date
    
    def get_date_filter_params(self, query={}):
        start_date = query.get('start_date', None)
        if start_date:
            start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d")

        end_date = query.get('end_date', None)
        if end_date:
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")
        else:
            end_date = datetime.datetime.now()

        return start_date, end_date

    def get_filter_date_range(self, query):
        time_span = query.get('time_span', None)
        start_date, end_date = self.get_date_filter_params(query)
        start_date, end_date = self.get_date_range_with_time_span(
            start_date,
            end_date,
            time_span
        )
        try:
            return start_date.date().isoformat(), end_date.date().isoformat()
        except:
            today = datetime.datetime.now()
            today = today.date().isoformat()
            return today, today
    
    def list(self, request, *args, **kwargs):
        use_db = DATABASE_MODEL_USE_DB.get('read', 'default')
        query = self.request.GET

        download = query.get('download', '0')
        if bool(int(download)):
            self.pagination_class = None

        user_id = None
        is_api_token = None
        if not self.request.user.is_superuser:
            user_id = self.request.user.id
            is_api_token = self.request.user.is_api_token

        workspace_slug = self.kwargs.get('workspace_slug', None)
        form_slug = query.get('forms', '')
        answers = query.get('answers', '').split(',')
        answers = tuple(answers)
        application_status = query.get('application_status', None)
        search = query.get('search', '')
        search_in = query.get('search_in', '')
        search_in_props = query.get('search_in_props', '')
        ordering = query.get('ordering', '')
        time_span = query.get('time_span', 'last_7_days')
        items = query.get('items', '').split(',')
        items = tuple(items)
        custom_status = self.get_custom_status_filter(query)
        encrypt_answers_list = []
        if form_slug:
            form = Form.objects.get(slug=form_slug)
            encrypt_answers_list = form.backend_schema.get("encrypt_answers", [])
        
        filter_applications = '''
        with filter_applications as (
            select distinct(dpa.application_id)
            , dpa.workspace_id
            , dpa.application_slug
            , dpa.applied_form_id
            , dpa.form_id
            from dynamicform_partitionapplication dpa
            where dpa.workspace_slug = %s
        '''
        filter_params = [workspace_slug]

        if time_span != 'all':
            start_date, end_date = self.get_filter_date_range(query)
            filter_applications = filter_applications + '''
            and date(dpa.application_created_at) between %s and %s
            '''
            filter_params.append(start_date)
            filter_params.append(end_date)

        if form_slug:
            form_slug = form_slug.split(',')
            form_slug = [i for i in form_slug if i != '']
        if form_slug:
            filter_applications = filter_applications + '''
            and dpa.form_slug in %s
            '''
            filter_params.append(tuple(form_slug))

        if items not in [None, tuple(['']), '']:
            filter_applications = filter_applications + '''
            and dpa.application_id in %s
            '''
            filter_params.append(items)

        if application_status:
            filter_applications = filter_applications + '''
            and dpa.application_status = %s
            '''
            filter_params.append(application_status)

        if search_in_props == 'slug' and search not in [None, '']:
            filter_applications = filter_applications + '''
            and dpa.application_slug = %s
            '''
            filter_params.append(search)
        
        if user_id:
            if is_api_token:
                filter_applications = filter_applications + '''
                and dpa.form_id in (
                    select distinct wtf.form_id 
                    from workspace_token as wt 
                    inner join workspace_token_forms wtf on wt.id = wtf.token_id 
                    where wt.user_id = %s
                )
                '''
            else:
                filter_applications = filter_applications + '''
                and dpa.form_id in (
                    select distinct wf.form_id 
                    from workspace_member wm 
                    inner join workspace_form wf on wm.workspace_id = wf.workspace_id 
                    where wm.user_id = %s
                )
                '''
            filter_params.append(user_id)

        filter_applications = filter_applications + ')'
        next_with = 'filter_applications'
        
        if custom_status not in [None, '', {}]:
            having_custom_status = []
            having_filter_params = []
            for custom_status_name, custom_status_value in custom_status.items():
                having_custom_status.append(
                    'count(case when name = %s and value = %s then 1 end) > 0 '
                )
                having_filter_params.append(custom_status_name)
                having_filter_params.append(custom_status_value)

            if having_custom_status:
                having_custom_status = 'and '.join(having_custom_status)
                filter_applications = filter_applications + f'''
                , filter_custom_status as (
                    select distinct(fa.*)
                    from {next_with} fa
                    inner join dynamicform_applicationstatus da on da.application_id = fa.application_id
                    group by fa.application_id, fa.workspace_id, fa.application_slug, fa.applied_form_id, fa.form_id
                    having {having_custom_status}
                )
                '''
                filter_params = filter_params + having_filter_params
                next_with = 'filter_custom_status'

        if search_in_props != 'slug' and search not in [None, ''] and search_in not in [None, '', {}]:
            if search_in in DYNAMICFORM_SEARCHABLE_QUESTIONS:
                filter_applications = filter_applications + f'''
                , filter_answer as (
                    select distinct(fa.*)
                    from {next_with} fa
                    inner join dynamicform_partitionanswer da on fa.application_id = da.application_id
                    where da.question = %s and da.value = %s
                )
                '''
                if search_in in encrypt_answers_list:
                    encrypt_search = Answer.encrypt_value(None, search, form)
                    search = encrypt_search
            else:
                filter_applications = filter_applications + f'''
                , filter_answer as (
                    select distinct(fa.*)
                    from {next_with} fa
                    inner join dynamicform_answer da on fa.applied_form_id = da.applied_form_id
                    where da.question = %s and da.value = %s 
                )
                '''
                if search_in in encrypt_answers_list:
                    encrypt_search = Answer.encrypt_value(None, search, form)
                    search = f'"{encrypt_search}"'
                else:
                    search = f'"{search}"'

            filter_params.append(search_in)
            filter_params.append(search)
            next_with = 'filter_answer'
        
        filter_applications = filter_applications + f'''
        , custom_status_obj as (
            select fa.application_id, jsonb_object_agg(name, value) custom_status
            from {next_with} fa
            inner join dynamicform_applicationstatus da on fa.application_id = da.application_id
            group by fa.application_id
        )
        '''

        if answers not in [None, tuple(['']), '']:
            filter_applications = filter_applications + f'''
            , answers_list as (
                select fa.application_id, jsonb_object_agg(da.question, jsonb_build_object('value', (da.value::jsonb)::text::jsonb, 'is_encrypt', da.is_encrypt)) answers
                from {next_with} fa 
                inner join dynamicform_answer da on fa.applied_form_id = da.applied_form_id
                where da.question in %s
                group by fa.application_id
            )
            '''
            filter_params.append(answers)
            select_answers = ', al.answers as answers'
            join_answers = 'left join answers_list al on dpa.application_id = al.application_id'
        else:
            select_answers = ', \'{}\'::jsonb as answers'
            join_answers = ''

        if ordering == 'created_at':
            order_by = 'order by dpa.application_created_at'
        else:
            order_by = 'order by dpa.application_created_at desc'

        filter_applications = filter_applications + f'''
        select distinct(dpa.*), df.name as form_name, cso.custom_status as custom_status {select_answers}
        from {next_with} fa
        inner join dynamicform_partitionapplication dpa on dpa.application_id = fa.application_id
        inner join dynamicform_form df on dpa.form_id = df.id
        left join custom_status_obj cso on dpa.application_id = cso.application_id
        {join_answers}
        {order_by}
        '''
        partition_applications = PartitionApplication.objects.using(use_db).raw(
            filter_applications,
            params=filter_params,
            translations={
                "pk": "id",
                "application_id": "application_id",
                "custom_status": "custom_status",
                "answers": "answers",
            }
            
        )

        page = self.paginate_queryset(partition_applications)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response = self.get_paginated_response(serializer.data)
        else:
            serializer = self.get_serializer(partition_applications, many=True)
            response = Response(serializer.data)

        if bool(int(download)):
            response = self.update_pandas_headers(response)

        return response
    
    def get_serializer_class(self):
        if self.action not in ['list']:
            return self.serializer_class
        
        query = self.request.GET
        download = query.get('download', '0')
        if bool(int(download)):
            return ApplicationPartitionDownloadSerializer
        
        return ApplicationPartitionSerializer
