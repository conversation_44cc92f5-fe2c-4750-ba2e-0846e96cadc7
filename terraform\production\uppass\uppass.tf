# todo: change to UAT
module "uppass" {
    source = "../../modules/gcloud-run-v2-web"
    
    google_cloud_credentials = var.google_cloud_credentials
    project = "uppass-app"
    name = "uppass"
    region = "asia-east1"
    service_account_name = "<EMAIL>"
    cpu = "2000m"
    memory = "2Gi"
    sandbox = "gvisor"
    cloudsql_instance_connection_name = "uppass-app:asia-east1:uppass"
    client_name = "cloud-console"
    min_scale = "2"
    
    image = var.image

    override_env = {
        "APP_NAME": "uppass",
        "APP_ENV": "prod",
        # "CURRENT_HOST": "xxx",
        "DEBUG": "0",
        "ENABLE_ADMIN": "1",
        "ENABLE_ACCESS_ADMIN_PATH": "0",
        "ENABLE_AUTO_MIGRATE": "0",
        # "SECRET_KEY": "SM KEY",

        "ENABLE_SECRET_MANAGER": "1",

        "SECRET_MANAGER_PROJECT_ID": "uppass-app",
        "SECRET_MANAGER_PREFIX": "UPPASS_PROD_",

        "QUEUE_PROJECT_ID": "uppass-app",
        "QUEUE_PATH_LOCATION": "us-central1",
        "QUEUE_ID": "uppass",

        "SQL_ENGINE": "psqlextra.backend",
        "SQL_HOST": "/cloudsql/uppass-app:asia-east1:uppass",
        "SQL_DATABASE": "uppass",
        "SQL_USER": "uppass",
        "SQL_PORT": "5432",
        # "SQL_PASSWORD": "SM KEY",

        "STACKDRIVER_PROJECT_ID": "uppass-app",
        "STACKDRIVER_RESOURCE_TYPE": "cloud_run_revision",
        "STACKDRIVER_RESOURCE_LABEL": "{\"configuration_name\":\"uppass\",\"location\":\"asia-east1\",\"project_id\":\"uppass-app\",\"revision_name\":\"uppass\",\"service_name\":\"uppass\"}",

        "STATIC_URL_TEMPLATE": "https://cdn.uppass.io/form/assets/{COMMIT_SHA}/dist/",
        "FRONTEND_SETTING_PREFIX": "VUE_APP_",
        "VUE_APP_TEXT_TH": "{}",
        "VUE_APP_SETTING_NAMESPACE": "appSettings",
        "VUE_APP_DEFAULT_LOCALE": "en",
        "VUE_APP_LOCALE_LIST": "[\"en\"]",
        "VUE_APP_TEXT_EN": "{}",
        "VUE_APP_RECAPTCHA_ENABLED": "True",
        # "VUE_APP_RECAPTCHA_INVISIBLE_SITE_KEY": "Manual add",
        # "VUE_APP_RECAPTCHA_CHECKBOX_SITE_KEY": "Manual add",
        "VUE_APP_ALLOW_SOCIAL_AUTH": "True",
        "VUE_APP_TEXT_TH": "{\"navigation\": {\"submit\": \"ยืนยันข้อมูล\"}}",
        "VUE_APP_NAVBAR_LOGO_URL": "https://www.uppass.io/brand-logo-beta.svg",
        "VUE_APP_THEME_URL": "https://cdn.uppass.io/form/themes/latest/verifio/style.css",
        "WEB_ICON": "https://www.uppass.io/assets/static/favicon.ac8d93a.af839e707d5e0e6b48c240c2c99517be.png",
        "WEB_TITLE": "UpPass",

        "LANGUAGE_CODE": "th",
        "LANGUAGES": "[[\"th\",\"Thai\"],[\"en\",\"English\"]]",

        # STORAGE
        # "GOOGLE_SERVICE_ACCOUNT_BASE64": "SM KEY",
        "GS_PROJECT_ID": "uppass-app",
        "GS_BUCKET_NAME": "uppass",
        
        "DYNAMICFORM_RUNNING_NUMBER_FORMAT": "COK{0:{1}{2}d}",
        "DYNAMICFORM_SLUG_RANDOM_CHANCE": pow(10, 18),
        "FORMS_MICROSERVICE": "{}"

        "SERVICES_ENDPOINT": "https://identity-api.uppass.io",
        "SERVICES_APP_ID": "**********",
        # "SERVICES_APP_SECRET": "SM KEY",
        "SERVICES_EXTERNAL_WHITELIST": "[\"https://identity-api.uppass.io\"]",
        "SERVICES_MS_FORMS": "{}",

        # "LEDGER_ENDPOINT": "https://payment.creditok.co",
        # "LEDGER_GET_TOKEN_URL": "/api/staff/auth/",
        # "LEDGER_USERNAME": "???",
        # "LEDGER_PASSWORD": "SM KEY",
        
        # "PSYCHOMETRIC_SOURCE": "???",

        # "OTP_REQUEST_REF_LEVEL": "{}",
        "OTP_NOTIFICATION_CHANNEL": "sms_otp",
        "OTP_EMAIL_NOTIFICATION_CHANNEL": "email_postmarkapp",
        "NOTIFICATION_SERVICE_URL": "https://notification-cewpxczx4a-de.a.run.app/message",
        # "SUBMIT_FORM_NOTIFICATION_CHANNEL": "sms_otp_form_prod",
        
        "EKYC_APP_ID": "**********",
        # "EKYC_APP_SECRET": "SM KEY",
        "EKYC_ENDPOINT": "https://identity-api.uppass.io",
        "EKYC_ENDPOINT_LIVENESS_VERSION": "/v4",
        "EKYC_ENDPOINT_OCR_VERSION": "/v3",
        "EKYC_LIVENESS_MAX_ATTEMPT": "5",
        "EKYC_FRONT_CARD_MAX_ATTEMPT": "5",
        "EKYC_UPDATE_STATUS_TO_REF_METHOD": "dynamicform.hook.ekyc_update_application_status",
        "EKYC_ENABLE_SANDBOX_MODE": "1",

        # "BANK_STATEMENT_YODLEE_ENABLE": "1",
        "BANK_STATEMENT_INKREDO_ENDPOINT": "https://inkredo-api-aqfb5nrmvq-de.a.run.app",
        
        "BANK_STATEMENT_GATEWAY_ENDPOINT": "https://document-extraction-gateway-prod-2hinkbbcdq-uc.a.run.app",
        "BANK_STATEMENT_GATEWAY_TOKEN_USER_ID": ****************,
        "BANK_STATEMENT_GATEWAY_TOKEN_COMPANY_NAME": "UpPass",
        # "BANK_STATEMENT_GATEWAY_JWT_PASSWORD_SM_KEY": "BANK_STATEMENT_GATEWAY_JWT_PASSWORD",

        "RECAPTCHA_ENABLED": "True",
        # "RECAPTCHA_SECRET_KEY": "SM KEY", # verifio-invisible

        "SOCIAL_AUTH_GOOGLE_OAUTH2_KEY": "************-bo9csttk5lurblts1gq8q3e3gelufell.apps.googleusercontent.com",
        "SOCIAL_AUTH_GOOGLE_OPENID_WHITELISTED_DOMAINS": []
        # "SOCIAL_AUTH_ALLOW_ONLY_EXISTING_USER": "0",
        "ALLOW_SIGNUP": 1,

        "EMAIL_HOST": "smtp.postmarkapp.com",
        "EMAIL_HOST_USER": "************************************",
        "EMAIL_HOST_PASSWORD_SM_KEY": "EMAIL_HOST_PASSWORD_POSTMARKAPP",
        "EMAIL_PORT": "587",
        "EMAIL_USE_TLS": "True",

        "ENABLE_CREDIT_SYSTEM": 1,

        "STATISTICS_DASHBOARD_API_ENDPOINT": "https://statistics-dashboard-api-cewpxczx4a-de.a.run.app/",
        "STATISTICS_DASHBOARD_API_USERNAME": "uppass_dasboard_api",
        
        # "GITLAB_ACCESS_TOKEN": "***",
        "GITLAB_SCHEMA_REPO_ID": "********",
        "GITLAB_LINKED_ENV": "prod",
        "*********************": "uppass",

        "EXPORT_PDF_API_ENDPOINT": "https://asia-east1-uppass-app.cloudfunctions.net/export-pdf-micro-service",
        "EXPORT_PDF_API_USERNAME": "uppass",

        "DECISION_FLOW_TRIGGER_HOST": "https://uppass-background-task-cewpxczx4a-de.a.run.app",
        
        "DATA_POINT_TRUE_TELCO_HOST": "https://asia-southeast1-uppass-app.cloudfunctions.net/true_telco_api_prod",
        "DATA_POINT_TRUE_TELCO_USERNAME": "true_telco_api",
        "DATA_POINT_KNOWN_FACE_HOST": "https://identity-api.uppass.io",
        "DATA_POINT_KNOWN_FACE_APP_ID": "**********",
        "DATA_POINT_CREDEN_HOST": "https://api2.hjkl.ninja/get_detail_shareholder_uppass",
        "DATA_POINT_LOCAL_THAI_PEP_LIST_HOST": "https://thai-pep-list-prod-tz7upncgja-uc.a.run.app",
        "DATA_POINT_LOCAL_THAI_PEP_LIST_USERNAME": "thai-pep-list-prod",
        "DATA_POINT_UBO_HOST": "https://ubo-mircoservice-prod-***********.asia-southeast1.run.app",
        "DATA_POINT_UBO_USERNAME": "ubo-microservice-prod",
        "DATA_POINT_DBD_HOST": "https://dbd-bdex-microservice-prod-************.us-central1.run.app",
        "DATA_POINT_ASIA_VERIFY_V2_HOST": "https://api.asiaverify.com",

        "DOCUSIGN_HOST": "https://account.docusign.com",

        "SESSION_COOKIE_SAMESITE":"None",
        "CSRF_COOKIE_SAMESITE":"None",
    }

}