from django.contrib.auth import get_user_model
from django.db import models
from django.utils.translation import gettext_lazy as _
from dynamicform.submodules.model import AutoNow, SoftDelete
from dynamicform.util import current_user, client_ip_address
from app.auth.two_factor.cryptography import AESGCM
from django.forms.models import model_to_dict
import json
import datetime
import logging

from typing import TYPE_CHECKING
from data_point.provider.answer import ANSWER_LIST_STRING_TYPE

if TYPE_CHECKING:
    from dynamicform.submodules.appliedform.models.appliedform_smartuploader import AppliedFormSmartUploader
    from dynamicform.submodules.appliedform.models.appliedform_utility_bill import AppliedFormUtilityBill
    from dynamicform.submodules.answerfile.models import AnswerFile

User = get_user_model()

logger: logging.Logger = logging.getLogger(__name__)


class Answer(AutoNow):
    applied_form = models.ForeignKey(
        "AppliedForm",
        on_delete=models.CASCADE,
    )
    step = models.CharField(max_length=191, null=True)
    section = models.CharField(max_length=191, null=True)
    is_encrypt = models.BooleanField(blank=True, null=True, default=False)
    question = models.CharField(max_length=191, db_index=True)
    _value = models.TextField(db_column="value")
    deleted_at = models.DateTimeField(blank=True, null=True, db_index=True)

    class Meta:
        unique_together = ["applied_form", "question"]

    def save(self, *args, **kwargs):
        if self.is_encrypt:
            encrypt_value = Answer.encrypt_value(self.applied_form, self._value)
            if self.pk:
                try:
                    current = Answer.objects.get(pk=self.pk)
                    if current.value == encrypt_value or self._value == current.value:
                        return
                except Answer.DoesNotExist:
                    pass
            self._value = encrypt_value
            self.value = self._value
        super().save(*args, **kwargs)

    @property
    def value(self):
        # start = datetime.datetime.now()
        result = json.loads(self._value)
        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: json.loads(self._value) {diff.total_seconds()}')
        return result
    
    @property
    def decrypted_value(self):
        result = self.get_decrypt_value()
        return result

    @value.setter
    def value(self, value):
        if isinstance(value, str) and value:
            value = value.strip()
        value = json.dumps(value, ensure_ascii=False).encode("utf8")
        self._value = value.decode()

    @staticmethod
    def get_value_from_type_file(answer_id, **kwargs):
        from dynamicform.models import AnswerFile

        # start = datetime.datetime.now()
        items = []
        answer = None
        for item in AnswerFile.objects.filter(answer_id=answer_id).order_by("created_at"):
            items.append(item.info(**kwargs))
            answer = item.answer
        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: get_value check OfflineUploader {diff.total_seconds()}')
        return items, answer

    @staticmethod
    def get_value_from_type_file_from_answer_ids(answer_ids, **kwargs):
        from dynamicform.models import AnswerFile

        items = {}
        answer_files = AnswerFile.objects.filter(answer_id__in=answer_ids).order_by("created_at").all()
        for item in answer_files:
            if item.answer_id not in items:
                items[item.answer_id] = []

            items[item.answer_id].append(item.info(**kwargs))

        return items

    @staticmethod
    def get_value_from_type_signature(answer_id, **kwargs):
        from dynamicform.models import AnswerFile

        # start = datetime.datetime.now()
        item = None
        answer = None
        _item = AnswerFile.objects.filter(answer_id=answer_id).order_by("-created_at").first()
        if _item:
            answer = _item.answer
            item = _item.info(**kwargs)
        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: get_value check OfflineUploader {diff.total_seconds()}')
        return [item], answer

    @staticmethod
    def get_value_from_type_signature_from_answer_ids(answer_ids, **kwargs):
        from dynamicform.models import AnswerFile

        items = {}
        answer_files = AnswerFile.objects.filter(
            answer_id__in=answer_ids
        ).order_by("answer_id", "-created_at").distinct("answer_id")
        for item in answer_files:
            if item.answer_id not in items:
                items[item.answer_id] = []

            items[item.answer_id].append(item.info(**kwargs))
        return items

    @staticmethod
    def get_value_from_type_utilitybill(answer_id, **kwargs):
        from dynamicform.models import AppliedFormUtilityBill

        # start = datetime.datetime.now()
        items = []
        answer = None
        for item in AppliedFormUtilityBill.objects.filter(answer_id=answer_id).order_by("created_at"):
            items.append(item.info(**kwargs))
            answer = item.answer
        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: get_value check OfflineUploader {diff.total_seconds()}')
        return items, answer

    @staticmethod
    def get_value_from_type_utilitybill_from_answer_ids(answer_ids, **kwargs):
        from dynamicform.models import AppliedFormUtilityBill

        items = {}
        answer_files = (
            AppliedFormUtilityBill.objects.prefetch_related("utility_bill")
            .filter(answer_id__in=answer_ids)
            .order_by("created_at")
        )
        for item in answer_files:
            if item.answer_id not in items:
                items[item.answer_id] = []

            items[item.answer_id].append(item.info(**kwargs))

        return items

    @staticmethod
    def get_value_from_type_smartuploader(answer_id, **kwargs):
        from dynamicform.models import AppliedFormSmartUploader

        # start = datetime.datetime.now()
        items = []
        answer = None
        answer_files = AppliedFormSmartUploader.objects.filter(answer_id=answer_id).order_by("created_at").all()
        for i, item in enumerate(answer_files):
            info = item.info(**kwargs)

            items.append(info)
            answer = item.answer
        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: get_value check OfflineUploader {diff.total_seconds()}')
        return items, answer

    @staticmethod
    def get_value_from_type_smartuploader_from_answer_ids(answer_ids, **kwargs):
        from dynamicform.models import AppliedFormSmartUploader

        items = {}
        answer_files = AppliedFormSmartUploader.objects.filter(answer_id__in=answer_ids).order_by("created_at").all()
        for i, item in enumerate(answer_files):
            info = item.info(**kwargs)
            items[item.answer_id] = info

        return items

    def get_value(self, item={}, **kwargs):
        # start = datetime.datetime.now()
        if item and item.get("type", None) in ["OfflineUploader", "SignaturePad"]:
            items = []
            for _item in self.answerfile_set.order_by("created_at").all():
                items.append(_item.info(**kwargs))
            return items
        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: get_value check OfflineUploader {diff.total_seconds()}')

        # start = datetime.datetime.now()
        if item.get("type", None) in ["UtilityBill"]:
            items = []
            for _item in self.appliedformutilitybill_set.order_by("created_at").all():
                items.append(_item.info(**kwargs))
            return items

        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: get_value check UtilityBill {diff.total_seconds()}')

        return self.value
    
    @staticmethod
    def encrypt_value(appiled_form, value, form=None):
        if form:
            AESGCM_SECRET_KEY = form.secret_key_256
        else:
            AESGCM_SECRET_KEY = appiled_form.form.secret_key_256
        aes_gcm = AESGCM(secret_key=AESGCM_SECRET_KEY)
        value = json.dumps(value)
        return aes_gcm.fix_encrypt(value)
    
    @staticmethod
    def decrypt_value(appiled_form, value, form=None):
        '''
        using in dynamicform answer
        '''
        if form:
            AESGCM_SECRET_KEY = form.secret_key_256
        else:
            AESGCM_SECRET_KEY = appiled_form.form.secret_key_256
        aes_gcm = AESGCM(secret_key=AESGCM_SECRET_KEY)
        return aes_gcm.decrypt(value)

    def get_submit_value(self, **kwargs):
        '''
        webhook use this method
        report_answers use this method
        applied_form_result use this method
        '''
        # from data_point.provider.answer import ANSWER_LIST_STRING_TYPE
        # AppliedFormSmartUploader
        appliedformsmartuploader_set: models.BaseManager[AppliedFormSmartUploader] = self.appliedformsmartuploader_set
        if appliedformsmartuploader_set.exists():
            items = []
            for item in appliedformsmartuploader_set.order_by("created_at").all():
                items.append(item.submit_info(**kwargs))
            return items

        # OfflineUploader
        answerfile_set: models.BaseManager[AnswerFile] = self.answerfile_set
        if answerfile_set.exists():
            items = []
            for item in answerfile_set.order_by("created_at").all():
                items.append(item.submit_info(**kwargs))
            return items

        # UtilityBill
        appliedformutilitybill_set: models.BaseManager[AppliedFormUtilityBill] = self.appliedformutilitybill_set
        if appliedformutilitybill_set.exists():
            items = []
            for item in appliedformutilitybill_set.order_by("created_at").all():
                items.append(item.info(**kwargs))
            return items

        questions_list = kwargs.get('questions', [])
        item_type = ''
        for question in questions_list:
            if self.question == question.get('question'):
                item_type = question.get('component_type')\
        
        # webhook use this method
        value = self.get_decrypt_value() if self.is_encrypt else self.value
        
        return value

    def log_answer(self, action, value=None, **kwargs):
        user = current_user()
        if not user:
            user = None
        if not value:
            value = self.get_value()

        ip_address = client_ip_address()

        return AnswerLog.objects.create(
            applied_form=self.applied_form,
            step=self.step,
            section=self.section,
            question=self.question,
            value=value,
            action=action,
            user=user,
            ip_address=ip_address,
        )
    
    def get_answers_dict(self, *field_name):
        data = model_to_dict(self, fields=field_name if field_name else None)
        if "_value" in data and self.is_encrypt:
            data['_value'] = self.get_decrypt_value()
        else:
            data['_value'] = self.value
        return data

    def get_decrypt_value(self):
        try:
            self._value = json.loads(self._value)
        except Exception:
            pass

        if not self.is_encrypt:
            return self._value
        
        decrypted_value = Answer.decrypt_value(self.applied_form, self._value)
        try:
            # this code important for load to original value
            decrypted_value = json.loads(decrypted_value)
        except Exception:
            pass
        
        return decrypted_value


class AnswerLog(models.Model):
    class Action(models.TextChoices):
        create = "create", _("Create")
        update = "update", _("Update")
        delete = "delete", _("Delete")

    applied_form = models.ForeignKey(
        "AppliedForm",
        on_delete=models.PROTECT,
    )

    step = models.CharField(max_length=191, null=True)
    section = models.CharField(max_length=191, null=True)
    question = models.CharField(max_length=191)
    value = models.TextField(null=True)
    action = models.CharField(max_length=20, choices=Action.choices)

    created_at = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(User, on_delete=models.PROTECT, null=True)
    ip_address = models.CharField(max_length=64, null=True)

    def __str__(self):
        return "User ({}) {} question {} value {} at {} in appliedform {}".format(
            self.user, self.action, self.question, self.value, self.created_at, self.applied_form.slug
        )

    @staticmethod
    def log_bulk(applied_form, logs=[], form=None):
        allow_log = False
        if form:
            allow_log = form.log_answers
        else:
            allow_log = applied_form.form.log_answers

        if not allow_log:
            return

        user = current_user()
        if not user:
            user = None

        ip_address = client_ip_address()

        create_logs = []
        for log in logs:
            create_logs.append(AnswerLog(applied_form=applied_form, user=user, ip_address=ip_address, **log))

        if not create_logs:
            return

        AnswerLog.objects.bulk_create(create_logs)
