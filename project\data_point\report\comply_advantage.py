from data_point.provider.local_thai_pep_list import LocalThaiPepList
from data_point.provider.asia_verify_business import AsiaVerifyBusiness
from .base import DataPointReportBase
from pydash import get
from ..provider.business_aml import BusinessAML
from ..provider.comply_advantage import ComplyAdvantage
from ..provider.amlo import AMLO
from ..provider.ubo import UBO
from ..provider.dbd_business import DBDBusiness
from ..provider.dbd_directorship import DBDDirectorship
from ..provider.dbd_shareholders import DBDShareholders

class ComplyAdvantageReport(DataPointReportBase):
    name = 'comply_advantage'
    data_points = [
        BusinessAML.name,
        ComplyAdvantage.name,
        AMLO.name,
        LocalThaiPepList.name,
        UBO.name,
    ]
    search_term_list = []

    def get_comply_advantage_report(self, **kwargs):
        if not hasattr(self.data_point_result, ComplyAdvantage.name):
            return []
        
        result_value = []
        comply_advantage = getattr(self.data_point_result, ComplyAdvantage.name, {})

        def set_result_value(item_output):
            data = get(item_output, "output.content.data", {})
            if not data:
                return
            
            search_term = get(data, 'search_term')
            if search_term in self.search_term_list:
                return

            self.search_term_list.append(search_term)
            if data != {}:
                result_value.append(data)
        
        if isinstance(comply_advantage, list):
            for item in reversed(comply_advantage):
                for item_output in get(item, 'output.comply_advantage',[]):
                    set_result_value(item_output)
        else:
            data = get(comply_advantage, "content.data", {})
            if data != {}:
                result_value.append(data)
                
        return result_value
    
    def get_business_aml_report(self, **kwargs):
        if not hasattr(self.data_point_result, BusinessAML.name):
            return []
        
        result_value = []
        business_aml = getattr(self.data_point_result, BusinessAML.name, [])

        def set_result_value(item_output):
            data = get(item_output, "output.content.data", {})
            if not data:
                return
            
            search_term = get(data, 'search_term')
            if search_term in self.search_term_list:
                return

            self.search_term_list.append(search_term)
            result_value.append(data)
        
        if isinstance(business_aml, list):
            for item in reversed(business_aml):
                for item_output in get(item, 'output.comply_advantage',[]):
                    set_result_value(item_output)
        else:
            set_result_value(business_aml)

        return result_value

    def get_amlo_report(self, **kwarges):
        if not hasattr(self.data_point_result, AMLO.name):
            return []
        def get_amlo_report_item(amlo_result_item, custom_full_name: str = ""):
            if not custom_full_name:
                input_full_name = get(amlo_result_item, 'input.full_name', {})
                person_screening_path = 'output.amlo_person_screening.output'
                un_list_path = 'output.amlo_person_screening_un_list.output'
            else:
                input_full_name = custom_full_name
                # AML PATH
                person_screening_path = 'amlo_person_screening.output'
                un_list_path = 'amlo_person_screening_un_list.output'

            # default output person_screening_output and un_list_output
            person_screening_output: list | dict = get(amlo_result_item, person_screening_path, [{'list': 'FREEZE-05', 'return_flag': 'N'}, {'list': 'HR-02', 'return_flag': 'N'}, {'list': 'HR-08-RISK', 'return_flag': 'N'}])
            un_list_output = get(amlo_result_item, un_list_path, {'result': [], 'total_record': 0})

            if type(person_screening_output) is dict and "errors" in person_screening_output.keys() :
                return {
                    "search_term": input_full_name,
                    "amlo": [person_screening_output]
            }
            
            # Create flaging for un-list(FREEZE-04)
            if not any(amlo_reason for amlo_reason in person_screening_output 
                   if 'FREEZE-04' in list(amlo_reason.values())):
                un_info = {
                    'list': 'FREEZE-04',
                    'return_flag': 'N'
                }
                # For FREEZE-04 validate by amlo_un_list_response_list -> total_record
                if un_list_output.get('total_record', 0) > 0 :
                    un_info['return_flag'] = 'Y'

                person_screening_output.append(un_info)
            return {
                "search_term": input_full_name,
                "amlo": person_screening_output
            }
        # Get AMLO output and modify for invalid error case
        # than add each AMLO input and output to result_value
        # [{"input": [], "output":[{"input":{}, "output"{}}]}]
        result = []
        amlo_results = getattr(self.data_point_result, AMLO.name, [])
        amlo_search_term_list = []
        for amlo_result in reversed(amlo_results):
            search_term = get(amlo_result, 'input.full_name', {})
            if search_term in amlo_search_term_list:
                continue
            amlo_result_outputs = amlo_result.get('output', [])
            for amlo_result_item in amlo_result_outputs:
                amlo_report_item = get_amlo_report_item(amlo_result_item)
                result.append(amlo_report_item)
            amlo_search_term_list.append(search_term)
        
        # Get AMLO From Business_AML if exists
        business_aml_results = getattr(self.data_point_result, BusinessAML.name, [])
        business_aml_search_term_list = []
        for business_aml_result in business_aml_results:
            search_term = get(business_aml_result, 'input.full_name', [])
            if search_term in business_aml_search_term_list:
                continue
            business_aml_outputs = get(business_aml_result, 'output.amlo', [])
            for business_aml_result_item in business_aml_outputs:
                custom_fullname = get(business_aml_result_item, 'additional_infomation.fullname')
                if any(r for r in result if get(r, 'search_term') == custom_fullname):
                    continue
                amlo_report_item = get_amlo_report_item(business_aml_result_item, custom_full_name=custom_fullname)
                result.append(amlo_report_item)
            business_aml_search_term_list.append(search_term)
        return result

    def get_local_thai_pep_list_report(self, **kwarges):
        search_term_list = []
        
        def get_pep_report_item(pep_result_item, custom_full_name: str = ""):
            if not custom_full_name:
                input_full_name = get(pep_result_item, 'input.full_name', {})
            else:
                input_full_name = custom_full_name
            pep_output = get(pep_result_item, 'output.data')
            if input_full_name in search_term_list:
                return

            search_term_list.append(input_full_name)
            return {
                "search_term": input_full_name,
                "pep": pep_output
            }
        
          
        result = []
        pep_results = getattr(self.data_point_result, LocalThaiPepList.name, [])
        for pep_result in reversed(pep_results):
            pep_result_outputs = get(pep_result, "output.local_thai_pep_list", [])
            for pep_result_item in pep_result_outputs:
                pep_report_item = get_pep_report_item(pep_result_item)
                
                # Validate that pep have infomation before append
                if pep_report_item:
                    result.append(pep_report_item)
        
        # Get local thai pep From Business_AML if exists
        business_aml_results = getattr(self.data_point_result, BusinessAML.name, [])
        for business_aml_result in business_aml_results:
            local_thai_pep_response_list = get(business_aml_result, 'output.local_thai_pep_list', [])
            for local_thai_pep_response in local_thai_pep_response_list:
                custom_fullname = get(local_thai_pep_response, 'additional_infomation.fullname')
                if any(r for r in result if get(r, 'search_term') == custom_fullname):
                    continue
                pep_report_item = get_pep_report_item(local_thai_pep_response, custom_full_name=custom_fullname)
                result.append(pep_report_item)
        return result
        
    def get_report(self, **kwargs):
        self.search_term_list = []

        if not self.data_point_result:
            return []
        
        data_point_result_value = []
        self.search_term_list = []
        
        comply_advantage_report = self.get_comply_advantage_report()
        
        if comply_advantage_report:
            data_point_result_value = data_point_result_value + comply_advantage_report

        business_alm_report = self.get_business_aml_report()
        if business_alm_report:
            data_point_result_value = data_point_result_value + business_alm_report
        
        amlo_report_list = self.get_amlo_report()
        del_index = []
        for report_item in data_point_result_value:
            for index, amlo_report_item in enumerate(amlo_report_list):
                if amlo_report_item.get('search_term') == report_item.get('search_term'):
                    report_item["amlo"] = amlo_report_item.get('amlo')
                    del_index.append(index)
        
        additional_amlo_list = []
        for index, amlo_report_item in enumerate(amlo_report_list):
            if index in del_index:
                continue
            additional_amlo_list.append(amlo_report_item)
            
        data_point_result_value = data_point_result_value + additional_amlo_list
        
        # Add PEP LIST report 
        pep_report_list = self.get_local_thai_pep_list_report()
        del_index = []
        for report_item in data_point_result_value:
            for index, pep_report_item in enumerate(pep_report_list):
                if pep_report_item.get('search_term') == report_item.get('search_term'):
                    report_item["pep"] = pep_report_item.get('pep')
                    del_index.append(index)
        
        additional_pep_list = []
        for index, pep_report_item in enumerate(pep_report_list):
            if index in del_index:
                continue
            additional_pep_list.append(pep_report_item)
            
        data_point_result_value = data_point_result_value + additional_pep_list
        ########
        ubo_aml_report = self.get_ubo_aml_report()
        if ubo_aml_report:
            data_point_result_value = data_point_result_value + ubo_aml_report

        # Add header_info for display label on Frontend
        data_point_result_value = self.add_header_info(data_point_result_value)

        return data_point_result_value
   
    def get_ubo_aml_report(self):
        if not hasattr(self.data_point_result, UBO.name):
            return []
        result_value = []
        def set_result_value(item_output):
            data = get(item_output, "output.content.data", {})
            if not data:
                return
            
            search_term = get(data, 'search_term')
            if search_term in self.search_term_list:
                return

            self.search_term_list.append(search_term)
            if data != {}:
                result_value.append(data)
        
        ubo = getattr(self.data_point_result, UBO.name, {})
        
        for item in ubo:
            for item_output in reversed(get(item, 'output.comply_advantage',[])):
                set_result_value(item_output)
            
                
        return result_value

    def add_header_info(self, data_point_result_value: list[dict]):
        '''
        Add header information for display on the AML tab.

        This function enriches each dictionary in the `data_point_result_value` list 
        by adding a new key called `'header_info'`. The data for this key is constructed 
        from the provider's information.

        The structure of `header_info` is as follows:
            {
                "positions": [...],
                "position_desc": "Position1, Position2 of Company"
            }

        The `position_desc` is generated by concatenating all values in the `positions` list 
        into a readable sentence describing the person's roles within a company.

        Args:
            data_point_result_value(list[dict]) : A list of result entries to be enriched with the `header_info` key.

        Returns:
            None: This function modifies the `data_point_result_value` list in place.
        '''
        
        def sort_data_point_result(item):
            priority = {
                "company": 1,
                "authorized_signatory_director": 2,
                "director": 3,
                "shareholder": 4,
            }
            header = item.get("header_info") or {}
            positions = header.get("positions", [])
            share_amount = header.get("share_amount", 0)

            # Find the lowerest priority in positions
            min_priority = min((priority.get(pos, 99) for pos in positions), default=99)

            # if more then one shareholders use share_amount
            shareholder_count = positions.count("shareholder")
            share_amount_sort = -share_amount if shareholder_count > 1 else 0

            return (min_priority, share_amount_sort)    
        
        for search_term_info in data_point_result_value:
            search_term = search_term_info.get('search_term')
            
            dbd_list = self.get_dbd_result_list()
            asia_verify_list = self.get_asia_verify_result_list()
            positions_detail_list = []
            
            # dbd_result can be find Company, Authorized Signatory Director position, Director, Shareholder 
            for dbd_result in dbd_list:
                company_positions = []
                company_positions_desc = []
                share_amount = 0
                
                # Prepare result to validate
                company_name_result = get(dbd_result, "output.jp_tname", "")
                if not company_name_result:
                    company_name_result = get(dbd_result, "output.raw_data.jd_name", "")
                jdid = get(dbd_result, "output.raw_data.jd_id", "")
               
                # Add position for this search_term,
                # If search_term is Company.
                if company_name_result in search_term:
                    company_positions.append('company')
                    company_positions_desc.append('Company')

                # Add position for this search_term,
                # If search_term is Authorized Signatory Director
                empowerment: dict = self.get_empowerment_by_jdid(jdid)
                if get(empowerment, 'output.is_match', False) and any(
                        True for name in get(empowerment, 'input.combination', []) if name in search_term
                    ):
                    company_positions.append('authorized_signatory_director')
                    company_positions_desc.append('Authorized Signatory Director')
                    
                # Add position for this search_term,
                # If search_term is Director but not Authorized Signatory Director
                else:
                    director_list = get(dbd_result, "output.partner", [])
                    if any(True for director in director_list if director in search_term):
                        company_positions.append('director')
                        company_positions_desc.append('Director')
                
                # Add position for this search_term,
                # If search_term is Shareholder
                shareholder_list = self.get_shareholder_list_by_jdid(jdid)
                for shareholder in shareholder_list:
                    if get(shareholder, "fullname", "").strip()  in search_term:
                        company_positions.append('shareholder')
                        company_positions_desc.insert(0, 'Shareholder')
                        share_amount = get(shareholder, "pct_share")
                
                # Add position detail if found any of position in this company
                if company_positions:
                    positions_detail_list.append({
                        "jdid": jdid,
                        "company_name": company_name_result,
                        "positions": company_positions,
                        "positions_desc": company_positions_desc,
                        "share_amount": share_amount
                    })
            
            # Add position for this search_term,
            # If search_term is UBO
            if hasattr(self.data_point_result, UBO.name):
                ubo_provider_result_list = getattr(self.data_point_result, UBO.name, [])
                for ubo_provider_result in ubo_provider_result_list:
                    ubo_service_result = get(ubo_provider_result, 'output.ubo', [])
                    if ubo_service_result:
                        ubo_first_result = ubo_service_result[0]
                        ubo_jdid = get(ubo_first_result, "input.business_id", "")
                        ubo_company_name = get(ubo_first_result, "output.data.name", "")
                        ubo_natural_person = get(ubo_first_result, f'output.data.natural_person', {})
                        ubo_info = None
                        for person, detail in ubo_natural_person.items():
                            if person in search_term:
                                ubo_info = get(ubo_first_result, f'output.data.natural_person.{person}', {})
                        if ubo_info:
                            ubo_share_amount = get(ubo_info, "total_benificial_share_percentage", 0)
                            # Add ubo position in current company
                            # Or Add new compary with ubo position
                            for person in positions_detail_list:
                                print(person)
                                if person.get("jdid") == ubo_jdid:
                                    person['positions'].append("ubo")
                                    person['positions_desc'].append("Ultimate Beneficial Owner")
                                    person['share_amount'] = ubo_share_amount
                                    break
                            else:
                                positions_detail_list.append({
                                    "jdid": ubo_jdid,
                                    "company_name": ubo_company_name,
                                    "positions": ['ubo'],
                                    "positions_desc": ['Ultimate Beneficial Owner'],
                                    "share_amount": ubo_share_amount
                                })
                
            # Asia Verify 
            # Asia Verify  can be find Company,  Director, Shareholder 
            for asia_verify_result in asia_verify_list:
                company_positions = []
                company_positions_desc = []
                share_amount = None
                
                # Prepare result to validate
                country: str = get(asia_verify_result, "input.country")
                asia_verify_jdid: str = get(asia_verify_result, "input.input")
                asia_verify_v2_country = ['PHL', 'AUS']
                if any(True for v2_country in asia_verify_v2_country if country == v2_country):
                    company_info: list = get(asia_verify_result, "output.result.CompanyInformation.data")
                    director_info: list = get(asia_verify_result, "output.result.majorPerson.data")
                    shareholder_info: list = get(asia_verify_result, "output.result.Shareholders.data")
                else:    
                    company_info: list = get(asia_verify_result, "output.result.data.CompanyRegistrationInformation.data")
                    director_info: list = get(asia_verify_result, "output.result.data.majorPerson.data")
                    shareholder_info: list = get(asia_verify_result, "output.result.data.Shareholders.data")
                    
                company_name: str = self.get_company_by_country(company_info, country)    
               
                # Add position for this search_term,
                # If search_term is Company.
                if search_term in company_name:
                    company_positions.append('company')
                    company_positions_desc.append('Company')
                    
                # Add position for this search_term,
                # If search_term is Director
                is_director =  self.find_is_director_in_asia_verify(search_term, director_info, country)
                if is_director:
                    company_positions.append('director')
                    company_positions_desc.append('Director')
                
                # Add position for this search_term,
                # If search_term is Shareholder
                is_shareholder, share_amount =  self.find_is_shareholder_in_asia_verify(search_term, shareholder_info, country)
                if is_shareholder:
                    company_positions.append('shareholder')
                    company_positions_desc.append('Shareholder')
                
                # Add position detail if found any of position in this company
                if company_positions:
                    positions_detail_list.append({
                        "jdid": asia_verify_jdid,
                        "company_name": company_name,
                        "positions": company_positions,
                        "positions_desc": company_positions_desc,
                        "share_amount": share_amount
                    })
            
            # Build `header_info`
            if positions_detail_list:
                # Initial variable for build `header_info``
                position_result = []
                positions_desc_result = []
                share_amount_result = 0.0
                
                # Marge positions_detail_list each value into one value
                for detail in positions_detail_list:
                    # Gather position into one list
                    positions = get(detail, "positions", [])
                    position_result.extend(positions)
                    
                    # Add position description into positions_desc_result list
                    positions_desc = get(detail, 'positions_desc', [])
                    company_name: str = get(detail, 'company_name', "")
                    
                    # Display in Frontend as Company if it only company found
                    if positions_desc == ['Company'] and company_name == search_term:
                        positions_desc_result.append(positions_desc[0])
                    # Display in Frontend position under company
                    else:
                        positions_desc_result.append(f"{' and '.join(positions_desc)} of {company_name}")
                        
                        # Get Share amount that is highest pct
                        share_amount: float = get(detail, 'share_amount') or  0
                        if share_amount > (share_amount_result or 0):
                            share_amount_result = share_amount
                
                # Create header_info
                search_term_info['header_info'] = {
                    "positions": position_result,
                    "position_desc": f"{', '.join(positions_desc_result)}",
                }
                search_term_info['header_info']["share_amount"] = share_amount_result  
                
        # Sort search term by position and share_amount
        data_point_result_value = sorted(data_point_result_value, key=sort_data_point_result)
        return data_point_result_value
    def get_dbd_result_list(self) -> list:
        dbd_list = []
        
        def add_dbd_result_list_by_provider_name(provider_name: str):
            if hasattr(self.data_point_result, provider_name):
                provider_result = getattr(self.data_point_result, provider_name, [])
                if provider_result:
                    for provider_first_result in provider_result:
                        # Exclude duplicate dbd result orElse, append to dbd_list
                        dbd_result = get(provider_first_result, "output.dbd[0]", {})
                        jdid_input = get(dbd_result, "input.jdid", "")
                        if jdid_input and not any(True for dbd in dbd_list if get(dbd, "input.jdid", "") == jdid_input):
                            dbd_list.append(dbd_result)
                    
        add_dbd_result_list_by_provider_name(BusinessAML.name)
        add_dbd_result_list_by_provider_name(DBDBusiness.name)
        add_dbd_result_list_by_provider_name(DBDDirectorship.name)    
        add_dbd_result_list_by_provider_name(DBDShareholders.name)  
        return dbd_list

    def get_asia_verify_result_list(self) -> list:
        asia_verify_list = []
        
        def add_asia_verify_result_list_by_provider_name(provider_name: str):
            if hasattr(self.data_point_result, provider_name):
                provider_result = getattr(self.data_point_result, provider_name, [])
                if provider_result:
                    for provider_first_result in provider_result:
                        # Exclude duplicate asia_verify result orElse, append to dbd_list
                        asia_verify_result = get(provider_first_result, "output.asia_verify[0]", {})
                        jdid_input = get(asia_verify_result, "input.input", "")
                        if jdid_input and not any(True for asia_verify in asia_verify_list if get(asia_verify, "input.input", "") == jdid_input):
                            asia_verify_list.append(asia_verify_result)
                    
        add_asia_verify_result_list_by_provider_name(BusinessAML.name)
        add_asia_verify_result_list_by_provider_name(AsiaVerifyBusiness.name)
        return asia_verify_list

    def get_shareholder_list_by_jdid(self, jdid: str) -> list:
        for provider_name in [BusinessAML.name, DBDShareholders.name]:
            if hasattr(self.data_point_result, provider_name):
                provider_result = getattr(self.data_point_result, provider_name, [])
                provider_first_result = provider_result[0] if provider_result else {}
                
                # Return shareholder list if correct jdid
                shareholder_list = get(provider_first_result, "output.creden[0].output.list_shareholder", [])
                creden_jdid = get(provider_first_result, "output.creden[0].output.company_id", "")
                if creden_jdid and creden_jdid == jdid:
                    return shareholder_list
        return []

    def get_empowerment_by_jdid(self, jdid: str) -> dict:
        for provider_name in [DBDDirectorship.name]:
            if hasattr(self.data_point_result, provider_name):
                provider_result = getattr(self.data_point_result, provider_name, [])
                provider_first_result = provider_result[0] if provider_result else {}
                
                # Return empowerment if correct jdid
                empowerment: dict = get(provider_first_result, "output.empowerment[0]", {})
                jp_no = get(provider_first_result, "output.empowerment[0].input.jp_no", "")
                if jp_no and jp_no == jdid:
                    return empowerment
        return {}

    def get_company_by_country(self, company_info_list: list[dict] | dict,
            country: str):
        company_paths = None
        if country == "MYS" or country == "SGP":
            company_info_list = company_info_list[0]
            company_paths = ["companyName"]
        elif country == "JPN":
            company_info_list = company_info_list[0]
            company_paths = ["enName", "ogName"]
        # Alreadys dict value in company_info_list
        elif country == "HKG":
            company_paths =  ["enName", "ogName"]
        elif country == "PHL":
            company_info_list = company_info_list[0]
            company_paths = ['name.companyName']
        elif country == "AUS":
            company_info_list = company_info_list[0]
            company_paths = ['name.companyName']
        for path in company_paths:
            company = get(company_info_list, path)
            if company:
                return company
            
    def find_is_director_in_asia_verify(
            self, 
            search_term: str, 
            directors_info_list: list[dict] | dict,
            country: str
        ):
        director_paths = None
        position_validate = None
        if country == "MYS" or country == "SGP":
            director_paths = ["name"]
            position_validate = lambda info: str(get(info, 'position', "")).upper() == "DIRECTOR"
        elif country == "JPN":
            director_paths = ["rdPersonnel"]
            position_validate = lambda info: "director" in str(get(info, 'rdPersonnel', "")).lower()
        elif country == "HKG":
            directors_info_list = get(directors_info_list, 'details', [])
            director_paths = ["ogFullName", "enFullName"]
            position_validate =  lambda info: str(get(info, 'position', "")).upper() == "DIRECTOR"
        elif country == "PHL":
            director_paths = ["enName", "ogName"]
            position_validate =  lambda info: str(get(info, 'role', "")).upper() == "DIRECTOR"
        elif country == "AUS":
            director_paths = ["enName"]
            position_validate =  lambda info: str(get(info, 'role', "")).upper() == "DIRECTOR"
        
        if director_paths and position_validate:
            for director_info in directors_info_list:
                name_list = [get(director_info, path) for path in director_paths]
                if position_validate(director_info) and any(True for name in name_list if search_term in name):
                    return True
        return False
    
    def find_is_shareholder_in_asia_verify(
            self, 
            search_term: str, 
            shareholder_info_list: list[dict],
            country: str
        ) -> tuple[bool, float|None]:
        shareholder_paths = None
        percent_share_path = None
        if country == "MYS" :
            shareholder_paths = ["stockName"]
            percent_share_path = "shareNumber"
        if country == "SGP":
            shareholder_info_list = get(shareholder_info_list, 'individual', [])
            shareholder_paths = ["name"]
            percent_share_path = "shareAmount"
        elif country == "JPN":
            shareholder_paths = ["shareholderName"]
            percent_share_path = "proportion"
        elif country == "HKG":
            shareholder_info_list = get(shareholder_info_list, 'details', [])
            shareholder_paths = ["stockName"]
            percent_share_path = "percent"
        elif country == "PHL":
            shareholder_info_list = get(shareholder_info_list, 'individual', [])
            shareholder_paths = ["shareholderNameEN", "shareholderNameOG"]
            percent_share_path = "percentageOwned"
        elif country == "AUS":
            shareholder_info_list = get(shareholder_info_list, 'individual', [])
            shareholder_paths = ["shareholderNameEN"]
            percent_share_path = "percentageOwned"
        
        if shareholder_paths:
            for shareholder_info in shareholder_info_list:
                name_list = [get(shareholder_info, path) for path in shareholder_info]
                if search_term in name_list:
                    percent_share_str = str(get(shareholder_info, percent_share_path)).replace("%", "")
                    percent_share = round(float(percent_share_str), 2)
                    return True, percent_share
        return False, None

    
