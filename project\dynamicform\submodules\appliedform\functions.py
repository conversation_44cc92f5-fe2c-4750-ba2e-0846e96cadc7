import datetime
import json
import logging
import uuid
import os
from wsgiref.util import is_hop_by_hop

import requests
from crequest.middleware import CrequestMiddleware
from django.apps import apps
from django.conf import settings
from django.db import models
from django.http import HttpResponse
from django.utils import timezone
from django.utils.translation import get_language
from pydash import merge, objects, set_
from rest_framework.exceptions import NotFound, PermissionDenied, ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.status import is_client_error, is_informational, is_redirect, is_success
from dynamicform.exceptions import ActionAPIException, AppliedformSessionExpired, FormIsInactive
from dynamicform.hook import EKYC_STATUS_PASS
from dynamicform.schema import BuildSchema, GetApp
from dynamicform.schema.actions import do_action
from dynamicform.schema.trans import collect_trans
from dynamicform.submodules.appliedform.auto_compute_value import AppliedFormAutoCompute
from dynamicform.submodules.answer.models import Answer
from dynamicform.submodules.application.models import Application
from dynamicform.submodules.appliedform.google_task import create_task
from dynamicform.submodules.form.functions import TRIGGER_EVENT_SUBMIT_FORM
from dynamicform.submodules.form.helpers.permissions import (
    IsWorkspaceAdmin,
    IsWorkspaceAdminOrEditor,
    IsWorkspaceMember,
)
from dynamicform.util import *
from user_agents import parse

logger: logging.Logger = logging.getLogger(__name__)

class AppliedFormFunction(models.Model, AppliedFormAutoCompute):
    class Meta:
        abstract = True

    def save_answers(self, answers, invalid=[], save_disable=False, **kwargs):
        backend_schema = kwargs.pop("backend_schema", self.backend_schema)
        disabled_save = backend_schema.get("disabled_save", [])
        releted_questions = backend_schema.get("releted_questions", {})
        auto_compute_value = backend_schema.get("auto_compute_value", {})

        items = backend_schema["items"]
        app_answers = {}
        updated_answers = self.answers.copy()

        for step, sections in answers.items():
            for section, section_answers in sections.items():
                if section_answers:
                    self.log(step=step, section=section, action=self.ACTION_SAVE)
                
                for question, answer in section_answers.items():
                    # Item ####################################
                    if question in invalid:
                        continue
                    if question not in items:
                        continue
                    if question in disabled_save:
                        continue

                    item = items.get(question)
                    if item.get("props", {}).get("disable", False) and save_disable == False:
                        continue
                    if item.get("props", {}).get("disabled", False) and save_disable == False:
                        continue
                    
                    item_type = item.get('type', '')
                    if item_type not in app_answers:
                        app_answers[item_type] = {}
                    
                    app_answers[item_type].update({
                        question: {'value':answer, 'item':item}
                    })
                    updated_answers.update({question: answer})
                    self.collect_auto_compute_questions(
                        question,
                        app_answers,
                        releted_questions,
                        auto_compute_value,
                        items
                    )
        
        self.app_save_answers(app_answers, save_disable=save_disable, updated_answers=updated_answers)
        self.application_updated_now()

    def app_save_answers(self, app_answers, **kwargs):
        for app_type, answers in app_answers.items():
            item_app = GetApp.get_item_app_from_type(app_type)
            if hasattr(item_app, "auto_compute_value"):
                item_app.auto_compute_value(
                    applied_form=self, answers=answers, **kwargs
                )

            if hasattr(item_app, "save_answers"):
                item_app.save_answers(
                    applied_form=self, answers=answers, **kwargs
                )
            elif hasattr(item_app, "save"):
                for question, item in answers.items():
                    answer = objects.get(item, 'value', None)
                    step = objects.get(item, 'item.step', None)
                    section = objects.get(item, 'item.section', None)
                    item_app.save(
                        applied_form=self, step=step, section=section, question=question, answer=answer, **kwargs
                    )


    def save_answer(self, step, section, question, answer, invalid=[], save_disable=False, item={}, **kwargs):
        if question in invalid:
            return

        if item.get("props", {}).get("disable", False) and save_disable == False:
            return
        if item.get("props", {}).get("disabled", False) and save_disable == False:
            return

        item_app = GetApp.get_item_app(item)
        if hasattr(item_app, "save"):
            item = item_app.save(
                applied_form=self, step=step, section=section, question=question, answer=answer, **kwargs
            )
        
        backend_schema = self.backend_schema
        items = backend_schema.get("items")
        releted_questions = backend_schema.get("releted_questions", {})
        auto_compute_value = backend_schema.get("auto_compute_value", {})
        app_answers = {}

        self.collect_auto_compute_questions(
            question,
            app_answers,
            releted_questions,
            auto_compute_value,
            items
        )

        if app_answers:
            updated_answers = self.answers.copy()
            updated_answers.update({question: answer})
            self.app_save_answers(app_answers, save_disable=save_disable, updated_answers=updated_answers)

        return item

    def get_apps_questions(self, items):
        apps_questions = {}
        apps_dynamicform = {}
        for question, item in items.items():
            item_type = item.get('type', '')
            app_name, component_type = GetApp.split_type(item_type)
            app_name, dynamicform_app = GetApp.get_app_name(app_name)
            question_item = {"question": question, "component_type": component_type}

            if app_name not in apps_dynamicform:
                apps_dynamicform.update({app_name: dynamicform_app})

            if app_name not in apps_questions:
                apps_questions.update({app_name: [question_item]})
            else:
                apps_questions[app_name].append(question_item)

        return apps_questions, apps_dynamicform

    def get_report(self, **kwargs):
        return self.get_answers(call_function="get_report", **kwargs)

    def get_extra(self, **kwargs):
        extra_answer = self.get_answers(call_function="get_extra", **kwargs)
        extra_data_point = self.application.get_extra_data_point()
        return {
            **extra_answer,
            **extra_data_point
        }

    def get_dynamicform_app_answers(self, query={}, exclude_setting_path=None, **kwargs):
        answers = {}
        query_answers = query.get("answers", False)
        if query_answers:
            questions = query_answers.split(",")
            answers = self.get_dynamicform_app_answers_by_questions(
                questions, exclude_setting_path=exclude_setting_path
            )
        return answers

    def get_dynamicform_app_answers_by_questions(
        self, questions, app_props_data=[], all_questions=False, exclude_setting_path=None, **kwargs
    ):
        if all_questions:
            _answers = self.answer_set.all()
        else:
            _answers = self.answer_set.filter(question__in=questions)

        exclude = self.get_form_settings(path="export.csv.exclude", default=[])
        _answers = _answers.exclude(question__in=exclude)

        exclude = []
        if exclude_setting_path:
            exclude = self.get_form_settings(path=exclude_setting_path, default=[])
            _answers = _answers.exclude(question__in=exclude)

        answers = {}
        for item in _answers:
            answers[item.question] = item.decrypted_value

        for app_props in app_props_data:
            try:
                app_name, properties = app_props.split(":")
                app_props_value = self.get_app_data(app_name, properties)
                if app_props_value:
                    answers[app_props] = app_props_value
            except Exception as e:
                logger.warning(f"get_dynamicform_app_answers_by_questions {e}")
        return answers

    def apps_questions(self, backend_schema=None, **kwargs):
        if not backend_schema:
            backend_schema = self.form.backend_schema

        items = backend_schema.get("items", {})

        filter_questions = kwargs.get("filter_questions", [])
        if len(filter_questions) > 0:
            _items = {}
            for question in filter_questions:
                if question in items:
                    _items[question] = items[question]
            items = _items

        return self.get_apps_questions(items)

    def get_answers(self, call_function="get_answers", backend_schema=None, look_up_extra_answers=True, **kwargs):
        # start = datetime.datetime.now()
        apps_questions, apps_dynamicform = self.apps_questions(**kwargs)
        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: apps_questions {diff.total_seconds()}')
        answers = {}

        logger.info(f'get_answers 1 apps_dynamicform {str(apps_dynamicform)}')
        # dynamicform first
        dynamicform_app_name = "dynamicform"
        dynamicform_app = apps_dynamicform.pop(dynamicform_app_name, None)
        if dynamicform_app and hasattr(dynamicform_app, call_function):
            dynamicform_app_get_answers = getattr(dynamicform_app, call_function)
            dynamicform_answers = dynamicform_app_get_answers(self,
                questions=apps_questions[dynamicform_app_name],
                **kwargs
            )
            answers.update(dynamicform_answers)
        
        # start = datetime.datetime.now()
        for app_name, app_dynamicform in apps_dynamicform.items():
            if not hasattr(app_dynamicform, call_function):
                continue
            app_get_answers = getattr(app_dynamicform, call_function)
            _answers = app_get_answers(self, questions=apps_questions[app_name], **kwargs)
            answers.update(_answers)

        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: app_get_answers {diff.total_seconds()}')

        # start = datetime.datetime.now()
        if look_up_extra_answers:
            extra_answers = self.get_extra_answers(
                call_function=call_function, backend_schema=backend_schema, **kwargs
            )
            answers.update(extra_answers)
        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: get_extra_answers {diff.total_seconds()}')

        return answers

    def get_extra_answers(self, call_function="get_answers", backend_schema=None, **kwargs):
        extra_answers = {}
        application =  self.application

        if not application:
            return extra_answers
        
        if not backend_schema:
            backend_schema = self.form.backend_schema

        # Find in main #################
        extra_answers_main_form = backend_schema.get("extra_answers_main_form", {})
        if bool(extra_answers_main_form.get("map", {})):
            first_applicationappliedform_applied_form = application.applicationappliedform_set.order_by(
                "created_at"
            ).first()
            if first_applicationappliedform_applied_form:
                first_applied_form = first_applicationappliedform_applied_form.applied_form
                if bool(extra_answers_main_form.get("questions", [])):
                    answers = Answer.objects.filter(
                        applied_form__id=first_applied_form.id,
                        question__in=extra_answers_main_form.get("questions", []),
                    )
                    for answer in answers:
                        if answer.question in extra_answers_main_form["map"]:
                            extra_answers.update(
                                {
                                    extra_answers_main_form["map"][answer.question]: answer.get_value(
                                        report=(call_function == "get_report"), **kwargs
                                    )
                                }
                            )
                if bool(extra_answers_main_form.get("properties", [])):
                    for prop in extra_answers_main_form.get("properties", []):
                        if hasattr(first_applied_form, prop):
                            extra_answers.update(
                                {extra_answers_main_form["map"][prop]: getattr(first_applied_form, prop)}
                            )

        # Find in specific #################
        for form, lookup in backend_schema["extra_answers"].items():
            extra_applied_form = application.applicationappliedform_set.filter(applied_form__form__slug=form).first()
            if not extra_applied_form:
                continue
            extra_applied_form = extra_applied_form.applied_form
            answers = extra_applied_form.get_answers(
                backend_schema=backend_schema, look_up_extra_answers=False, call_function=call_function, **kwargs
            )

            properties = lookup["properties"]
            for prop in properties:
                if hasattr(extra_applied_form, prop):
                    answers.update({prop: getattr(extra_applied_form, prop)})

            for key, value in lookup["map"].items():
                if key not in answers:
                    continue
                extra_answers.update({value: answers[key]})

        return extra_answers

    def get_submit_answers(self, **kwargs):
        backend_schema = self.form.backend_schema

        items = backend_schema["items"]
        apps_questions, apps_dynamicform = self.get_apps_questions(items)

        app_list = backend_schema["app_list"]

        app_name_list = {}
        for app_type in app_list["steps"]:
            app_name, app_dynamicform = GetApp.get_app_name(app_type.lower())
            app_name_list.update({app_name: app_dynamicform})

        app_name_answers = {}
        for app_name, app_dynamicform in app_name_list.items():
            if not hasattr(app_dynamicform, "get_submit_answers"):
                continue

            _apps_questions = []
            if app_name in apps_questions:
                _apps_questions = apps_questions[app_name]
            get_answers = getattr(app_dynamicform, "get_submit_answers")
            _answers = get_answers(self, questions=_apps_questions, **kwargs)
            app_name_answers.update({app_name: _answers})

        return app_name_answers

    def flat_questions(self, answers):
        flat_questions = {}
        try:
            for step_key, sections in answers.items():
                for section_key, questions in sections.items():
                    for question, value in questions.items():
                        flat_questions[question] = value
        except:
            flat_questions = {}
        return flat_questions

    def validate(self,
            flat_questions,
            validate_all=False,
            backend_schema={},
            labels=[],
            *args,
            **kwargs
        ):
        is_valid = True
        errors = {}
        if not backend_schema:
            backend_schema = self.form.backend_schema
        steps_backend = backend_schema["steps"]
        visible_items_type = backend_schema.get("visible_items_type", {})
        app_validator = {}

        if validate_all:
            answered_questions = self.get_answers()
            flat_questions = {**answered_questions, **flat_questions}

        for _, info in steps_backend.items():
            # to collect validator
            validate_app = GetApp.get_step_app_validator(info)

            module_name = validate_app.__class__.__module__
            if module_name not in app_validator:
                app_validator.update({module_name: validate_app})

        for _, step_app in app_validator.items():
            if not hasattr(step_app, "validate"):
                continue

            _is_valid, error = step_app.validate(self,
                flat_questions,
                validate_all=validate_all,
                backend_schema=backend_schema,
                labels=labels,
                **kwargs
            )

            if _is_valid:
                continue

            is_valid = False
            errors.update(error)
        
        for item_type, items in visible_items_type.items():
            validate_app = GetApp.get_item_app_from_type(item_type)
            if not hasattr(validate_app, "validate"):
                continue

            _is_valid, error = validate_app.validate(self,
                flat_questions,
                items=items,
                validate_all=validate_all,
                labels=labels
            )
            if _is_valid:
                continue
            is_valid = False
            errors.update(error)

        return is_valid, errors

    def submit(
        self, log_all_sections=True, notify=True, request_hook_submit=True, submit_action=True, *args, **kwargs
    ):
        self.submitted_at = timezone.now()
        self.save()
        # set application status
        self.application_submitted()
        if log_all_sections:
            self.log_all_sections()
        if request_hook_submit:
            self.request_hook_submit()

        if not submit_action:
            return {}

        # eg. DYNAMICFORM_SUBMIT_ACTION = eucaapp.apps.application.functions.submit
        DYNAMICFORM_SUBMIT_ACTION = getattr(settings, "DYNAMICFORM_SUBMIT_ACTION", False)
        if not DYNAMICFORM_SUBMIT_ACTION:
            self.submit_to_cloud()
            return {}
        else:
            submit_function = import_callable(DYNAMICFORM_SUBMIT_ACTION)
            return submit_function(self, *args, **kwargs)

    def log_all_sections(self):
        steps = self.form.frontend_schema["steps"]
        for step_key, step in steps.items():
            if "sections" not in step:
                continue
            for section_key, section in step["sections"].items():
                self.save_log(step_key, section_key, action=self.ACTION_AUTO_LOG_ALL_SECTION, duplicate=False)

    def save_upmapped_answers(self, answers):
        form = self.form
        mapping_init_answers = form.backend_schema.get("mapping_init_answers", {})
        if bool(mapping_init_answers.get("data", {})):
            mapping_answers = objects_get_mapping_value(answers, mapping_init_answers.get("data", {}))
            self.init_answers(answers=mapping_answers)
        else:
            self.init_answers(answers=answers)

        keep_answers = form.backend_schema.get("keep_answers", {})
        if bool(keep_answers):
            keep_answers_result = objects_get_mapping_value(answers, keep_answers)
            self.init_answers(answers=keep_answers_result, keep_answers=True)

    def init_answers(self, answers=None, keep_answers=False, invalid=[], save_disable=False, *args, **kwargs):
        if not answers:
            return

        app_answers = {}

        backend_schema = self.backend_schema
        items = backend_schema.get("items")
        releted_questions = backend_schema.get("releted_questions", {})
        auto_compute_value = backend_schema.get("auto_compute_value", {})
        updated_answers = self.answers.copy()

        for question, answer in answers.items():
            if question in invalid:
                continue
            if question not in items and keep_answers:
                continue
                    
            item = items.get(question)
            if not item:
                continue
            
            item_type = item.get('type', '')
            if item_type not in app_answers:
                app_answers[item_type] = {}
            
            app_answers[item_type].update({
                question:{'value':answer, 'item':item}
            })
            updated_answers.update({
                question: answer
            })
            self.collect_auto_compute_questions(
                question,
                app_answers,
                releted_questions,
                auto_compute_value,
                items
            )

        self.app_save_answers(app_answers, save_disable=True, updated_answers=updated_answers)

    def created_info(self):
        return {
            "info": self.info_url,
            "detail": {
                "form": self.form.slug,
                "applied_form": self.slug,
                "step": self.get_step(),
                "section": self.get_section(),
                "submitted_at": self.submitted_at,
            },
            "form_url": self.secured_form_url,
        }

    def get_frontend_schema(self, *args, **kwargs):
        frontend_schema = self.form.trans_frontend_schema
        frontend_schema["steps"] = BuildSchema.build_step(frontend_schema["steps"], self, **kwargs)
        frontend_schema["steps"] = self.build_nav(frontend_schema["steps"])
        return frontend_schema

    def get_report_schema(self, *args, **kwargs):
        frontend_schema = self.form.trans_frontend_schema
        report_schema = self.form.trans_report_schema
        report_schema = merge(frontend_schema, report_schema)
        report_schema["steps"] = BuildSchema.build_step(report_schema["steps"], self, **kwargs)
        report_schema["steps"] = self.build_nav(report_schema["steps"])
        return report_schema

    def build_nav(self, steps):
        prev_step = None
        prev_section = None
        for step_name, step in steps.items():
            for section_name, section in step["sections"].items():
                if "nav" not in section:
                    section["nav"] = {"previous": {}, "next": {}}
                section["nav"]["previous"] = {}

                if prev_step and prev_section:
                    section["nav"]["previous"]["step"] = prev_step
                    section["nav"]["previous"]["section"] = prev_section
                    steps[prev_step]["sections"][prev_section]["nav"]["next"]["step"] = step_name
                    steps[prev_step]["sections"][prev_section]["nav"]["next"]["section"] = section_name
                else:
                    section["nav"]["previous"] = False

                prev_step = step_name
                prev_section = section_name
        return steps

    def submit_to_cloud(self, **kwargs):
        logger.info(f"Time start submit_to_cloud form {self.form.slug}, appliedform {self.slug}")
        dynamicform_submit_url = getattr(settings, "DYNAMICFORM_SUBMIT_URL", False)
        headers = {}
        if dynamicform_submit_url:
            submit_url = dynamicform_submit_url
            logger.info("Submit to cloud DYNAMICFORM_SUBMIT_URL {}".format(submit_url))
            self.do_submit_to_cloud(submit_url, headers=headers)

        forms_microservice = getattr(settings, "FORMS_MICROSERVICE", {})
        dynamicform_ms_host = getattr(settings, "DYNAMICFORM_MS_HOST", False)
        dynamicform_ms_submit_api = getattr(settings, "DYNAMICFORM_MS_SUBMIT_API", False)
        submit_url = None
        ms_app_id = None
        ms_app_secrect = None
        if self.form.slug in forms_microservice:
            logger.info(f"form slug {self.form.slug} is in FORMS_MICROSERVICE")
            # submit to cloud belong to the form
            form_slug = self.form.slug
            microservice = forms_microservice.get(form_slug, {})
            host = microservice.get("host", None)
            submit_api = microservice.get("submit_api", None)
            app_id = microservice.get("app_id", None)
            app_secrect = microservice.get("app_secrect", None)

            if host and submit_api:
                submit_url = host + submit_api

            # eg. FORMS_MICROSERVICE_credit-loan_app_id
            ms_app_id = os.environ.get(
                f"FORMS_MICROSERVICE_{form_slug}_app_id", app_id
            )
            # eg. FORMS_MICROSERVICE_credit-loan_app_secrect
            ms_app_secrect = os.environ.get(
                f"FORMS_MICROSERVICE_{form_slug}_app_secrect",
                app_secrect,
            )

        elif dynamicform_ms_host and dynamicform_ms_submit_api:
            # submit to cloud default
            submit_url = dynamicform_ms_host + dynamicform_ms_submit_api
            ms_app_id = getattr(settings, "DYNAMICFORM_MS_APP_ID", False)
            ms_app_secrect = getattr(settings, "DYNAMICFORM_MS_APP_SECRECT", False)

        if submit_url and ms_app_id and ms_app_secrect:
            headers = {"Authorization": requests.auth._basic_auth_str(ms_app_id, ms_app_secrect)}
            logger.info("Submit to cloud DYNAMICFORM_MS_SUBMIT_API {}".format(submit_url))

            self.do_submit_to_cloud(submit_url, headers=headers)

    def get_uuid(self):
        try:
            request = CrequestMiddleware.get_request()
            _uuid = request.COOKIES.get("uuid", None)
            if _uuid:
                return _uuid

        except Exception:
            pass

        return str(uuid.uuid4())

    def do_submit_to_cloud(self, submit_url, headers={}, **kwargs):
        data = {
            "applied_form": {
                "id": self.id,
                "slug": self.slug,
                "submitted_at": str(self.submitted_at),
                "no": self.no_format,
            },
            "app_id": getattr(settings, "APP_NAME", ""),
            "app_env": getattr(settings, "APP_ENV", ""),
            "form_slug": self.form.slug,
            "applied_form_slug": self.slug,
            "answers": self.get_submit_answers(),
            "extra": self.get_extra(),
        }
        # Submit to google cloud task
        queue = self.get_form_settings(path="queue", default={})

        _uuid = self.get_uuid()

        submit_url = f"{submit_url}?ref_1={self.slug}&ref_2={_uuid}"
        logger.info(f"create_task_to_google_cloud do_submit_to_cloud {self.slug} {submit_url}", data)
        create_task(submit_url, data, headers, queue=queue)

    def is_submitted(self):
        return self.submitted_at is not None

    def is_disabled(self):
        return self.disabled_at is not None

    def is_drop_off(self, raise_exception=True):
        if self.drop_off_at is not None:
            if raise_exception:
                raise NotFound()
            else:
                return False

        return True 
        

    def can_update(self):
        if self.submitted_at is None:
            return True
        if self.form.can_update_after_submit:
            return True
        return False

    def get_step(self):
        if self.step:
            return self.step

        first_step = self.form.backend_schema.get("first_step_section", [])
        if first_step and first_step[0]:
            return first_step[0]

        return self.first_step[0]

    def get_section(self):
        if self.section:
            return self.section

        first_section = self.form.backend_schema.get("first_step_section", [])
        if first_section and first_section[1]:
            return first_section[1]

        return self.first_section[0]

    def is_session_active(self, raise_exception=True):
        result = get_session(self.session_key)
        if not result and raise_exception:
            raise AppliedformSessionExpired()
        return result

    def set_can_view_info(self):
        set_session(self.session_key, True)
        if len(self.form.backend_schema.get("extra_answers_main_form", {}).get("questions", [])) > 0:
            set_session(self.main_applied_form.session_key, True)

    def set_can_view_report(self):
        self.set_can_view_info()

    def is_my_form(self):
        return self.form.is_my_form()

    def can_view_info(self):
        if not self.form.is_active:
            raise FormIsInactive()

        self.is_drop_off(raise_exception=True)

        if self.is_session_active(raise_exception=False):
            return True

        self.is_valid_url_token()

        if self.is_my_form():
            return True

        if self.is_submitted():
            return False

        if self.is_disabled():
            return False

        return True

    def can_update_info(self):
        if self.is_my_form():
        # if self.is_my_form() and self.form.can_update_after_submit:
            return True
        
        if not self.form.is_active:
            raise FormIsInactive()

        if self.is_submitted():
            return False

        if self.is_disabled():
            return False

        self.is_drop_off(raise_exception=True)
        return self.is_session_active()

    def can_do_update(self):
        if self.is_my_form():
        # if self.is_my_form() and self.form.can_update_after_submit:
            return True
        
        if not self.form.is_active:
            raise FormIsInactive()

        if self.is_submitted():
            return False

        if self.is_disabled():
            return False

        self.is_drop_off(raise_exception=True)
        return True

    def can_view_file(self):
        if self.is_my_form():
            return True

        if not self.form.is_active:
            raise FormIsInactive()

        return self.is_session_active()

    def can_view_report(self):
        return self.is_my_form()

    def can_view_page(self):
        if not self.is_submitted():
            return True

        timeout = get_setting("DYNAMICFORM_PAGE_TIMEOUT")
        if self.submitted_at + datetime.timedelta(seconds=timeout) > timezone.now():
            return True

        if self.is_my_form():
            return True

        if not self.form.is_active:
            raise FormIsInactive()

        return self.is_session_active()

    def can_view_document(self):
        if not self.is_submitted():
            return True

        timeout = get_setting("DYNAMICFORM_DOCUMENT_TIMEOUT")
        if self.submitted_at + datetime.timedelta(seconds=timeout) > timezone.now():
            return True

        if current_user() is not False:
            return True
        
        if not self.form.is_active:
            raise FormIsInactive()
            
        result = self.is_session_active()
        return result

    def set_first_step_setion(self):
        schema = self.get_frontend_schema()
        key = list(schema["steps"].keys())[0]
        self.step = key
        key = list(schema["steps"][key]["sections"].keys())[0]
        self.section = key
        self.save()
        return self

    def _get_query_steps(self, request=None):
        query_steps = None
        try:
            if request:
                query = request.GET
                if query:
                    query_steps = query.get("steps", None)
                    query_steps = json.loads(query_steps)
        except:
            return None
        return query_steps

    def get_info(self, request=None, schema=None, answers=None, **kwargs):
        # start = datetime.datetime.now()
        query_params = request.query_params
        query_steps = self._get_query_steps(request=request)
        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: _get_query_steps {diff.total_seconds()}')

        if not schema:
            # start = datetime.datetime.now()
            schema = self.get_frontend_schema(query_steps=query_steps, **kwargs)
            # diff = datetime.datetime.now() - start
            # logger.info(f'info time: get_frontend_schema {diff.total_seconds()}')

        ####################
        ## Event Tracking ##
        ####################
        # start = datetime.datetime.now()
        tracking_settings = self.form.get_settings("schema_config.configs.tracking", {})
        direct_url = tracking_settings.get('direct_url', None)
        geolocation_enabled = tracking_settings.get('geolocation_enabled', False)
        if direct_url:
            tracking_url = direct_url + f"?form={self.form.slug}&slug={self.slug}"
        else:
            tracking_url = reverse("dynamicform:appliedform-tracking", form_slug=self.form.slug, slug=self.slug)

        schema["tracking"] = {
            "enabled": self.is_tracking,
            "url": tracking_url,
            "geolocation_enabled": geolocation_enabled
        }

        #####################
        ## Schema Override ##
        #####################
        self.form.override_schema(schema)

        ####################
        ## can_update     ##
        ####################
        if self.can_update():
            schema["save_url"] = reverse("dynamicform:appliedform-save", form_slug=self.form.slug, slug=self.slug)
            schema["submit_url"] = reverse("dynamicform:appliedform-submit", form_slug=self.form.slug, slug=self.slug)
        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: can_update {diff.total_seconds()}')
        #
        # start = datetime.datetime.now()
        if not answers:
            answers = self.get_answers(request=request)

        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: get_answers {diff.total_seconds()}')

        # start = datetime.datetime.now()
        info = {
            "schema": schema,
            "data": {
                "is_submitted": self.is_submitted(),
                "submitted_at": self.submitted_at,
                "created_at": self.created_at,
                "can_update": self.can_update(),
                "slug": self.slug,
                "form_slug": self.form.slug,
                "form_name": self.form.name,
                "current_step": self.step,
                "current_section": self.section,
                "data": answers,
                "step_log": self.get_log(),
                "application": self.application.info,
            },
            "trans": self.get_trans(),
            "schema_config": self.get_form_settings(path="schema_config"),
            "pages": self.form.pages_list,
        }
        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: info json {diff.total_seconds()}')
        return info

    def get_report_info(self, request=None, schema=None, answers=None, **kwargs):
        info = self.get_info(request=request, schema=schema, answers=answers, **kwargs)
        application = {"application": self.application.info}
        info["data"].update(application)
        return info

    def get_info_for_permission_deny(self):
        page_url = self.page_url
        page_name = self.get_frontend_schema().get("submitted_page", "thankyou")
        redirect_page = page_url.replace(":pageName", page_name)

        info = {"redirect_page": redirect_page}
        return info

    def pre_save_section(self, frontend_schema, step=None, section=None, answers={}, raise_validation=False):
        """
        To do actions in "pre_save" before save the current section
        json
        {
          "current-section": {
            "name": "current-section",
            "post_save": [...],
            "pre_save": [
              # ACTION OBJECT
              {
                "rules": {"underwriting_status": "in:accepted"}, # condition to do the action
                "action": "request" # request, request_ledger or save
                "params": {
                  "url": "/not-full-url",
                  "method": "post",
                  "json": {
                    "destination-key": "the-applied-form-answer-question-name"
                  },
                  # to request to destination use request_external: requests.request(method, url, **kwargs)
                  # the param (kwargs) is the same as requests.request function doc
                  # json or data will mapping with answers before send a request
                },
                "raise_validation": true,
                "errors_message": {
                  # use with action "request"
                  "not_success_status_code": "message would like to response to dynamicform",
                  "430": "Something wrong"
                },
                "data_applied_form_obj": true, # to pass mapping data like {'request':,'applied_form':,'answers':}
                "manage_results": [
                  # ACTION OBJECT
                  {
                    "action": "save",
                    "params": {
                      "mapping": {
                        "the-existing-question-name": "response.json.key.to.get.value.to.save.into.the-existing-question-name",
                        "the-none-existing-question-name": "response.json.key.to.get.value.to.save.into.the-none-existing-question-name"
                      }
                    },
                    "manage_results": {
                      # ACTION OBJECT
                    }
                  },
                  {
                    "action": "update_application_status",
                    "params": {
                      "status": "submitted",
                      "other_status": {"ekyc":"pass"}
                    },
                    "params_key_properties": [],
                    "get_main_applied_form_answers": True,
                  }
                ]
              }
            ]
          }
        }
        """
        pre_save = objects.get(frontend_schema, f"steps.{step}.sections.{section}.pre_save", None)
        if pre_save is None:
            return

        return self.do_actions(
            actions=pre_save, step=step, section=section, answers=answers, raise_validation=raise_validation
        )

    def post_save_section(self, frontend_schema, step=None, section=None, answers={}, raise_validation=False):
        post_save = objects.get(frontend_schema, f"steps.{step}.sections.{section}.post_save", None)
        if post_save is None:
            return

        return self.do_actions(
            actions=post_save, step=step, section=section, answers=answers, raise_validation=raise_validation
        )

    def do_actions(
        self, actions, step=None, section=None, answers={}, raise_validation=False, **kwargs
    ):
        if not actions:
            return

        pre_answers = self.get_report()
        action_answers = {**pre_answers, **answers}
        return self.actions(
            actions=actions,
            action_answers=action_answers,
            step=step,
            section=section,
            raise_validation=raise_validation,
            **kwargs,
        )

    def actions(self, actions, action_answers, step=None, section=None, raise_validation=False, **kwargs):
        main_applied_form_answers = {}
        action_result = {}
        for action in actions:
            all_data_answers = action_answers.copy()

            _main_applied_form_answers = {}
            if action.get("get_main_applied_form_answers", None) and not main_applied_form_answers:
                _main_applied_form_answers = self.main_applied_form.get_report()
                main_applied_form_answers = _main_applied_form_answers.copy()
            elif action.get("get_main_applied_form_answers", None) and main_applied_form_answers:
                _main_applied_form_answers = main_applied_form_answers
            all_data_answers = {**_main_applied_form_answers, **all_data_answers}

            result = self.action(
                action, all_data_answers, step=step, section=section, raise_validation=raise_validation, **kwargs
            )
            if result is None:
                continue
            action_result.update(result)
        return action_result

    def action(self, action, action_answers, step=None, section=None, raise_validation=False, **kwargs):
        params_key_properties = action.get("params_key_properties", [])
        raise_validation = action.pop("raise_validation", raise_validation)
        raise_validation_plain_text = action.pop("raise_validation_plain_text", False)

        for applied_form_prop in params_key_properties:
            if hasattr(self, applied_form_prop):
                action_answers.update({applied_form_prop: getattr(self, applied_form_prop)})

        action.update(kwargs)
        log_action = action.copy()
        try:
            result, done = do_action(
                self,
                data=action_answers,
                step=step,
                section=section,
                return_json={},
                raise_validation=raise_validation,
                **action,
            )
            if done:
                self.log(
                    step,
                    section,
                    action=self.ACTION_PRE_SAVE,
                    detail={"action": log_action, 'result': result},
                    is_fail=False,
                    message='',
                    update_current_step_section=False
                )
            return result
        except ValidationError as e:
            logger.warn(
                "pre_save_section ValidationError {}".format(str(e)),
                {"action": log_action, "step": step, "section": section},
            )
            self.log(
                step,
                section,
                action=self.ACTION_PRE_SAVE,
                detail={"action": log_action, "content": getattr(e, "detail", str(e))},
                is_fail=True,
                message=getattr(e, "detail", str(e)),
                update_current_step_section=False
            )
            if raise_validation:
                raise e
        except ActionAPIException as e:
            logger.warn(
                "pre_save_section Exception {}".format(str(e)), {"action": action, "step": step, "section": section}
            )
            self.log(
                step,
                section,
                action=self.ACTION_PRE_SAVE,
                detail={"action": log_action, "content": str(e)},
                is_fail=True,
                update_current_step_section=False
            )
            
            if raise_validation:
                if raise_validation_plain_text:
                    detail = e.detail
                else:
                    detail = {"detail": e.detail}

                raise ActionAPIException(detail=detail, status_code=e.status_code)

    def pre_save_section_all_schema(self, frontend_schema, answers):
        result = {}
        for step in frontend_schema["steps"]:
            for section in frontend_schema["steps"][step]["sections"]:
                presave_result = self.pre_save_section(
                    frontend_schema=frontend_schema, step=step, section=section, answers=answers, raise_validation=True
                )
                if presave_result and type(presave_result) == dict:
                    result = presave_result
        return result

    def notify(self):
        if self.form.backend_schema.get("allow_notification", True):
            notification(self)

    def set_disable(self):
        self.disabled_at = timezone.now()
        self.save()

    def set_application_submitted_at(self):
        self.application.set_submitted_at()

    def application_submitted(self):
        app_status = "complete"

        self.set_application_submitted_at()
        self.update_application_status(app_status=app_status)

    def get_trans(self):
        form = self.form
        locale_set = form.locale_set

        language = get_language()

        if self.dynamicform and self.dynamicform.locale_set:
            trans = self.dynamicform.locale_set
        else:
            trans = locale_set.filter(language=language)

        result = collect_trans(trans, schema=form.frontend_schema)
        return result

    def get_app_data(self, app_name, property="", **kwargs):
        # All installed apps must relate the applied form by ref.
        if not apps.is_installed(app_name):
            return None

        app_name, app_dynamicform = GetApp.get_app_name(app_name)
        if not hasattr(app_dynamicform, "dashboard_data"):
            return None

        return app_dynamicform.dashboard_data(applied_form=self, property=property, **kwargs)

    def get_event_tracking_summary(self):
        result = {
            'device_info': {
                'device_type': None,
                'device_band': None,
                'os_name': None,
                'os_version': None,
                'screen_resolution': None,
                'network_protocal': None,
                'geo_location': None,
                'browser_type': None,
                'browser_language': None,
                'ip_address': None,
                'oversea_registration': None,
            },
        }

        applied_form_log = (
            self.appliedformlog_set.filter(applied_form=self)
            .only(
                'user_agent',
                'ip_address',
            )
            .last()
        )

        if not applied_form_log:
            return result

        user_agent_data = parse(applied_form_log.user_agent)

        if user_agent_data.is_pc:
            device_type = 'Desktop'
        elif user_agent_data.is_mobile:
            device_type = 'Mobile'
        elif user_agent_data.is_tablet:
            device_type = 'Tablet'
        else:
            device_type = None

        result['device_info']['device_type'] = device_type
        result['device_info']['device_band'] = user_agent_data.device.brand
        result['device_info']['os_name'] = user_agent_data.os.family
        result['device_info']['os_version'] = user_agent_data.os.version_string
        result['device_info']['browser_type'] = user_agent_data.browser.family
        result['device_info']['ip_address'] = applied_form_log.ip_address

        return result

    def get_event_tracking_log_list(self):
        url = settings.EVENT_TRACKING_ENDPOINT_GET_LOG_LIST
        username = settings.EVENT_TRACKING_USERNAME
        password = settings.EVENT_TRACKING_PASSWORD
        params = {
            "app_name": settings.APP_NAME,
            "applied_form_slug": self.slug,
            "form_slug": self.form.slug,
        }
        response = requests.get(url, params=params, auth=(username, password))
        return response

    def update_application_status(self, app_status=None, other_status={}):
        self.application.update_status(app_status=app_status, other_status=other_status)

    def create_application(self):
        application = Application.objects.create(slug=self.slug)
        application.add_applied_form(applied_form=self)
        return application

    def set_is_tracking(self):
        if self.dynamicform:
            is_tracking = objects.get(self.dynamicform.frontend_schema, "tracking.enabled", None)
            if is_tracking is None:
                is_tracking = self.dynamicform.get_form_settings(
                    path='schema_config.configs.tracking.enabled',
                    default=None,
                    secure=True
                )
        else:
            is_tracking = objects.get(self.form.frontend_schema, "tracking.enabled", None)
            if is_tracking is None:
                is_tracking = self.get_form_settings("schema_config.configs.tracking.enabled", None)

        if is_tracking is not None:
            self.is_tracking = is_tracking
            self.save()

    def request_hook_submit(self):
        self.request_hook()

    def request_hook(self, event_trigger=TRIGGER_EVENT_SUBMIT_FORM, data={}, **kwargs):
        if not self.form.formwebhook_set.filter(
                enable=True,
                webhook__event_trigger=event_trigger
            ).exists():
            return

        query = ""
        for k, v in kwargs.items():
            query = query + f"&{k}={v}"

        url = reverse("dynamicform:appliedform-hook-submit", form_slug=self.form.slug, slug=self.slug)
        url = f"{url}?event_trigger={event_trigger}{query}"
        username = settings.DYNAMICFORM_APP_USERNAME
        password = settings.DYNAMICFORM_APP_PASSWORD
        headers = {"Authorization": requests.auth._basic_auth_str(username, password)}
        queue = self.get_form_settings(path=f"queue", default={})
        
        event_trigger_queue = queue.get("event_trigger", {}).get(event_trigger)
        if event_trigger_queue:
            queue = event_trigger_queue
        else:
            queue.pop("event_trigger", None)

        create_task(url, data, headers=headers, queue=queue)

    def hook_submit(self, query=None, data={}):
        if query is None:
            query = dict()
        collect_result = query.pop('collect_result', '1')
        if collect_result.lower() not in ['0', 'false']:
            self.apps_component_collect_result_after_submit()
        logger.info(f"Active hook applied_form slug {self.slug}", {"query": query})
        return self.form.trigger_webhooks(applied_form=self, data=data, **query)

    def apps_component_collect_result_after_submit(self):
        backend_schema = self.form.backend_schema
        app_list = backend_schema.get("app_list", {})
        app_list_step = app_list.get("steps", [])
        app_list_section = app_list.get("sections", [])
        app_list = app_list_step + app_list_section
        app_list = list(set(app_list))

        for app in app_list:
            app_name, Dynamicform = GetApp.get_app_name(app.lower()) # NOSONAR
            if not hasattr(Dynamicform, "collect_result_after_submit"):
                continue
            Dynamicform.collect_result_after_submit(applied_form=self)

    def get_request_props(self, build_method="custom_method", props={}, answers={}, **kwargs):
        data = {"applied_form": self, "answers": answers}
        CustomMethod = apps.get_model("webhook", "CustomMethod") # NOSONAR

        props = map_value_to_dest_key(data, props)

        if build_method == "request":
            request_method = props
            request_method = {"url": request_method.pop("url", None), "props": request_method}
        elif build_method == "custom_method":
            request_method = props
            request_method.pop("method", {})

        request_custom = CustomMethod(**request_method)
        body = request_method.get("props", {}).get("body", {})

        request_props = request_custom.get_method_trigger_kwargs(body=body, password=request_custom.password)

        return request_props

    def get_document(self, filename, query={}):
        """
        "documents": {
          "example-filename.pdf": {
            # refer to requests.request
            "request": {
              "url": "",
              "method": "get",
              "headers": {
                "Accept": "application/pdf",
                "Authorization": "Basic <token>"
              },
              "props": {
                "body": {"a": "{{a}}"},
                "all_application_answers" : false
              },
            }
            # refer to CustomMethod
            "custom_method": {
              "authorization": "",
              "url": "",
              "url_get_token": "",
              "token_path": "",
              "username": "",
              "password": "",
              "props": {
                "method": "get",
                "body": {"a": "{{a}}"},
                "all_application_answers" : false,
                "dynamicform_get_choice_label": false, #not return label
              },
            },
            "manage_results": [
              {
                "action": "save",
                "params": {
                  "mapping": {
                    "result": "{{result}}"
                  }
                }
              }
            ]
          }
        }
        """
        settings_path = ["documents", filename]
        document = self.get_form_settings(settings_path, {}, secure=False)

        if not document:
            e = ActionAPIException({"detail": f"no document {filename}"})
            self.log(
                action=self.ACTION_REQUEST_EXTERNAL_API, detail={"document": filename, "content": str(e)}, is_fail=True
            )
            raise e

        permission_classes = document.get("permission_classes", [])
        if permission_classes:
            self.check_permissions(permission_classes)

        build_method = None
        props = {}
        if document.get("request", {}):
            build_method = "request"
            props = document.get("request", {})
        elif document.get("custom_method", {}):
            build_method = "custom_method"
            props = document.get("custom_method", {})

        all_application_answers = props.get("props", {}).get("all_application_answers", False)
        dynamicform_get_choice_label = props.get("props", {}).get("dynamicform_get_choice_label", True)
        response_headers = props.get("props", {}).get("response_headers", {})

        locale = get_language()

        if all_application_answers:
            answers = self.application.get_answers_applied_form(
                dynamicform_get_choice_label=dynamicform_get_choice_label, locale=locale
            )
        else:
            answers = self.get_answers(dynamicform_get_choice_label=dynamicform_get_choice_label, locale=locale)
        answers.update(query)

        return_json = self.do_actions_from_setting(
            f"documents.{filename}.before_request_actions", action_answers=answers
        )
        answers.update(return_json)

        request_props = self.get_request_props(build_method=build_method, props=props, answers=answers)
        response = requests.request(**request_props)
        logger.info(
            f"document response {response.status_code}",
            {"text": response.text, "url": request_props.get("url", None), "response_status": response.status_code},
        )

        manage_results = objects.get(document, "manage_results", objects.get(document, "manage_reults", default=None))

        if (
            is_informational(response.status_code)
            or is_success(response.status_code)
            or is_redirect(response.status_code)
            or is_client_error(response.status_code)
        ):
            response_json = {}
            try:
                response_json = response.json()
            except json.decoder.JSONDecodeError:
                response_json = {"content": response.text}
            except Exception:
                response_json = {"content": response.text}
        else:
            response_json = {}

        if manage_results:
            self.actions(manage_results, action_answers=answers, data_result=response_json, data_applied_form_obj=True)

        content = {}
        response_is_success = is_success(response.status_code)
        if not response_is_success:
            content = response.text
            response_is_success = False

        self.log(
            action=self.ACTION_REQUEST_EXTERNAL_API,
            detail={"document": filename, "response_status": response.status_code, "content": content},
            is_fail=not response_is_success,
        )

        headers = response.headers
        response_headers.update(headers)

        def get_headers(headers):
            _headers = headers.copy()
            for header in _headers:
                if is_hop_by_hop(header):
                    headers.pop(header, None)
                if header in ['Content-Encoding']:
                    headers.pop(header, None)


        response_headers = get_headers(response_headers)
        response = HttpResponse(
            content=response.content,
            status=response.status_code,
            headers=response_headers
        )

        return response

    def set_application_status_ekyc(self):
        app_list = self.form.backend_schema.get("app_list", {})
        all_apps = app_list.get("steps", []) + app_list.get("sections", [])
        if "ekyc" not in all_apps and "Ekyc" not in all_apps:
            self.update_application_status(other_status={"ekyc": EKYC_STATUS_PASS})

    def can_save_answer(self, question, force_save_disable=False, **kwargs):
        keep_answers = self.form.backend_schema.get("keep_answers", {})
        items = self.form.backend_schema.get("items", {})
        if question in keep_answers:
            return True

        if question in items:
            props = objects.get(items, f"{question}.props", {})
            if ("disable" in props or "disabled" in props) and force_save_disable == False:
                return False
            return True

        return False

    def check_permissions(self, permission_classes):
        allow_permission_classes = {
            "IsAuthenticated": IsAuthenticated(),
            "IsWorkspaceAdmin": IsWorkspaceAdmin(),
            "IsWorkspaceAdminOrEditor": IsWorkspaceAdminOrEditor(),
            "IsWorkspaceMember": IsWorkspaceMember(),
        }

        _permission_classes = [allow_permission_classes[c] for c in permission_classes]

        request = CrequestMiddleware.get_request()
        for permission in _permission_classes:
            if not permission.has_permission(request, view=None):
                raise PermissionDenied()
        return True

    def user_log(self, **kwargs):
        self.application.user_log(**kwargs)

    def application_updated_now(self):
        self.updated_at = datetime.datetime.now()
        self.save()

        self.application.updated_at = datetime.datetime.now()
        self.application.save()

    def do_actions_from_setting(self, setting_actions, action_answers={}):
        actions = self.get_form_settings(setting_actions, [])

        if not actions:
            return {}

        return self.actions(actions, action_answers=action_answers, data_applied_form_obj=True)

    def set_drop_off(self):
        self.drop_off_at = timezone.now()
        self.save()
    
    def trigger_webhook(self, form_webhook_id, **kwargs):
        form_webhook = self.form.formwebhook_set.filter(
            id=form_webhook_id,
            enable=True
        ).first()
        if not form_webhook:
            return

        data = {
            'applied_form': self
        }
        return self.form.trigger_webhook(form_webhook=form_webhook, applied_form=self, data=data, **kwargs)

    def get_form_settings(self, path=None, default={}, secure=True):
        if self.dynamicform:
            return self.dynamicform.get_form_settings(
                path=path,
                default=default,
                secure=secure
            )
        
        return self.form.get_settings(
            path=path,
            default=default,
            secure=secure
        )
    
