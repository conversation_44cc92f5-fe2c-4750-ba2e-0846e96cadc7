from project.custom_logger import logger

class PDFAuthenticationError(Exception):
    """Raised when an encrypted PDF cannot be authenticated with the provided password."""
    pass

def otp_hook_request_ref_over_max(ref):
    from dynamicform.submodules.appliedform.models import AppliedForm
    from dynamicform.exceptions import AppliedformIsDisbled

    applied_form = AppliedForm.objects.filter(slug=ref).first()
    if not applied_form:
        return
    applied_form.set_disable()
    raise AppliedformIsDisbled()


def otp_hook_can_check_state_with_ref(ref):
    from dynamicform.submodules.appliedform.models import AppliedForm

    applied_form = AppliedForm.objects.filter(slug=ref).first()
    if not applied_form:
        return False

    return applied_form.can_view_info()


def visitor_ip_address(request):
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")

    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


from django.core.files.base import File


def check_file_has_pages(file):
    import os

    file_name = os.path.basename(file.name).lower()
    _, file_extension = os.path.splitext(file_name)
    file_extension = file_extension[1:].lower()  # Remove leading dot and lowercase

    logger.debug("check_file_has_pages", {"file_name": file_name, "file_extension": file_extension})

    # Check for file types that are likely single-page or don't have pages
    SINGLE_PAGE_EXTENSIONS = {
        "png",
        "jpg",
        "jpeg",
        "gif",
        "webp",
        "heic",
        "heif",
        "bmp",
        "tiff",
        "txt",
        "csv",
        "json",
        "xml",
        "html",
        "htm",
    }
    if file_extension in SINGLE_PAGE_EXTENSIONS:
        logger.debug("check_file_has_pages.single_page", {"file_extension": file_extension, "has_pages": False})
        return False

    # Default to 1 page for unknown or unhandled types
    if file_extension != "pdf":
        logger.debug("check_file_has_pages.unknown_type", {"file_extension": file_extension, "has_pages": False})
        return False

    logger.debug("check_file_has_pages.pdf", {"file_extension": file_extension, "has_pages": True})
    return True


def check_pdf_pages(file: File, keep_file_open=False):
    """
    Counts the number of pages in a file using fitz (PyMuPDF).

    Args:
        file: A file-like object (e.g., from Django's InMemoryUploadedFile or UploadedFile).
        keep_file_open (bool, optional): Whether to keep the file open after counting pages. Defaults to False (close the file).

    Returns:
        int: The number of pages in the file. Returns 1 for file types without readable pages or obviously single-page files (like common images).
    """

    logger.debug("check_pdf_pages.start")

    try:
        has_pages = check_file_has_pages(file)
        if not has_pages:
            logger.debug("check_pdf_pages.no_pages", {"return_value": 1})
            return 1

        import fitz
        from io import BytesIO

        file_stream = BytesIO(file.read())

        doc = fitz.open(stream=file_stream, filetype="pdf")
        page_count = doc.page_count
        logger.debug("check_pdf_pages.count", {"page_count": page_count})

        file.seek(0)

        if keep_file_open:
            logger.debug("check_pdf_pages.keep_open")
        else:
            doc.close()
            file_stream.close()
            logger.debug("check_pdf_pages.closed")

    except Exception as e:
        logger.error("check_pdf_pages.error", {"error": str(e)})
        return 1

    logger.debug("check_pdf_pages.end")
    return page_count


from django.core.files.uploadedfile import UploadedFile
from django.core.files.images import ImageFile


def convert_pdf_to_images(file: UploadedFile, password: str = None) -> list[ImageFile]:
    try:
        has_pages = check_file_has_pages(file)
        if not has_pages:
            return []

        import fitz
        from io import BytesIO

        images: list[ImageFile] = []

        file.seek(0)

        doc = fitz.open(stream=file.read(), filetype="pdf")

        # Handle encrypted PDFs
        if doc.is_encrypted and password:
            print(f"File '{file.name}' is encrypted. Attempting to authenticate...")
            auth_success = doc.authenticate(password)
            if not auth_success:
                doc.close() # Close the document if authentication fails
                raise PDFAuthenticationError(f"Authentication failed for '{file.name}'. Incorrect password or corrupt file.")
            print(f"Authentication successful for '{file.name}'.")

        for idx, page in enumerate(doc):

            pixmap = page.get_pixmap(dpi=300)
            image_bytes = pixmap.tobytes()

            image = ImageFile(BytesIO(image_bytes), name=f"{file.name}_{idx}.png")
            images.append(image)

        return images

    except Exception:
        import traceback

        print(traceback.format_exc())
        return []


def check_disabled_save(item: dict, answers: dict, applied_form, backend_schema=None):
    from pydash import get

    if backend_schema:
        disabled_save = backend_schema.get("disabled_save", [])
        if item["name"] in disabled_save:
            return True

    disabled_schema = get(item, ["props", "disabled"], False)
    if disabled_schema:
        return True

    disabled_schema_obsolete = get(item, ["props", "disable"], False)
    if disabled_schema_obsolete:
        return True

    disabled_condition = get(item, ["disabled_condition"], [])
    if disabled_condition:
        from decision_flow.entity.node_condition import NodeCondition

        updated_answers = answers.copy()
        required_answers = []

        def crawl_answers(obj):
            if isinstance(obj, dict):
                if obj.get("category") == "answer":
                    value = obj.get("value")
                    if value:
                        obj.update({"value": f"{{{{answer.{value}}}}}"})
                        if value not in answers:
                            required_answers.append(value)
                    return

                for value in obj.values():
                    crawl_answers(value)
            elif isinstance(obj, list):
                for item in obj:
                    crawl_answers(item)

        crawl_answers(disabled_condition)

        more_answers = applied_form.answer_set.filter(question__in=required_answers)
        updated_answers.update({answer.question: answer.value for answer in more_answers})

        context = {
            "enable_lookup_data_point": False,
            "data_point_result": {"answer": updated_answers},
        }

        # Fetch any missing answers from db if needed
        condition = NodeCondition(data={}, context=context)
        disabled = condition.do_compute(data=disabled_condition)
        if disabled:
            return True

    return False
