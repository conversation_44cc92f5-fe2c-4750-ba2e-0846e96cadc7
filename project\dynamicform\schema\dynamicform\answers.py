from dynamicform.schema.getapp import GetApp
from django.apps import apps
from .components.utilitybill import UtilityBill
from data_point.provider.answer import ANSWER_LIST_STRING_TYPE

import logging
import json

import datetime

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from dynamicform.submodules.answer.models import Answer

logger: logging.Logger = logging.getLogger(__name__)

class DynamicformAnswers:
  def __get_answers_from_model(applied_form, query={}, model=None, **kwargs):
    Model: Answer = model # NOSONAR
    if not Model:
      Model = apps.get_model(GetApp.app_default, 'Answer') # NOSONAR
    
    filter = {
      'applied_form_id': applied_form.id,
    }

    filter_questions_in = []
    filter_questions = kwargs.get('filter_questions', [])
    query_answers = query.get('answers', False)
    if query_answers:
      filter_questions_in = filter_questions + query_answers.split(',')
    if filter_questions:
      filter_questions_in = filter_questions_in + filter_questions

    if len(filter_questions_in) > 0:
      filter['question__in'] = filter_questions_in

    answers = Model.objects.filter(**filter)
    return answers

  def __get_choice_label(question, value, item={}, **kwargs):
    # todo apply locale

    if not item:
      return value

    if item.get('type', None) == 'SignaturePad' and len(value)>0:
      return value[0].get('preview')

    if item.get('type', None) == 'OfflineUploader' and len(value)>0:
      return [i.get('preview', None) for i in value]

    enum = item.get('enum', None)
    if enum:
      for i in enum:
        if value == i.get('value', None):
          return i.get('label', None)
    
    return value

  def __get_answers__old(applied_form, dynamicform_get_choice_label=None, **kwargs):
    Answer = apps.get_model(GetApp.app_default, 'Answer') # NOSONAR

    _answers = {}
    answers = DynamicformAnswers.__get_answers_from_model(applied_form, model=Answer, **kwargs)
    answers = answers.values('id', 'question', '_value')
    
    items_schema = applied_form.form.backend_schema.get('items', {})
    
    for answer in answers:

      question = answer.get('question', None)
      item = items_schema.get(question, {})
      item_type = item.get('type', None)
      answer_id = answer.get('id', None)
      
      if item_type in ['OfflineUploader']:
        answer_value, _ = Answer.get_value_from_type_file(answer_id=answer_id)
        _answers.update({
          question: answer_value
        })
        
      elif item_type in ['SignaturePad']:
        answer_value, _ = Answer.get_value_from_type_signature(answer_id=answer_id)
        _answers.update({
          question: answer_value
        })

      elif item_type in ['UtilityBill']:
        answer_value, answer_obj = Answer.get_value_from_type_utilitybill(answer_id=answer_id, **kwargs)
        _answers.update({
          question: answer_value
        })

        utility_bill = UtilityBill()
        if answer_obj:
          _answers.update(utility_bill.get_component_value(answer_obj, item))
        # diff = datetime.datetime.now() - start
        # logger.info(f'info time: answers: UtilityBill get_component_value {diff.total_seconds()}')

      elif item_type in ['SmartUploader']:
        answer_value, _ = Answer.get_value_from_type_smartuploader(answer_id=answer_id, **kwargs)
        _answers.update({
          question: answer_value
        })

      else:

        answer_value = answer.get('_value', None)
        try:
          answer_value = json.loads(answer_value)
        except:
          pass

        if dynamicform_get_choice_label:
          answer_value = DynamicformAnswers.__get_choice_label(question, answer_value, item, **kwargs)
       
        _answers.update({
          question: answer_value
        })
    return _answers

  def __get_answers(applied_form, dynamicform_get_choice_label=None, dynamicform=None, **kwargs):
    Answer = apps.get_model(GetApp.app_default, 'Answer') # NOSONAR
    _answers = {}
    answers = DynamicformAnswers.__get_answers_from_model(applied_form, model=Answer, **kwargs)
    answers = [answer.get_answers_dict('id', 'question', '_value') for answer in answers]

    if dynamicform:
      items_schema = dynamicform.backend_schema_items
    else:
      items_schema = applied_form.form.backend_schema.get('items', {})
    
    item_type_answer_ids = {
        'OfflineUploader': [],
        'SignaturePad': [],
        'UtilityBill': [],
        'SmartUploader': [],
      }
    answer_id_question = {}

    for answer in answers:
      question = answer.get('question', None)
      item = items_schema.get(question, {})
      item_type = item.get('type', None)
      answer_id = answer.get('id', None)
      
      if item_type in item_type_answer_ids:
        item_type_answer_ids[item_type].append(answer_id)
        answer_id_question.update({answer_id:question})
      else:
        answer_value = answer.get('_value', None)

        if dynamicform_get_choice_label:
          answer_value = DynamicformAnswers.__get_choice_label(question, answer_value, item, **kwargs)
       
        _answers.update({
          question: answer_value
        })
    
    lookup_kwargs = {
      "dynamicform": dynamicform,
      "answer_id_question": answer_id_question,
      **kwargs
    }

    for item_type, answer_ids in item_type_answer_ids.items():
      _items = {}
      if item_type == 'OfflineUploader':
        _items = Answer.get_value_from_type_file_from_answer_ids(
          answer_ids=answer_ids,
          **lookup_kwargs
        )
      elif item_type == 'SignaturePad':
        _items = Answer.get_value_from_type_signature_from_answer_ids(
          answer_ids=answer_ids,
          **lookup_kwargs
        )
      elif item_type == 'UtilityBill':
        _items = Answer.get_value_from_type_utilitybill_from_answer_ids(
          answer_ids=answer_ids,
          **lookup_kwargs
        )
      elif item_type == 'SmartUploader':
        _items = Answer.get_value_from_type_smartuploader_from_answer_ids(
          answer_ids=answer_ids, 
          **lookup_kwargs
        )

      if not _items:
        continue

      for answer_id, answer_value in _items.items():
        question_name = answer_id_question.get(answer_id, None)
        if not question_name:
          continue
        _answers.update({question_name:answer_value})

    return _answers
  
  def get_answers(applied_form, **kwargs):
    return DynamicformAnswers.__get_answers(applied_form, **kwargs)

  def get_report(applied_form, **kwargs):
    return DynamicformAnswers.__get_answers(applied_form, report=True, **kwargs)

  def get_submit_answers(applied_form, **kwargs):
    _answers = []
    answers = DynamicformAnswers.__get_answers_from_model(applied_form, submit_answers=True, **kwargs)
    for answer in answers:
      _answers.append({
        'step': answer.step,
        'section': answer.section,
        'question': answer.question,
        'value': answer.get_submit_value(**kwargs),
        'created_at': str(answer.created_at.isoformat()),
        'updated_at': str(answer.updated_at.isoformat()),
      })

    return _answers
  
  def hard_delete(applied_form, **kwargs):
    Answer = apps.get_model(GetApp.app_default, 'Answer') # NOSONAR
    Answer.objects.filter(applied_form__id=applied_form.id).delete()
  
  @staticmethod
  def dashboard_data(applied_form, property=None, **kwargs):
    return getattr(applied_form, property, None)
  
  @staticmethod
  def get_extra(applied_form, questions, **kwargs):
    result = {}
    utility_bills = applied_form.appliedformutilitybill_set.all()
    if utility_bills:
      from dynamicform.submodules.appliedform.serializer import ApplicationUtilityBillResultSerializer
      serializer = ApplicationUtilityBillResultSerializer(
        utility_bills,
        many=True,
        context={
          "get_extra": True
        }
      )
      result['utility_bills'] = serializer.data

    return result
