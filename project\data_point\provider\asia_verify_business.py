import inspect
import logging
import traceback
from datetime import date, datetime

from dateutil.parser import parse
from dateutil.relativedelta import relativedelta
from django.conf import settings
from pydash import get, set_
from rest_framework import status
from rest_framework.exceptions import APIException

from data_point.base import DataPointBase
from data_point.exceptions import DataPointException, DataPointInputValidationError, DataPointResponseException
from data_point.fields.base import DataPointJ<PERSON>NField
from data_point.services.asia_verify.v2 import AsiaVerifyApiV2
from data_point.utils import ErrorMessage

DATA_POINT_ASIA_VERIFY_HOST = settings.DATA_POINT_ASIA_VERIFY_HOST
DATA_POINT_ASIA_VERIFY_AUTH = settings.DATA_POINT_ASIA_VERIFY_AUTH
DATA_POINT_ASIA_VERIFY_SIGN = settings.DATA_POINT_ASIA_VERIFY_SIGN

DATAPOINT_OPTIONS = {
    'company_name': {
        'label': 'Return the company name from AsiaVerify (local)',
        'type': 'string',
    },
    'registered_date': {
        'label': 'Date the company has registered',
        'type': 'date',
    },
    'company_age': {
        'label': 'The age of the company calculated from the registered_date',
        'type': 'number',
    },
    'company_age_month': {
        'label': 'The age of the company calculated from the registered_date in month',
        'type': 'number',
    },
    'company_status': {
        'label': 'Current status of the company',
        'type': 'string',
    },
    'capital': {
        'label': 'Amount of registered capital',
        'type': 'number',
    },
    'company_name_en': {
        'label': 'Return the company name from AsiaVerify (EN)',
        'type': 'string',
    },
}

logger: logging.Logger = logging.getLogger(__name__)

class AsiaVerifyBusiness(DataPointBase):
    business_information = DataPointJSONField(
        label='Business Information',
        type='string',
        required=True,
        split_fields=True,
        allowed_item_builder_types=["business_information"] #  allow only business_information builder
    )

    name = 'asia_verify_business'
    title = 'eKYB Business (Asia Verify)'
    sub_title = 'Business Information'
    description = 'Platform allows real-time company checks, UBO, KYB, and KYC by establishing direct connections with the business registries in Asia.'
    icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/icon_64/AsiaVer_64.png'
    option_icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/option-icon_36/AsiaVer_36.png'
    data_point_opitons = DATAPOINT_OPTIONS

    def validate_business_information(self, value):
        self.is_valid_payload_connection(value, field_name='business_information')
        return value
    
    def set_current_payload(self):
        # Set payload from business_infomation by split_field feature.
        self.set_payload('id')
        self.set_payload('name')
        self.set_payload('country')

        return self.current_payload
    
    def get_data_result(self, **kwargs):
         # split field from business infomation
        company_id = self.get_payload('id')
        business_infomation_name = self.get_payload('name')
        country = self.get_payload('country')

        save_result = {
            'asia_verify': [],
        }
        
        is_input_valid, error_message = self.validate_input(company_id, country)
        
        # Prepare error result and Return error result
        # if is_input_valid is False
        if not is_input_valid:
            error_result =  self.invalid_input_error_result(error_message, DataPointInputValidationError)
            if 'error_result_tracking' in kwargs.keys() :
                self.prepare_error_result_for_save_db(save_result, error_result, kwargs['error_result_tracking'])
            return error_result

        # Initial body for request body of Asia Verify API
        data_point_option_result_function = None
        country_upper = str(country).upper()  # Make country always upper
        body = {
            "input": company_id,
            "language": "ALL",
            "country": country_upper # This field is not effective in request but need to set to use in bussiness tab in report page
        }
        # Find function to use to convert response to data point result along country from connection
        if country_upper == "JPN":
            data_point_option_result_function = self.data_point_option_result_for_japan
        elif country_upper == "MYS":
            data_point_option_result_function = self.data_point_option_result_for_malaysia
        elif country_upper == "SGP":
            data_point_option_result_function = self.data_point_option_result_for_singapore
        elif country_upper == "HKG":
            data_point_option_result_function = self.data_point_option_result_for_hongkong
        elif country_upper == "PHL":
            data_point_option_result_function = self.data_point_option_result_for_philippines
        elif country_upper == "AUS":
            data_point_option_result_function = self.data_point_option_result_for_australia
        else:
            # This exception is from develop do not add any configuration for this country
            raise DataPointException(f'This country is not configured for request service: {str(country)}')
        
        asia_verify_service = AsiaVerifyApiV2(self.context, self.name)
        # Call Asia Verify API
        if country_upper  == "HKG":
            search_result = asia_verify_service.search_asia_verify_webhook_response(
                request_body=body, 
                country=country_upper
            )
        elif country_upper == "PHL":
            body['additionalIdentifier'] = {
                    "companyName": "company_name"
                }
            search_result = asia_verify_service.search_asia_verify_webhook_response(
                request_body=body, 
                country=country_upper
            )
        else:
            search_result = asia_verify_service.search_asia_verify(
                request_body=body, 
                country=country_upper
            )
        save_result['asia_verify'].append({
            'input': body,
            'output': search_result,
        })
        
        # Logic for Asia verify to data point result then set result to current_data_point_output to save in next step
        v2_response = ["PHL", "AUS"]
        if any(True for country_code in v2_response if country_upper == country_code) :
            company_information = get(search_result, 'result', {})
        else:
            company_information = get(search_result, 'result.data', {})
        result = data_point_option_result_function(company_information)
        self.set_current_data_point_output(save_result)
        return result

    def data_point_option_result_for_japan(self, company_information: dict) -> dict :
        company_information_data = get(company_information, 'CompanyRegistrationInformation.data[0]')
        
        company_name = get(company_information_data, 'ogName')
        # company_province = get(company_information_data, f'regAddress')
        # if company_province :
        #     company_province = str(company_province).split(',')[-1].strip()
        # company_structure_type = None
        registered_date_str = get(company_information_data, "establishDate")
        registered_date = None
        if registered_date_str:
            registered_date = datetime.strptime(registered_date_str, '%Y-%m-%d %H:%M:%S')
            registered_date = registered_date.date()

        # Find company_age and company_age_month by registered_date
        company_age = None
        company_age_month = None
        if registered_date :
            try:
                company_age, company_age_month = self.__find_company_age_by_registered_date(registered_date)
            except Exception as e:
                company_age = None
                company_age_month = None
                traceback.print_exc()
                logger.info(f'Asia verify cannot get company_age_month', {
                    'query_result': company_information,
                    'error': str(e)
                })  # NOSONAR
        company_status = get(company_information_data, 'bizStatus')
        # business_code = get(company_information_data, 'businessScope')
        # business_description = get(company_information_data, 'businessScope')
        capital = self.__convert_money_to_float(get(company_information_data, 'regCapital'))
        # paid_capital = None
        company_name_en = get(company_information_data, 'enName')
        return {
            'company_name': company_name,
            # 'company_province': company_province,
            # 'company_structure_type': company_structure_type,
            'registered_date': datetime.strftime(registered_date, '%Y-%m-%d') if registered_date else None,
            'company_age': company_age,
            'company_age_month': company_age_month,
            'company_status': company_status,
            # 'business_code': business_code,
            # 'business_description': business_description,
            'capital': capital,
            # 'paid_capital': paid_capital,
            'company_name_en': company_name_en,
        }
    
    def data_point_option_result_for_singapore(self, company_information: dict) -> dict :
        company_information_data = get(company_information, 'CompanyRegistrationInformation.data[0]')
        
        company_name = get(company_information_data, 'companyName')
        # company_province = "Singapore"
        # company_structure_type = get(company_information_data, "companyType")
        registered_date_str = get(company_information_data, "establishDate")
        registered_date = None
        if registered_date_str:
            registered_date = datetime.strptime(registered_date_str, '%Y-%m-%d')
    
        # Find company_age and company_age_month by registered_date
        company_age = None
        company_age_month = None
        if registered_date :
            try:
                company_age, company_age_month = self.__find_company_age_by_registered_date(registered_date)
            except Exception as e:
                company_age = None
                company_age_month = None
                traceback.print_exc()
                logger.info(f'Asia verify cannot get company_age_month', {
                    'query_result': company_information,
                    'error': str(e)
                }) # NOSONAR
        company_status = get(company_information_data, 'status')
        # business_code = get(company_information_data, 'businessScope.code')
        # business_description = get(company_information_data, 'businessScope.description')
        capital = self.__convert_money_to_float(get(company_information_data, 'shares[0].issuedCapital'))
        # paid_capital = self.__convert_money_to_float(get(company_information_data, 'shares[0].paidUpCapital')) 
        company_name_en = get(company_information_data, 'companyName')

        return {
            'company_name': company_name,
            # 'company_province': company_province,
            # 'company_structure_type': company_structure_type,
            'registered_date': datetime.strftime(registered_date, '%Y-%m-%d') if registered_date else None,
            'company_age': company_age,
            'company_age_month': company_age_month,
            'company_status': company_status,
            # 'business_code': business_code,
            # 'business_description': business_description,
            'capital': capital,
            # 'paid_capital': paid_capital,
            'company_name_en': company_name_en,
        }
    
    def data_point_option_result_for_malaysia(self, company_information: dict) -> dict :
        company_information_data = get(company_information, 'CompanyRegistrationInformation.data[0]')
        
        company_name = get(company_information_data, 'companyName')
        
        # Find province in address
        # company_address_list = get(company_information_data, f'companyAddresses', [])
        # company_province = None
        # if  company_address_list :
        #     company_address: dict = next((address for address in company_address_list 
        #                                   if address.get('addressType') == 'Registered Address'), {})
        #     address = str(get(company_address, "address"))
        #     company_province = address.split(',')[-3].strip()

        # company_structure_type = get(company_information_data, "companyType")
        registered_date_str = get(company_information_data, "establishedDate")
        registered_date = None

        if registered_date_str:
            try:
                registered_date = parse(registered_date_str)
                parsed_registered_date_str = registered_date.strftime('%Y-%m-%d')
            except Exception as e:
                logger.info(
                    'Asia verify cannot parse registered_date',
                    {
                        'registered_date_str': registered_date_str,
                        'error': str(e),
                    },
                )
                parsed_registered_date_str = None

            company_information = set_(company_information, 'CompanyRegistrationInformation.data[0].establishedDate', parsed_registered_date_str)

        # Find company_age and company_age_month by registered_date
        company_age = None
        company_age_month = None
        if registered_date :
            try:
                company_age, company_age_month = self.__find_company_age_by_registered_date(registered_date)
            except Exception as e:
                company_age = None
                company_age_month = None
                traceback.print_exc()
                logger.info(f'Asia verify cannot get company_age_month', {
                    'query_result': company_information,
                    'error': str(e)
                })  # NOSONAR
        company_status = get(company_information_data, 'bizStatus')
        # business_code = get(company_information_data, 'businessScope[0].code')
        # business_description = get(company_information_data, 'businessScope[0].description')
        capital = self.__convert_money_to_float(get(company_information_data, 'capitalStructure[0].shareAmount'))
        # paid_capital = None
        company_name_en = get(company_information_data, 'companyName')
        return {
            'company_name': company_name,
            # 'company_province': company_province,
            # 'company_structure_type': company_structure_type,
            'registered_date': datetime.strftime(registered_date, '%Y-%m-%d') if registered_date else None,
            'company_age': company_age,
            'company_age_month': company_age_month,
            'company_status': company_status,
            # 'business_code': business_code,
            # 'business_description': business_description,
            'capital': capital,
            # 'paid_capital': paid_capital,
            'company_name_en': company_name_en,
        }

    def data_point_option_result_for_hongkong(self, company_information: dict) -> dict :
        company_information_data = get(company_information, 'CompanyRegistrationInformation.data')
        
        company_name = get(company_information_data, 'ogName')
        # company_province = None
        # company_structure_type = get(company_information_data, "orgType")
        
        # Convert date pattern to out standard
        registered_date_str = get(company_information_data, "establishDate")
        registered_date = None
        if registered_date_str:
            try:
                registered_date = datetime.strptime(registered_date_str, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                registered_date = datetime.strptime(registered_date_str, '%Y-%m-%d')
            registered_date = registered_date.date()
    
        # Find company_age and company_age_month by registered_date
        company_age = None
        company_age_month = None
        if registered_date :
            try:
                company_age, company_age_month = self.__find_company_age_by_registered_date(registered_date)
            except Exception as e:
                company_age = None
                company_age_month = None
                traceback.print_exc()
                logger.info(f'Asia verify cannot get company_age_month', {
                    'query_result': company_information,
                    'error': str(e)
                })  # NOSONAR
    
        company_status = get(company_information_data, 'bizStatus')
        # business_code = None
        # business_description = None
        capital = self.__convert_money_to_float(get(company_information_data, 'capitalStructure.details[0].totalAmount'))
        # paid_capital = None
        company_name_en = get(company_information_data, 'enName')
        return {
            'company_name': company_name,
            # 'company_province': company_province,
            # 'company_structure_type': company_structure_type,
            'registered_date': datetime.strftime(registered_date, '%Y-%m-%d') if registered_date else None,
            'company_age': company_age,
            'company_age_month': company_age_month,
            'company_status': company_status,
            # 'business_code': business_code,
            # 'business_description': business_description,
            'capital': capital,
            # 'paid_capital': paid_capital,
            'company_name_en': company_name_en,
        }
    
    def data_point_option_result_for_philippines(self, company_information: dict) -> dict :
        company_information_data = get(company_information, 'CompanyInformation.data.0', {})
        company_name = get(company_information_data, 'name.companyName_other.0.otherName')
        company_province = get(company_information_data, 'regAddress.state')    
        company_status = get(company_information_data, 'companyStatus')
        capital_list = get(company_information_data, 'capital')
        capital = None
        for capital_dict in capital_list:
            if capital is None:
                capital = self.__convert_money_to_float(get(capital_dict, 'capitalAmount'))
            else:
                capital += self.__convert_money_to_float(get(capital_dict, 'capitalAmount'))
        company_name_en = get(company_information_data, 'name.companyName')
        return {
            'company_name': company_name,
            'company_province': company_province,
            'company_status': company_status,
            'capital': capital,
            'company_name_en': company_name_en,
        }
    
    def data_point_option_result_for_australia(self, company_information: dict) -> dict :
        company_information_data = get(company_information, 'CompanyInformation.data.0', {})
        company_name = get(company_information_data, 'name.companyName')
        company_status = get(company_information_data, 'companyStatus')
        company_name_en = get(company_information_data, 'name.companyName')
        
        capital_list = get(company_information_data, 'capital', [])
        capital = None
        for capital_dict in capital_list:
            if capital is None:
                capital = self.__convert_money_to_float(get(capital_dict, 'capitalAmount'))
            else:
                capital += self.__convert_money_to_float(get(capital_dict, 'capitalAmount'))
        
        # Find company_age and company_age_month by registered_date
        company_age = None
        company_age_month = None
        
        registered_date_str = get(company_information_data, "incorpDate")
        registered_date = None

        if registered_date_str:
            try:
                registered_date = parse(registered_date_str)
                parsed_registered_date_str = registered_date.strftime('%Y-%m-%d')
            except Exception as e:
                logger.info(
                    'Asia verify cannot parse registered_date',
                    {
                        'registered_date_str': registered_date_str,
                        'error': str(e),
                    },
                )
                parsed_registered_date_str = None

            company_information = set_(company_information, 'CompanyInformation.data[0].incorpDate', parsed_registered_date_str)
            
        if registered_date :
            try:
                company_age, company_age_month = self.__find_company_age_by_registered_date(registered_date)
            except Exception as e:
                company_age = None
                company_age_month = None
                traceback.print_exc()
                logger.info(f'Asia verify cannot get company_age_month', {
                    'query_result': company_information,
                    'error': str(e)
                })  # NOSONAR
        return {
            'company_name': company_name,
            'registered_date': datetime.strftime(registered_date, '%Y-%m-%d') if registered_date else None,
            'company_age': company_age,
            'company_age_month': company_age_month,
            'company_status': company_status,
            'capital': capital,
            'company_name_en': company_name_en,
        }

    def __convert_money_to_float(self, money: str | None) -> float | None:
        if money:
            # Remove HK currency from money then cast to float
            money = money.replace('HK$', '')
            return float(money.replace(',', ''))
        return None
    
    def __find_company_age_by_registered_date(self, registered_date: datetime | date) -> tuple:
        current_date = datetime.now()
        
        # Calculate the date difference using relativedelta
        date_diff = relativedelta(current_date, registered_date)
        
        # Calculate the total difference in months
        company_age = date_diff.years
        company_age_month = (date_diff.years * 12) + date_diff.months
        return company_age, company_age_month
    
    def validate_input(self, company_id: str | None, country: str | None) -> tuple[bool, str|None]:
        '''Validate input that is valid for Asia verify business connection

        Validate checklistanot None or empty string
        - country must be equal one of key in STATIC_OPTIONS
        
        Args:
            company_id: company_id that need to validate
            country: country that need to validate
        
        Return:
            tuple: 
            * bool: True if inputs valid orElse, return False
            * str|None: Error message for display
        '''
        country_support = AsiaVerifyApiV2.COUNTRY_SUPPORT
        is_input_valid = True
        
        is_input_valid = True
        error_message = None
        if not (company_id and country):
            is_input_valid = False
            field_list = []
            
            if not company_id :
                field_list.append("Company ID")
            if not country:
                field_list.append("Country")
            is_input_valid = False
            format_text = {"fields": ', '.join(field_list)}
            error_message = f"{self.title}: {ErrorMessage.EMPTY_VALUE.value.format(**format_text)}"
        elif country not in country_support:
            is_input_valid = False
            error_message = f'{self.title}: {ErrorMessage.COUNTRY_UNSUPPORT.value}: {country}'
        return is_input_valid, error_message

