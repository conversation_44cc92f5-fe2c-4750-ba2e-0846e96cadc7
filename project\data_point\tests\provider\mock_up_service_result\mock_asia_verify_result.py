import enum


class MockAsiaVerifyServiceResult:
    '''This class is responsible for mock response from AsiaVerifyBusiness api service to use in Business AML unit test'''
    JPN_RESULT = {
        "code": 200,
        "message": "Request succeeded",
        "result": {
            "data": {
                "Shareholders": {
                    "total": 10,
                    "data": [
                        {
                            "proportion": "25",
                            "shareholderName": "Japan Kastodo Bank Co., Ltd. (Trust Port 5)"
                        },
                        {
                            "proportion": "14.6",
                            "shareholderName": "ステートストリートバンクウェストクライアントトリーティー505234 (Permanent Attorney Co., Ltd. Mitsu Bank)"
                        },
                        {
                            "proportion": "14.9",
                            "shareholderName": "Mitsubishi UFJ Bank Co., Ltd."
                        },
                        {
                            "proportion": "16.6",
                            "shareholderName": "Nippon Life Insurance Co., Ltd. (Permanent Agent, Japan Mastaste Trust Bank Co., Ltd.)"
                        },
                        {
                            "proportion": "20.5",
                            "shareholderName": "Tokyo Maritime Fire Protection Co., Ltd."
                        },
                        {
                            "proportion": "29.6",
                            "shareholderName": "Meiji Yasuda Life Insurance Co., Ltd. (Permanent Agent, Japan Kastode Bank Co., Ltd.)"
                        },
                        {
                            "proportion": "33.1",
                            "shareholderName": "エスエスビーティシークライアントオムニバスアカウント (Permanent Agent The Hongkong and Shanghai Banking Corporation)"
                        },
                        {
                            "proportion": "0.40",
                            "shareholderName": "Modex Rei · Land · Can Pini · Ell Elsie (Permanent Agent Mitsubishi UFJ Bank Co., Ltd.)"
                        },
                        {
                            "proportion": "0.0682",
                            "shareholderName": "Japan Cattod Bank Co., Ltd. (trust port)"
                        },
                        {
                            "proportion": "0.25",
                            "shareholderName": "Japan Mast Tower Trust Bank Co., Ltd. (Trust Port)"
                        }
                    ]
                },
                "majorPerson": {
                    "total": 1,
                    "data": [
                        {
                            "rdPersonnel": "Director Toshihiro Mibe, Executive Officer and President"
                        }
                    ]
                },
                "CompanyRegistrationInformation": {
                    "total": 1,
                    "data": [
                        {
                            "country": "Japan",
                            "regCapital": "86,067,000,000.00",
                            "businessScope": "Transportation machinery and equipment manufacturing industry, その他の manufacturing industry, information service industry, video, sound, and text information production industry, article leasing industry, professional service industry",
                            "establishDate": "1948-09-24 00:00:00",
                            "employeeNumber": "21903",
                            "webSite": "http://www.honda.co.jp/", # NOSONAR
                            "closeDate": "",
                            "regNumber": "6010401027577",
                            "enName": "",
                            "ogName": "本田技研工業株式会社",
                            "bizStatus": "",
                            "closeCause": "",
                            "regAddress": "No. 1, Minami-Aoyama 2-chome, Minato-ku, Tokyo",
                            "kanaName": "ほんだぎけんこうぎょう"
                        }
                    ]
                }
            },
            "lastUpdated": "2024-08-05 15:09:20",
            "orderNo": "av202408051509191010179733",
            "status": 200004,
            "total": 1
        }
    }

    MYS_RESULT = {
        "code": 200,
        "message": "Request succeeded",
        "result": {
            "data": {
                "Shareholders": {
                    "total": 1,
                    "data": [
                        {
                            "shareNumber": "100",
                            "totalNumber": "100000",
                            "stockName": "SUPER 10 CAPITAL (EAST COAST) SDN. BHD.",
                            "id": "1258314-V",
                            "shareholderType": "Company"
                        }
                    ]
                },
                "majorPerson": {
                    "total": 3,
                    "data": [
                        {
                            "address": "NO 23 LORONG IM 5/17, BANDAR INDERA MAHKOTA, KUANTAN, Pahang, 25200",
                            "name": "HOOI WAI PIK",
                            "position": "Officer",
                            "birthDate": "1/19/1969"
                        },
                        {
                            "address": "NO 23 LORONG IM 5/17, BANDAR INDERA MAHKOTA, KUANTAN, Pahang, 25200",
                            "name": "CHONG KUAN MEE",
                            "position": "Officer",
                            "birthDate": "1/4/1968"
                        },
                        {
                            "address": "NO. 9, 1ST FLOOR, TAMAN ZABIDIN, JALAN TEMERLOH,, MENTAKAB, Pahang, 28400",
                            "name": "LEOW CHUEN HOCK",
                            "position": "director",
                            "birthDate": "8/27/1982"
                        }
                    ]
                },
                "CompanyRegistrationInformation": {
                    "total": 1,
                    "data": [
                        {
                            "companyNumberOld": "1000008-M",
                            "establishedDate": "28/04/2010",
                            "companyNumber": "201001012084",
                            "companyType": "Private",
                            "companyName": "BIG 10 SUPER STORE (TERENGGANU) SDN. BHD.",
                            "capitalStructure": [
                                {
                                    "currency": "RM",
                                    "shareAmount": "100000"
                                }
                            ],
                            "businessScope": [
                                {
                                    "code": "",
                                    "description": "TO OPERATE DEPARTMENT STORE SELLING VARIOUS PRODUCTS AND SUPPLIES OF CLOTHINGS, BAGS, SCARVES, BELTS AND OTHER ACCESSORIES AND GENERAL HOUSEHOLD PRODUCTS"
                                }
                            ],
                            "bizStatus": "Exists",
                            "companyAddresses": [
                                {
                                    "address": "PT 16742-16745, JALAN BESAR, KAMPUNG CACAR, PAKA, Terengganu, 23100, MALAYSIA",
                                    "addressType": "Business Address"
                                },
                                {
                                    "address": "NO. B18, (1ST FLOOR),, JALAN KURNIA JAYA 3,, PUSAT KOMERSIAL BATU 3,, KUANTAN, Pahang, 25150, MALAYSIA",
                                    "addressType": "Registered Address"
                                }
                            ]
                        }
                    ]
                }
            },
            "lastUpdated": "2024-08-05 15:11:49",
            "orderNo": "av202408051511481649832837",
            "status": 200004,
            "total": 1
        }
    }

    SGP_RESULT = {
        "code": 200,
        "message": "Request succeeded",
        "result": {
            "data": {
                "Shareholders": {
                    "total": 1,
                    "data": {
                        "individual": [
                            {
                                "name": "JORDAN KATHERINE SEE",
                                "currency": "SINGAPORE, DOLLARS",
                                "id": "*********",
                                "shareAmount": "100",
                                "shareType": "Ordinary",
                                "appointmentDate": "2019-02-12"
                            }
                        ],
                        "members": [],
                        "grouping": []
                    }
                },
                "majorPerson": {
                    "total": 2,
                    "data": [
                        {
                            "name": "JORDAN KATHERINE SEE",
                            "id": "*********",
                            "position": "Director",
                            "appointDate": "2019-02-12"
                        },
                        {
                            "name": "KENNY GIAM CHOONG LENG",
                            "id": "*********",
                            "position": "Secretary",
                            "appointDate": "2019-03-14"
                        }
                    ]
                },
                "CompanyRegistrationInformation": {
                    "total": 1,
                    "data": [
                        {
                            "historyNames": [],
                            "statusDate": "2019-02-12",
                            "companyType": "EXEMPT PRIVATE COMPANY LIMITED BY SHARES",
                            "companyName": "23 FILM COMPANY PTE. LTD.",
                            "lastARDate": "",
                            "businessScope": {
                                "code": "59111",
                                "description": "MOVIE PRODUCTION",
                                "otherDescription": "DEVELOPMENT, PRODUCTION OF MOTION PICTURES"
                            },
                            "lastFinancialDate": "",
                            "type": "COM",
                            "establishDate": "2019-02-12",
                            "lastAGMDate": "",
                            "shares": [
                                {
                                    "shareAllotted": "100",
                                    "shareCurrency": "SINGAPORE, DOLLARS",
                                    "issuedCapital": "1",
                                    "shareType": "Ordinary",
                                    "paidUpCapital": "1"
                                }
                            ],
                            "expiryDate": "",
                            "number": "201904553K",
                            "regAddress": {
                                "blkhseNo": "440",
                                "bldgName": "",
                                "streetName": "PASIR RIS DRIVE 4",
                                "unitNo": "01",
                                "postalCode": "510440",
                                "levelNo": "09"
                            },
                            "status": "Live Company"
                        }
                    ]
                }
            },
            "lastUpdated": "2024-08-05 15:16:22",
            "orderNo": "av202408051516221105041272",
            "status": 200004,
            "total": 1
        }
    }

    HKG_RESULT = {
        "code":200,
        "message":"Request succeeded",
        "result":{
            "lastUpdated":"2022-06-08 23:42:57",
            "total":1,
            "orderNo":"av202206082342571364985685",
            "data":{
                "Shareholders":{
                    "total":1,
                    "data":{
                        "publishDate":"2021-10-11 00:00:00",
                        "details":[
                            {
                                "shareNumber":"100",
                                "totalNumber":"10,000",
                                "address":"NO.56, JIJIA NONG, CHAQIAO VILLAGE, AN ZHENZHEN, XISHAN DISTRICT, WUXI CITY, JIANGSU, CHINA",
                                "stockName":"Wu Danping WU DAN PING",
                                "percent":"100.00%",
                                "shareType":"Ordinary shares"
                            }
                        ]
                    }
                },
                "majorPerson":{
                    "total":1,
                    "data":{
                        "publishDate":"2021-10-11 00:00:00",
                        "details":[
                            {
                                "position":"Director",
                                "type":"Natural person",
                                "ogFullName":"吳旦娉",
                                "enFullName":"WU, DAN PING"
                            }
                        ]
                    }
                },
                "CompanyRegistrationInformation":{
                    "total":1,
                    "data":{
                        "orgType":"Private company limited by shares",
                        "regNumber":"2154701",
                        "enName":"LSH INTERNATIONAL CO., LIMITED",
                        "capitalStructure":{
                            "publishDate":"2021-10-11 00:00:00",
                            "details":[
                                {
                                    "totalAmount":"HK$10,000",
                                    "totalNumber":"10,000",
                                    "currency":"HK$",
                                    "totalAmountPaid":"HK$10,000",
                                    "shareType":"Ordinary shares"
                                }
                            ]
                        },
                        "ogName":"理盛國際有限公司",
                        "bizStatus":"Live",
                        "establishDate":"2014-10-10 00:00:00"
                    }
                }
            },
            "status":200004
        },
    }

    @classmethod
    def get_major_person_list(cls, country: str) -> list:
        if country.upper() == 'JPN':
            return cls.JPN_RESULT['result']['data']['majorPerson']['data']
        if country.upper() == 'MYS':
            return cls.MYS_RESULT['result']['data']['majorPerson']['data']
        if country.upper() == 'SGP':
            return cls.SGP_RESULT['result']['data']['majorPerson']['data']
        if country.upper() == 'HKG':
            return cls.HKG_RESULT['result']['data']['majorPerson']['data']['details']
        return []

    @classmethod
    def get_25shareholder_list(cls, country: str) -> list:
        if country.upper() == 'JPN':
            return cls.JPN_RESULT['result']['data']['Shareholders']['data']
        if country.upper() == 'MYS':
            return cls.MYS_RESULT['result']['data']['Shareholders']['data']
        if country.upper() == 'SGP':
            return cls.SGP_RESULT['result']['data']['Shareholders']['data']['individual']
        if country.upper() == 'HKG':
            return cls.HKG_RESULT['result']['data']['Shareholders']['data']['details']
        return []