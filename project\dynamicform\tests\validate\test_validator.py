from datetime import datetime

from dateutil.relativedelta import relativedelta
from django.test import SimpleTestCase

from dynamicform.validator.validator import validate

PRINT_RESULT = False


class ValidatorTest(SimpleTestCase):
    """ Example of cases payload
    cases = [
            ["value", "rule"],
    ]
    """

    def __assert(self, cases, data, assert_pass=True, merge_data_none=True):
        rules = {}
        i = 1
        for value, rule in cases:
            name = f'case-{i}'
            if value is not None or merge_data_none:
                data[name] = value
            rules[name] = rule
            i = i + 1

        errors = validate(data, rules)
        if PRINT_RESULT:
            print('errors', errors)

        if assert_pass:
            msg = f'case {list(errors.keys())} fail'
            self.assertTrue(len(errors) <= 0, msg)
        else:
            keys = list(set(rules.keys()).difference(set(errors.keys())))
            msg = f'case {keys} fail'
            self.assertTrue(len(errors) == len(rules), msg)

    def __assert_pass(self, cases, data, merge_data_none=True):
        self.__assert(cases, data, assert_pass=True, merge_data_none=merge_data_none)

    def __assert_fail(self, cases, data, merge_data_none=True):
        self.__assert(cases, data, assert_pass=False, merge_data_none=merge_data_none)

    def test_validate_regex_rules_pass(self):
        cases = [
            ['Kiyotaka', 'regex:/^[a-zก-๏เ-์. ]+$/i'],
            ['Kiyotaka ', 'regex:/^[a-zก-๏เ-์. ]+$/i'],
            ['kiyotaka', 'regex:/^[a-zก-๏เ-์.]+$'],
            ['Kiyotaka Ayanokoji', 'regex:/^[a-zก-๏เ-์. ]+$/i'],
            ['คิโยทากะ ฮะยาโนะโคจิ', 'regex:/^[a-zก-๏เ-์. ]+$/i'],
            ['0*********', 'regex:/^[0-9]+$/i'],
            ['0*********123', 'regex:/^\\d{13}$/'],
            ['*********4', 'regex:/^\\d{9}\\d?$/'],
            # TODO: ['*********4', 'regex:/^\\d{9,10}$/'],
        ]
        data = {}
        self.__assert_pass(cases, data)

    def test_validate_regex_rules_not_pass(self):
        cases = [
            ['0*********', 'regex:/^[a-zก-๏เ-์. ]+$/i'],
            ['Kiyotaka !', 'regex:/^[a-zก-๏เ-์. ]+$/i'],
            ['Kiyotaka', 'regex:/^[a-zก-๏เ-์.]+$'],
            ['Kiyotaka Ayanokoji', 'regex:/^[a-zก-๏เ-์.]+$/i'],
            ['綾小路 清隆', 'regex:/^[a-zก-๏เ-์. ]+$/i'],
            ['Kiyotaka', 'regex:/^[0-9]+$/i'],
            ['0*********12', 'regex:/^\\d{13}$/'],
        ]
        data = {}
        self.__assert_fail(cases, data)

    def test_validate_between_number_rules_pass(self):
        between_number_rule_1_10 = 'between_number:1,10'
        cases = [
            ['1', between_number_rule_1_10],
            [1, between_number_rule_1_10],
            [5, between_number_rule_1_10],
            ['2.7', between_number_rule_1_10],
            [3.5, between_number_rule_1_10],
            ['10', between_number_rule_1_10],
        ]
        data = {}
        self.__assert_pass(cases, data)

    def test_validate_between_number_rules_not_pass(self):
        between_number_rule_1_10 = 'between_number:1,10'
        cases = [
            ['0', between_number_rule_1_10],
            [0, between_number_rule_1_10],
            [11, between_number_rule_1_10],
            ['12.7', between_number_rule_1_10],
            [0.5, between_number_rule_1_10],
            ['10.1', between_number_rule_1_10],
        ]
        data = {}
        self.__assert_fail(cases, data)

    def test_validate_required_if_condition_pass(self):
        cases = [
            ['name', 'required_if_condition:value1,<,2'],
            ['name', 'required_if_condition:value1,<,2,value1,>,0'],
            ['name', 'required_if_condition:value1,<=,2,value2,>=,1'],
            ['name', 'required_if_condition:value1,<,2,value2,>,1,value3,=,2'],
            ['name', 'required_if_condition:value1,<,2,value2,>,1,value3,=,2'],
            [None, 'required_if_condition:value1,<=,1,value2,>=,2,value3,=,3'],
            [None, 'required_if_condition:value4,!=,1'],
        ]
        data = {
            'value1': 1,
            'value2': 2,
            'value3': '2',
        }
        self.__assert_pass(cases, data)

    def test_validate_required_if_condition_not_pass(self):
        cases = [
            [None, 'required_if_condition:value1,=,1'],
            [None, 'required_if_condition:value1,<,2'],
            [None, 'required_if_condition:value1,<=,1'],
            [None, 'required_if_condition:value1,>,0'],
            [None, 'required_if_condition:value2,>=,2'],
            [None, 'required_if_condition:value3,!=,1'],
        ]
        data = {
            'value1': 1,
            'value2': 2,
            'value3': '2',
        }
        self.__assert_fail(cases, data, merge_data_none=False)

    def test_validate_pass_checksum_pass(self):
        cases = [
            ['1509901739962', 'pass_checksum'],
            ['1101400299681', 'pass_checksum'],
        ]
        data = {}
        self.__assert_pass(cases, data)

    def test_validate_different_pass(self):
        cases = [
            ['value_1', 'different:value1'],
            [2, 'different:value2'],
            ['value_3', 'different:value3'],
        ]
        data = {
            'value1': 'value 1',
            'value2': 22,
        }
        self.__assert_pass(cases, data)

    def test_validate_different_not_pass(self):
        cases = [
            ['value_1', 'different:value1'],
            [2, 'different:value2'],
        ]
        data = {
            'value1': 'value_1',
            'value2': 2,
        }
        self.__assert_fail(cases, data)

    def test_validate_numeric_pass(self):
        cases = [
            [123, 'numeric'],
            [0, 'numeric'],
            [-123, 'numeric'],
            [123.45, 'numeric'],
            [-123.45, 'numeric'],
            ['123', 'numeric'],
            ['123.45', 'numeric'],
            ['-123', 'numeric'],
            ['-123.45', 'numeric'],
            ['0', 'numeric'],
        ]
        data = {}
        self.__assert_pass(cases, data)

    def test_validate_numeric_not_pass(self):
        cases = [
            ['abc', 'numeric'],
            # [None, 'numeric'], None is not present, so the validator will not validate this.
            [True, 'numeric'],
            # ['', 'numeric'], empty string is not present, so the validator will not validate this.
            ['กขค', 'numeric'],
            ['1+1', 'numeric'],
            ['+', 'numeric'],
        ]
        data = {}
        self.__assert_fail(cases, data)

    def test_validate_is_rule_pass(self):
        cases = [
            ['abc', 'is:abc'],
            [True, 'is:True'],
            [False, 'is:False'],
            [False, 'is:False'],
            [1.3, 'is:1.3'],
            [True, 'is:true'],
            [False, 'is:false'],
            # [None, 'is:None'],
        ]
        data = {}
        self.__assert_pass(cases, data)

    def test_validate_is_rule_not_pass(self):
        cases = [
            ['abc', 'is:abcd'],
            [True, 'is:1'],
            [False, 'is:0'],
            [1.3, 'is:1.30'],
            # [None, 'is:None'],
        ]
        data = {}
        self.__assert_fail(cases, data)

    def test_validate_not_in_rule_pass(self):
        cases = [
            ['def', 'not_in:abc'],
            [False, 'not_in:True'],
            [True, 'not_in:False'],
            [False, 'not_in:true'],
            [True, 'not_in:false'],
            [1.5, 'not_in:1.3'],
        ]
        data = {}
        self.__assert_pass(cases, data)

    def test_validate_not_in_rule_not_pass(self):
        cases = [
            ['abc', 'not_in:abc'],
            [True, 'not_in:True'],
            [False, 'not_in:False'],
            [1.3, 'not_in:1.3'],
            [True, 'not_in:true'],
            [False, 'not_in:false'],
        ]
        data = {}
        self.__assert_fail(cases, data)

    def test_validate_required_with_all_equal_value_rule_pass(self):
        cases = [
            [1, 'required_with_all_equal_value:a1,true,a2,false,a3,Answer'],
            [1, 'required_with_all_equal_value:a1,true'],
            [None, 'required_with_all_equal_value:a1,true,a2,true'],
            [None, 'required_with_all_equal_value:a1,true,a2,true,a3,No-answer'],
            [None, 'required_with_all_equal_value:a1,false'],
            [1, 'required_with_all_equal_value:a1,'],
            [1, 'required_with_all_equal_value:a3'],
            [None, 'required_with_all_equal_value:a4,'],
            [None, 'required_with_all_equal_value:a4'],
            [None, 'required_with_all_equal_value:a5.b_1,b.1'],
        ]
        data = {
            'a1': True,
            'a2': False,
            'a3': 'Answer',
            'a5': {
                'b_1': 'not_required',
            },
        }
        self.__assert_pass(cases, data)

    def test_validate_required_with_all_equal_value_rule_not_pass(self):
        cases = [
            [None, 'required_with_all_equal_value:a1,true,a2,false,a3,Answer'],
            [None, 'required_with_all_equal_value:a1,True,a2,False'],
            [None, 'required_with_all_equal_value:a1,true,a2,'],
            [None, 'required_with_all_equal_value:a1,true,a2,,a3,'],
            [None, 'required_with_all_equal_value:a3'],
            [None, 'required_with_all_equal_value:a5.b_1,b.2'],
        ]
        data = {
            'a1': True,
            'a2': False,
            'a3': 'Answer',
            'a5': {
                'b_1': 'b.2',
            },
        }
        self.__assert_fail(cases, data)

    def test_validate_datetime_before_rule_pass(self):
        ytd = datetime.now() + relativedelta(days=-1)
        ytd_str = ytd.strftime('%Y-%m-%d')

        cases = [
            [f'{ytd_str}', 'datetime_before'],
            ['2022-02-28', 'datetime_before:2022-03-01'],
            ['2019-12-31 12:00:00', 'date_time:%Y-%m-%d %H:%M:%S|datetime_before:2019-12-31 13:00:00'],
            ['1991-02-28', 'datetime_before:1993-02-28,-1'],
            ['1991-02-28', 'datetime_before:1992-02-28,1'],
            ['1991-02-28', 'datetime_before:1992-02-28,,-11,-5'],
            ['1991-02-28', 'datetime_before:,-5'],
            ['1991-02-28', 'datetime_before:a1,,,-5'],
            ['2019-12-31 12:00:00', 'date_time:%Y-%m-%d %H:%M:%S|datetime_before:a2,,,,1'],
            ['2019-12-31 12:00:00', 'date_time:%Y-%m-%d %H:%M:%S|datetime_before:a3,,,,-1'],
        ]
        data = {
            'a1': '1991-03-06',
            'a2': '2019-12-31 12:00:00',
            'a3': '2019-12-31 13:00:01',
        }
        self.__assert_pass(cases, data)

    def test_validate_datetime_before_rule_fail(self):
        tmr = datetime.now() + relativedelta(days=1)
        tmr_str = tmr.strftime('%Y-%m-%d')

        last_year = datetime.now() + relativedelta(years=-1, days=1)
        last_year_str = last_year.strftime('%Y-%m-%d')

        cases = [
            [f'{tmr_str}', 'datetime_before'],
            ['2022-02-28', 'datetime_before:2022-02-28'],
            ['2019-12-31 12:00:00', 'date_time:%Y-%m-%d %H:%M:%S|datetime_before:2019-12-31 12:00:00'],
            ['1991-02-28', 'datetime_before:1992-02-28,-1'],
            ['1991-02-28', 'datetime_before:1990-02-28,1'],
            ['1991-02-28', 'datetime_before:1991-12-28,,-11,-5'],
            [f'{last_year_str}', 'datetime_before:,-1'],
            ['1991-02-28', 'datetime_before:a1,,,-5'],
            ['2019-12-31 12:00:00', 'date_time:%Y-%m-%d %H:%M:%S|datetime_before:a2,,,,1'],
            ['2019-12-31 12:00:00', 'date_time:%Y-%m-%d %H:%M:%S|datetime_before:a3,,,,-1'],
        ]
        data = {
            'a1': '1991-02-23',
            'a2': '2019-12-31 11:00:00',
            'a3': '2019-12-31 12:59:00',
        }
        self.__assert_fail(cases, data)

    def test_validate_datetime_after_rule_pass(self):
        tmr = datetime.now() + relativedelta(days=1)
        tmr_str = tmr.strftime('%Y-%m-%d')

        today = datetime.now()
        today_str = today.strftime('%Y-%m-%d')

        next_5_y = datetime.now() + relativedelta(years=5, days=1)
        next_5_y_str = next_5_y.strftime('%Y-%m-%d')

        cases = [
            [f'{tmr_str}', 'datetime_after'],
            [f'{tmr_str}', f'datetime_after:{today_str}'],
            ['2019-12-31 12:00:00', 'date_time:%Y-%m-%d %H:%M:%S|datetime_after:2019-12-31 11:00:00'],
            ['1991-02-28', 'datetime_after:1991-02-28,-1'],
            ['1991-02-28', 'datetime_after:1990-02-27,1'],
            ['1991-02-28', 'datetime_after:1992-01-28,,-11,-5'],
            [f'{next_5_y_str}', 'datetime_after:,-5'],
            ['1991-03-01', 'datetime_after:a1,,,-5'],
            ['2019-12-31 12:00:00', 'date_time:%Y-%m-%d %H:%M:%S|datetime_after:a2,,,,1'],
            ['2019-12-31 12:00:00', 'date_time:%Y-%m-%d %H:%M:%S|datetime_after:a3,,,,-1'],
        ]
        data = {
            'a1': '1991-03-05',
            'a2': '2019-12-31 10:00:00',
            'a3': '2019-12-31 12:00:00',
        }
        self.__assert_pass(cases, data)

    def test_validate_datetime_after_rule_fail(self):
        tmr = datetime.now() + relativedelta(days=1)
        tmr_str = tmr.strftime('%Y-%m-%d')

        today = datetime.now()
        today_str = today.strftime('%Y-%m-%d')

        cases = [
            [f'{today_str}', 'datetime_after'],
            [f'{today_str}', f'datetime_after:{tmr_str}'],
            ['2019-12-31 12:00:00', 'date_time:%Y-%m-%d %H:%M:%S|datetime_after:2019-12-31 12:00:00'],
            ['1991-01-02', 'datetime_after:1992-01-02,-1'],
            ['1991-01-02', 'datetime_after:1991-01-01,1'],
            ['1991-03-01', 'datetime_after:1991-04-02,,-1,-1'],
            [f'{today_str}', 'datetime_after:,+1'],
            ['1991-03-05', 'datetime_after:a1,,,-5'],
            ['2019-12-31 12:00:00', 'date_time:%Y-%m-%d %H:%M:%S|datetime_after:a2,,,,1'],
            ['2019-12-31 12:00:00', 'date_time:%Y-%m-%d %H:%M:%S|datetime_after:a3,,,,-1'],
        ]
        data = {
            'a1': '1991-03-10',
            'a2': '2019-12-31 11:00:00',
            'a3': '2019-12-31 13:00:00',
        }
        self.__assert_fail(cases, data)

    def test_validate_business_id_pass(self):
        cases = [
            ['*********0123', 'business_id:th_brn'],
            ['*********0123', 'business_id:jp_bcn'],
            ['1234567', 'business_id:hk_brn'],
            ['12345678', 'business_id:hk_crn'],
            ['1000008-M', 'business_id:my_crn_old'],
            ['1000008M', 'business_id:my_crn_old'],
            ['201001012084', 'business_id:my_crn'],
            ['53480099U', 'business_id:sg_uen_a'],
            ['201712177T', 'business_id:sg_uen_b'],
            ['T09LP0001B', 'business_id:sg_uen_c'],
            ['AC12345678', 'business_id:ph_brn'],
            ['*********', 'business_id:au_acn'],
            ['*********', 'business_id:no_country'],
            ['', 'business_id:th_brn'],
        ]
        data = {
            'th_brn': 'THA',
            'jp_bcn': 'JPN',
            'hk_brn': 'HKG',
            'hk_crn': 'HKG',
            'my_crn_old': 'MYS',
            'my_crn': 'MYS',
            'sg_uen_a': 'SGP',
            'sg_uen_b': 'SGP',
            'sg_uen_c': 'SGP',
            'ph_brn': 'PHL',
            'au_acn': 'AUS',
        }
        self.__assert_pass(cases, data)

    def test_validate_business_id_fail(self):
        cases = [
            ['*********012', 'business_id:th_brn'],
            ['*********012', 'business_id:jp_bcn'],
            ['123456', 'business_id:hk_brn'],
            ['*********', 'business_id:hk_crn'],
            ['1000008-MM', 'business_id:my_crn_old'],
            ['1000008MM', 'business_id:my_crn_old'],
            ['300001012084', 'business_id:my_crn'],
            ['123409120840', 'business_id:my_crn'],
            ['*********', 'business_id:sg_uen_a'],
            ['999912177T', 'business_id:sg_uen_b'],
            ['T99LP0001B', 'business_id:sg_uen_c'],
            ['ABC1234567', 'business_id:ph_brn'],
            ['ABC123456', 'business_id:au_acn'],
            ['', 'business_id:th_brn|required'],
        ]
        data = {
            'th_brn': 'THA',
            'jp_bcn': 'JPN',
            'hk_brn': 'HKG',
            'hk_crn': 'HKG',
            'my_crn_old': 'MYS',
            'my_crn': 'MYS',
            'sg_uen_a': 'SGP',
            'sg_uen_b': 'SGP',
            'sg_uen_c': 'SGP',
            'ph_brn': 'PHL',
            'au_acn': 'AUS',
        }
        self.__assert_fail(cases, data)
