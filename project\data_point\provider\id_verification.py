from datetime import datetime

from data_point.base import EKYCBase
from workspace.events import ALL_EKYC_DOCUMENT_ITEM_TYPES
from dateutil.parser import parse
from dateutil.relativedelta import relativedelta
from django.conf import settings
from pydash import get, find

EKYC_FRONT_CARD_MAX_ATTEMPT = settings.EKYC_FRONT_CARD_MAX_ATTEMPT

DATAPOINT_OPTIONS = {
    'is_id_document': {
        'label': 'The image taken is an ID Document',
        'type': 'boolean',
    },
    'is_no_face_found': {
        'label': 'ID Document does not have face on it.',
        'type': 'boolean',
    },
    'is_expired': {
        'label': 'ID Document is expired',
        'type': 'boolean',
    },
    'is_misaligned': {
        'label': 'ID Document image is not horizontally aligned.',
        'type': 'boolean',
    },
    'is_fraud_detected': {
        'label': 'ID Document is not physically present or photoshopped',
        'type': 'boolean',
    },
    'is_poor_quality': {
        'label': 'The quality of ID Document image',
        'type': 'boolean',
    },
    'issue_date': {
        'label': 'Check if the issue date match the date condition',
        'type': 'date',
    },
    'issued_country': {
        'label': 'The issued countries of the ID Document match with the following in the list.',
        'type': 'string',
    },
    'is_supported_country': {
        'label': 'ID Document is issued by the list of countries defined in the builder',
        'type': 'boolean',
    },
    'age': {
        'label': 'Age extracted from the ID Document',
        'type': 'number',
    },
    'id_type': {
        'label': 'Type of ID Document',
        'type': 'string',
    },
    'attempts_count': {
        'label': 'The number of ID Document attempts',
        'type': 'number',
    },
    'fail_attempts_count': {
        'label': 'The number of times the selfie attempts failed in the form.',
        'type': 'number',
    },
    'fraud_attempts_count': {
        'label': 'The number of times the selfie fail liveness detection in the form.',
        'type': 'number',
    },
    'is_ever_hit_max_attempt': {
        'label': 'Has the device ever hit max attempts',
        'type': 'boolean',
    },
}


class IDVerification(EKYCBase):
    name = 'id_verification'
    title = 'ID Verification'
    sub_title = ''
    description = 'Global document ID verification for 10,000+ document types and 150+ countries in real-time enhance customer authentication and safeguard your business against identity fraud.'
    icon = ''
    option_icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/uppass_verification_services_36/ID-Document-Verification_36.png'
    option_icon_name = 'lucide:credit-card'
    data_point_opitons = DATAPOINT_OPTIONS
    required_item_types = [ALL_EKYC_DOCUMENT_ITEM_TYPES]
    required_item_types_auto_connect = True

    def get_data_result(self, values_list=[], **kwargs):
        # fmt: off
        result = {}
        ekyc = self.get_ekyc()
        form = self.context.get('form')
        
        if not ekyc.document:
            return {}
        
        backend_schema = form.backend_schema
        document_result = ekyc.document.result_response
        ocr_result = document_result.get('ocr', {})
        data_result = get(document_result, 'data.result', {})
        document_country_code = ocr_result.get('address_country_code', '')
        document_type = document_result.get('document_type')
        issue_date = ocr_result.get('date_of_issue_formatted')
        available_country = self.get_available_country(backend_schema)
        attemps_count_result = self._get_attempts_count(ekyc)
        result['is_id_document'] = get(data_result, 'ocr.status')
        result['is_no_face_found'] = not get(data_result, 'face.status', True)
        result['is_expired'] = not get(data_result, 'expiry.status', True)
        result['is_misaligned'] = not get(data_result, 'orientation.status', True)
        result['is_fraud_detected'] = not get(data_result, 'warning.status', True)
        result['is_poor_quality'] = not get(data_result, 'image_quality.status', True)
        result['issue_date'] = issue_date
        result['issued_country'] = available_country
        result['is_supported_country'] = self.get_is_support_country(document_country_code, available_country)
        result['age'] = self.get_age(ocr_result)
        result['id_type'] = self.get_document_type(document_type)
        result['attempts_count'] = attemps_count_result['attempts_count']
        result['fail_attempts_count'] = attemps_count_result['fail_attempts_count']
        result['fraud_attempts_count'] = attemps_count_result['fraud_attempts_count']
        result['is_ever_hit_max_attempt'] = attemps_count_result['is_ever_hit_max_attempt']
        # fmt: on

        return result

    def _get_attempts_count(self, ekyc):
        # fmt: off
        result = {
            'attempts_count': [],
            'fail_attempts_count': [],
            'success_attempts_count': [],
            'fraud_attempts_count': [],
            'is_ever_hit_max_attempt': '',
        }
        max_attempt = self.form.get_settings(
            path='ekyc.front_card.max_attempt',
            default=EKYC_FRONT_CARD_MAX_ATTEMPT,
            secure=True,
        )
        frontcard_set = ekyc.frontcard_set.only(
            'result_response',
            'is_success',
        )
        attempts_count = 0
        fail_attempts_count = 0
        success_attempts_count = 0

        for i in frontcard_set:
            result_response = i.result_response
            status = i.is_success
            fraud_detection_status = get(result_response, 'data.result.warning.status', True)

            result['fraud_attempts_count'].append(fraud_detection_status)
            attempts_count += 1

            if status is True:
                success_attempts_count += 1
            else:
                fail_attempts_count += 1

        result['attempts_count'] = attempts_count
        result['fail_attempts_count'] = fail_attempts_count
        result['success_attempts_count'] = success_attempts_count
        result['fraud_attempts_count'] = result['fraud_attempts_count'].count(False)

        if (
            result['attempts_count'] == max_attempt
            and result['success_attempts_count'] == 0
        ):
            result['is_ever_hit_max_attempt'] = True
        else:
            result['is_ever_hit_max_attempt'] = False
        # fmt: on

        return result

    def get_available_country(self, backend_schema: dict):
        # fmt: off
        visible_items_type = backend_schema.get('visible_items_type', {})
        ekyc_document = find(visible_items_type, lambda v, k: k in ['Ekyc.Document', 'Ekyc.DocumentVNext'])
        ekyc_document_country_key = get(ekyc_document, 'ekyc_document.fields.country', '')
        available_country = get(backend_schema, f'items.{ekyc_document_country_key}.enum_final_filter_includes', [])
        # fmt: on

        return available_country

    def get_is_support_country(
        self,
        document_country_code: str,
        available_country: list,
    ):
        if document_country_code.upper() in available_country:
            return True
        else:
            return False

    def get_age(self, ocr_data: dict):
        current_datetime = datetime.now().date()
        date_of_birth = ocr_data.get('date_of_birth_formatted')

        try:
            datetime_obj = parse(date_of_birth, ignoretz=True)
            result = relativedelta(current_datetime, datetime_obj).years
        except Exception:
            result = None

        return result

    def get_document_type(self, document_type: str) -> str:
        lookup_table = {
            'front_card': 'National ID',
            'passport': 'Passport',
            'driver_license': 'Driver License',
        }

        return lookup_table.get(document_type)
