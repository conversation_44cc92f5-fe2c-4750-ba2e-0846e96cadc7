from crequest.middleware import CrequestMiddleware
from django.utils import timezone
from datetime import timedelta
from dynamicform.exceptions import AppliedformTokenIsInvalid
import jwt

SECURITY_URL_TOKEN_QUERY_STRING = 't'


class SecurityURL:

    @property
    def is_enable_security_url(self):
        return self.get_form_settings('secure_url.enable', False)
        
    def jwt_url_token_encode(self):
        if not self.is_enable_security_url:
            return None

        exp_in_secs = self.get_form_settings('secure_url.exp_in_secs', 60)
        iat = timezone.now()
        exp = iat + timedelta(seconds=int(exp_in_secs))
        jwt_kwargs_encode = self.get_form_settings('secure_url.jwt_kwargs.encode', {}, secure=False)
        key = jwt_kwargs_encode.pop('key', self.form.secret_key)
        algorithm = jwt_kwargs_encode.pop('algorithm', 'HS256')
        payload = {
            'exp': exp,
            'iat': iat,
            'applied_form_slug': self.slug
        }

        return jwt.encode(payload,
            key=key,
            algorithm=algorithm,
            **jwt_kwargs_encode
        )

    def jwt_url_token_decode(self, token):
        enable = self.get_form_settings('secure_url.enable', False)
        if not enable:
            return None

        jwt_kwargs_decode = self.get_form_settings('secure_url.jwt_kwargs.decode', {}, secure=False)
        key = jwt_kwargs_decode.pop('key', self.form.secret_key)
        algorithms = jwt_kwargs_decode.pop('algorithms', ['HS256'])
        options = {'require': ['exp', 'applied_form_slug']}

        try:
            payload = jwt.decode(
                token,
                key=key,
                algorithms=algorithms,
                options=options,
                **jwt_kwargs_decode
            )
        except Exception as e:
            raise AppliedformTokenIsInvalid()

        return payload

    @property
    def url_token_query_string(self):
        if not self.is_my_form():
            return ''
            
        token = self.jwt_url_token_encode()
        if token:
            return f'{SECURITY_URL_TOKEN_QUERY_STRING}={token}'
        return ''
    
    def is_valid_url_token(self, request=None):
        if not request:
            request = CrequestMiddleware.get_request()
            
        if not self.is_enable_security_url:
            self.set_can_view_info()
            return True # NOSONAR

        token = request.GET.get(SECURITY_URL_TOKEN_QUERY_STRING)
        payload = self.jwt_url_token_decode(token)
        applied_form_slug = payload.get('applied_form_slug')
        is_valid = applied_form_slug == self.slug
        if not is_valid:
            raise AppliedformTokenIsInvalid()
        
        self.set_can_view_info()
        return is_valid
