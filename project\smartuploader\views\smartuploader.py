from django.utils.translation import gettext_lazy as _
from rest_framework import viewsets, filters, status

from workspace.models import Workspace, Role

from rest_framework.decorators import action
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response

from dynamicform.models import AppliedForm, Form

from smartuploader.models.smartuploader import SmartUploader

from smartuploader.serializer import SmartUploaderCallbackSerializer, SmartUploaderSerializer
from smartuploader.types import ResultExtractionResponse

from project.custom_logger import logger

ACTION_CALLBACK_SMART_UPLOADER_SAVE_RESULT = "callback_smart_uploader_save_result"
ACTION_CALLBACK_SMART_UPLOADER_SAVE_ANSWERS = "callback_smart_uploader_save_answers"
ACTION_CALLBACK_SMART_UPLOADER_TRIGGER_DECISION_FLOW = "callback_smart_uploader_trigger_decision_flow"


class SmartUploaderViewSet(viewsets.ModelViewSet):
    queryset = SmartUploader.objects.all()
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ["id", "created_at"]
    ordering = ["-created_at"]

    def get_authenticators(self):
        if self.request.method in ["POST"]:  # callback
            return []

        return super().get_authenticators()

    def get_permissions(self):
        if self.action in ["callback"]:
            return [AllowAny()]
        if self.action in ["update"]:
            return [IsAuthenticated()]

        return super().get_permissions()

    def get_serializer_class(self):
        if self.action in ["callback"]:
            return None  # will handle in method

        return SmartUploaderSerializer

    def perform_update(self, serializer):
        serializer.save()
        self.updated_smart_uploader_instance = serializer.instance

    def update(self, request, *args, **kwargs):
        try:
            smart_uploader: SmartUploader = self.get_object()
            applied_form = smart_uploader.applied_form

            # Validate Workspace
            form = applied_form.form
            workspace: Workspace = form.form_of.workspace
            if not workspace.is_allow_member(request.user, roles=[Role.ADMIN, Role.EDITOR]):
                return Response("Not allowed", status=status.HTTP_403_FORBIDDEN)
        
            # update answers in smart uploader
            response = super().update(request, *args, **kwargs)

            # Application not found and cannot auto save answers
            if not applied_form:
                return Response("Cannot auto save answers", status=status.HTTP_400_BAD_REQUEST)

            # update into auto-gen answers
            updated_smart_uploader: SmartUploader = self.updated_smart_uploader_instance
            applied_form.smart_uploader_auto_save_answers(smart_uploader=updated_smart_uploader)
            applied_form.log(action=ACTION_CALLBACK_SMART_UPLOADER_SAVE_ANSWERS, is_fail=False)
            return response
        except SmartUploader.DoesNotExist:
            return Response("Not found", status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.info(f"{ACTION_CALLBACK_SMART_UPLOADER_SAVE_ANSWERS}: {str(e)}")
            applied_form.log(action=ACTION_CALLBACK_SMART_UPLOADER_SAVE_ANSWERS, is_fail=True, message=str(e))
            return Response(e, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=["POST"])
    def callback(self, request, *args, **kwargs):

        # Validate Input
        serializer = SmartUploaderCallbackSerializer(data=request.data)
        is_valid = serializer.is_valid(raise_exception=False)
        if not is_valid:
            logger.info("SmartUploader Callback serializer errors:", serializer.errors)
            return Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        # data: ResultExtractionResponse = serializer.validated_data.get("result")
        request_id: str = serializer.validated_data.get("request_id")

        # Get SmartUploader
        smart_uploader: SmartUploader
        applied_form: AppliedForm | None
        try:
            smart_uploader = SmartUploader.objects.get(request_id=request_id)
            applied_form = smart_uploader.applied_form
        except SmartUploader.DoesNotExist:
            logger.info("SmartUploader Callback: Not found")
            return Response("Not found", status=status.HTTP_404_NOT_FOUND)

        # Save result
        try:
            smart_uploader.save_result(serializer.validated_data)
            if applied_form:
                applied_form.log(action=ACTION_CALLBACK_SMART_UPLOADER_SAVE_RESULT, is_fail=False)
        except Exception as e:
            logger.info(f"{ACTION_CALLBACK_SMART_UPLOADER_SAVE_RESULT}: {str(e)}")
            if applied_form:
                applied_form.log(action=ACTION_CALLBACK_SMART_UPLOADER_SAVE_RESULT, is_fail=True, message=str(e))
            return Response(e, status=status.HTTP_400_BAD_REQUEST)

        # Application not found (ex: Deleted)
        if not applied_form:
            return Response("ok, but application is not found", status=status.HTTP_200_OK)

        # Save answers
        try:
            applied_form.smart_uploader_auto_save_answers(smart_uploader=smart_uploader)
            applied_form.log(action=ACTION_CALLBACK_SMART_UPLOADER_SAVE_ANSWERS, is_fail=False)
        except Exception as e:
            logger.info(f"{ACTION_CALLBACK_SMART_UPLOADER_SAVE_ANSWERS}: {str(e)}")
            applied_form.log(action=ACTION_CALLBACK_SMART_UPLOADER_SAVE_ANSWERS, is_fail=True, message=str(e))
            return Response(e, status=status.HTTP_400_BAD_REQUEST)

        # Trigger Decision Flow
        try:
            applied_form.smart_uploader_trigger_decision_flow()
            applied_form.log(action=ACTION_CALLBACK_SMART_UPLOADER_TRIGGER_DECISION_FLOW, is_fail=False)
        except Exception as e:
            logger.info(f"{ACTION_CALLBACK_SMART_UPLOADER_TRIGGER_DECISION_FLOW}: {str(e)}")
            applied_form.log(action=ACTION_CALLBACK_SMART_UPLOADER_TRIGGER_DECISION_FLOW, is_fail=True, message=str(e))

        return Response("ok", status=status.HTTP_200_OK)
