import json
import logging
from datetime import datetime

import requests
from crequest.middleware import CrequestMiddleware
from django.conf import settings
from django.core.exceptions import FieldDoesNotExist
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.utils.translation import get_language
from django.utils.translation import gettext_lazy as _
from rest_framework import status, viewsets
from rest_framework.authentication import BasicAuthentication
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound, PermissionDenied
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from app.auth.authentication import APITokenAuthentication, TokenAuthentication
from base.cryptography.symmetric import AESCBC
from dynamicform.exceptions import AppliedformIsDisbled, AppliedformServiceUnavailable
from dynamicform.models import AnswerLog, AppliedForm, Form
from dynamicform.submodules.answer.serializer import AnswerLogSerializer
from dynamicform.submodules.appliedform.dynamicform import Dynamicform
from dynamicform.submodules.appliedform.serializer import (
    ApplicationFormNewSerializer,
    ApplicationFormResultSerializer,
    ApplicationFormSerializer,
)
from dynamicform.submodules.appliedform.util import visitor_ip_address
from dynamicform.submodules.form.helpers.permissions import IsWorkspaceAdmin, IsWorkspaceMember
from dynamicform.submodules.form.pagination import FormPagination
from workspace.helpers.permissions import (
    HasEditSubmissionDetailPermission,
    HasResendWebhookPermission,
    HasRunAndRerunDecisionFlowsPermission,
    HasViewSubmissionDetailPermission,
)

logger: logging.Logger = logging.getLogger(__name__)

ACTION_VIEW_SUBMISSION_DETAIL = [
    "list",
    "retrieve",
    'get_tracking_log_list',
    "report",
    "report_pdf",
    "report_answers",
    "report_answers_log",
    "applied_form_result",
]
ACTION_RESEND_WEBHOOK = [
    "hook_submit"
]
ACTION_RUN_AND_RERUN_DECISION_FLOWS = [
    "decision_flow_re_trigger"
]
ACTION_EDIT_SUBMISSION_DETAIL = [
    "update",
    "partial_update",
    "app_update_answers",
]
ACTION_NEED_PERMISSION = ACTION_VIEW_SUBMISSION_DETAIL + ACTION_RESEND_WEBHOOK + ACTION_EDIT_SUBMISSION_DETAIL


ALLOW_BASIC_AUTH = ["hook_submit", "decision_flow_trigger"]
DATABASE_MODEL_USE_DB = settings.DATABASE_MODEL_USE_DB.get("applied_form", {})

APP_NAME = settings.APP_NAME
APP_ENV = settings.APP_ENV
EVENT_TRACKING_ENDPOINT = settings.EVENT_TRACKING_ENDPOINT
EVENT_TRACKING_USERNAME = settings.EVENT_TRACKING_USERNAME
EVENT_TRACKING_PASSWORD = settings.EVENT_TRACKING_PASSWORD


class AppliedFormViewSet(viewsets.ModelViewSet):
    lookup_field = "slug"
    queryset = AppliedForm.objects.all()
    serializer_class = ApplicationFormSerializer
    pagination_class = FormPagination
    parent_lookup_kwargs = {"form": "form_form"}

    def get_authenticators(self):
        if self.action_map.get("post", None) in ALLOW_BASIC_AUTH:
            authentication_classes = [
                BasicAuthentication,
                APITokenAuthentication,
                TokenAuthentication
            ]
        else:
            authentication_classes = self.authentication_classes
        return [auth() for auth in authentication_classes]

    def get_permissions(self):
        if self.action in ACTION_VIEW_SUBMISSION_DETAIL:
            permission_classes = [IsAuthenticated, HasViewSubmissionDetailPermission]
        elif self.action in ACTION_RESEND_WEBHOOK:
            permission_classes = [IsAuthenticated, HasResendWebhookPermission]
        elif self.action in ACTION_EDIT_SUBMISSION_DETAIL:
            permission_classes = [IsAuthenticated, HasEditSubmissionDetailPermission]
        elif self.action in ACTION_RUN_AND_RERUN_DECISION_FLOWS:
            permission_classes = [IsAuthenticated, HasRunAndRerunDecisionFlowsPermission]
        else:
            permission_classes = self.permission_classes

        return [permission() for permission in permission_classes]

    def get_queryset(self):
        form_slug = self.kwargs['form_slug']
        applied_forms = AppliedForm.objects.filter(form__slug=form_slug, deleted_at__isnull=True)
        if self.action in ACTION_NEED_PERMISSION:
            user = self.request.user
            if user and user.is_api_token:
                applied_forms = applied_forms.filter(form__token__user=user)

        if self.action in ['list', 'info', 'trans']:
            use_db = DATABASE_MODEL_USE_DB.get('read', 'default')
            logger.info(f'get_queryset: read using {use_db}')
            return applied_forms.using(use_db)

        return applied_forms

    def filter_queryset(self, queryset):
        applied_forms = super(AppliedFormViewSet, self).filter_queryset(queryset)
        query = self.request.GET
        submitted = query.get("submitted", False)
        step = query.get("step", None)
        section = query.get("section", None)
        if submitted:
            applied_forms = applied_forms.filter(submitted_at__isnull=False)
        if step:
            applied_forms = applied_forms.filter(step=step)
        if section:
            applied_forms = applied_forms.filter(section=section)
        # =================================================
        try:
            # Resolve filters
            answers_filter = query.get("answers_filter", "[]")
            answers_filter = json.loads(answers_filter)
            answers_exclude = query.get("answers_exclude", "[]")
            answers_exclude = json.loads(answers_exclude)
            answers_filter_final = []
            answers_exclude_final = []
            for filter_item in answers_filter:
                value = json.dumps(filter_item[1])
                if value == "null":
                    answers_exclude_final.append(filter_item)
                else:
                    answers_filter_final.append(filter_item)
            for exclude_item in answers_exclude:
                value = json.dumps(exclude_item[1])
                if value == "null":
                    answers_filter_final.append(exclude_item)
                else:
                    answers_exclude_final.append(exclude_item)

            # INCLUDE filters
            for filter_item in answers_filter_final:
                field = filter_item[0]
                value = json.dumps(filter_item[1])

                if len(filter_item) == 3:
                    lookup_form_slug = filter_item[2]
                    query = {
                        "applicationappliedform__application__applicationappliedform__applied_form__answer__question__exact": field,
                        "applicationappliedform__application__applicationappliedform__applied_form__form__slug": lookup_form_slug,
                    }
                    if value != "null":
                        query[
                            "applicationappliedform__application__applicationappliedform__applied_form__answer___value__exact"
                        ] = value
                else:
                    query = {"answer__question__exact": field}
                    if value != "null":
                        query["answer___value__exact"] = value
                applied_forms = applied_forms.filter(**query)

            # EXCLUDE filters
            for exclude_item in answers_exclude_final:
                field = exclude_item[0]
                value = json.dumps(exclude_item[1])

                if len(exclude_item) == 3:
                    lookup_form_slug = exclude_item[2]
                    query = {
                        "applicationappliedform__application__applicationappliedform__applied_form__answer__question__exact": field,
                        "applicationappliedform__application__applicationappliedform__applied_form__form__slug": lookup_form_slug,
                    }
                    if value != "null":
                        query[
                            "applicationappliedform__application__applicationappliedform__applied_form__answer___value__exact"
                        ] = value
                else:
                    query = {"answer__question__exact": field}
                    if value != "null":
                        query["answer___value__exact"] = value
                applied_forms = applied_forms.exclude(**query)
        except Exception as e:
            print(e)
            return AppliedForm.objects.none()
        # =================================================
        # =================================================
        try:
            answers_search = query.get("answers_search", False)
            if answers_search:
                value = json.dumps(answers_search)
                applied_forms = applied_forms.filter(answer___value__icontains=value)
        except:
            return AppliedForm.objects.none()
        # =================================================

        order_by = query.get("order", False)
        desc = query.get("desc", False)
        try:
            AppliedForm._meta.get_field(order_by)
            if desc:
                order_by = "-" + order_by

            applied_forms = applied_forms.order_by(order_by)
        except FieldDoesNotExist:
            applied_forms = applied_forms.order_by("id")

        return applied_forms

    def get_object(self):
        if hasattr(self.request, 'applied_form') and self.request.applied_form:
            return self.request.applied_form
        
        return super(AppliedFormViewSet, self).get_object()
    
    def get_form(self):
        if hasattr(self.request, 'form') and self.request.form:
            return self.request.form
        
        return None

    def create(self, request, form_slug=None, *args, **kwargs):
        crequest = CrequestMiddleware.get_request()
        form = get_object_or_404(Form, slug=form_slug)

        if not form.backend_schema.get("allowed_guest", True):
            if not request.user or not form.users.filter(id=request.user.id).exists():
                raise PermissionDenied()
        serializer = ApplicationFormNewSerializer(data=request.data, context={"form": form, 'request': crequest})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=["GET"])
    def info(self, request, form_slug=None, slug=None):
        applied_form = self.get_object()
        
        form = self.get_form()
        if form is None:
            form = applied_form.form

        dynamicform = Dynamicform(
            applied_form,
            form,
            request=request
        )
        
        can_view = dynamicform.applied_form.can_view_info()
        if not can_view:
            return Response(dynamicform.applied_form.get_info_for_permission_deny())
        dynamicform.applied_form.set_can_view_info()
        
        info = dynamicform.get_info()
        
        to_log = {
            "type": "info_request_user_agent",
            "form_slug": form_slug,
            "slug": slug,
            "ua": self.request.META.get("HTTP_USER_AGENT"),
        }
        logger.info("info_request_user_agent", to_log)

        return Response(info)

    @action(detail=True, methods=["GET"])
    def report(self, request, form_slug=None, slug=None, **kwargs):
        applied_form = self.get_object()
        
        form = self.get_form()
        if form is None:
            form = applied_form.form

        dynamicform = Dynamicform(
            applied_form,
            form,
            request=request
        )

        if not dynamicform.applied_form.can_view_report():
            raise AppliedformIsDisbled()

        dynamicform.applied_form.set_can_view_report()
        dynamicform.application.user_log(section="Form", action="View", detail="View Form")
        report_data = dynamicform.get_report()
        return Response(report_data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["GET"], url_path="report/answers")
    def report_answers(self, request, form_slug=None, slug=None, **kwargs):
        _ = get_object_or_404(Form, slug=form_slug)
        applied_form = self.get_object()
        if not applied_form.can_view_report():
            raise AppliedformIsDisbled()

        applied_form.set_can_view_info()
        info = {
            "form_slug": applied_form.form.slug,
            "applied_form_slug": applied_form.slug,
            "answers": applied_form.get_submit_answers(),
        }
        return Response(info, status=status.HTTP_200_OK)

    @action(detail=True, methods=["GET"], url_path="report/answers/log")
    def report_answers_log(self, request, form_slug=None, slug=None, **kwargs):
        _ = get_object_or_404(Form, slug=form_slug)
        applied_form = self.get_object()
        if not applied_form.can_view_report():
            raise AppliedformIsDisbled()

        applied_form.set_can_view_info()
        logs = AnswerLog.objects.filter(applied_form_id=applied_form.id)

        page = self.paginate_queryset(logs)
        if page is not None:
            serializer = AnswerLogSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = AnswerLogSerializer(logs, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=["GET"], url_path="report/data")
    def report_data(self, request, form_slug=None, slug=None, **kwargs):
        applied_form = self.get_object()
        
        form = self.get_form()
        if form is None:
            form = applied_form.form

        
        dynamicform = Dynamicform(
            applied_form,
            form,
            request=request
        )

        if not dynamicform.applied_form.can_view_report():
            raise AppliedformIsDisbled()

        dynamicform.application.user_log(section="Form", action="View", detail="View Report Data")

        query = self.request.GET
        apps = query.get("apps", None)
        if apps:
            apps = str(apps).split(",")
        else:
            # answers form dynamicform is default
            apps = ["dynamicform"]

        report_data = dynamicform.get_report_data(filter_apps=apps)

        return Response(report_data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["POST"])
    def save(self, request, form_slug=None, slug=None, **kwargs):
        crequest = CrequestMiddleware.get_request()
        crequest.json_body = request.data
        applied_form = self.get_object()
        form = self.get_form()
        if not form:
            form = applied_form.form
        
        answers = request.data.get("answers", {})
        step = request.data.get("step", None)
        section = request.data.get("section", None)
        extra = request.data.get("extra", {})

        if not applied_form.can_update_info():
            content = str(_("Cannot update submitted form"))
            applied_form.log(
                step,
                section,
                action=applied_form.ACTION_REQUEST_SAVE,
                detail={"status": status.HTTP_403_FORBIDDEN, "content": content},
                is_fail=True,
            )
            return Response(content, status=status.HTTP_403_FORBIDDEN)

        dynamicform = Dynamicform(
            applied_form, 
            form,
            answers,
            step,
            section,
            extra
        )
        is_valid, errors = dynamicform.is_valid()
        
        # Pre save
        pre_save_result = dynamicform.pre_save()

        # Pre save app
        pre_save_app_result = dynamicform.pre_save_app()

        # Save
        invalid_questions = [*errors]

        dynamicform.save(
            invalid_questions=invalid_questions
        )
        dynamicform.save_applied_form_obj()

        if not is_valid:
            dynamicform.log(
                action=applied_form.ACTION_REQUEST_SAVE,
                detail={"status": status.HTTP_422_UNPROCESSABLE_ENTITY, "content": errors},
                is_fail=True,
                message=errors,
            )
            return Response(errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        # Post save
        post_save_result = dynamicform.post_save()

        result = pre_save_result or pre_save_app_result or post_save_result

        response_status = status.HTTP_200_OK
        dynamicform.log(
            action=applied_form.ACTION_REQUEST_SAVE,
            detail={
                "status": response_status,
            }
        )
        dynamicform.trigger_save_action()

        # ccc
        return Response(result, status=status.HTTP_200_OK)

    @action(detail=True, methods=["POST"])
    def submit(self, request, form_slug=None, slug=None, **kwargs):
        applied_form = self.get_object()

        answers = request.data.get("answers", {})
        step = request.data.get("step", None)
        section = request.data.get("section", None)
        extra = request.data.get("extra", {})

        if not applied_form.can_update_info():
            content = str(_("Cannot update submitted form"))
            applied_form.log(
                step=step,
                section=section,
                action=applied_form.ACTION_REQUEST_SUBMIT,
                detail={"status": status.HTTP_403_FORBIDDEN, "content": content},
                is_fail=True,
            )
            return Response(content, status=status.HTTP_403_FORBIDDEN)

        form = self.get_form()
        if not form:
            form = applied_form.form

        dynamicform = Dynamicform(
            applied_form, 
            form,
            answers,
            step,
            section,
            extra,
            is_submit=True
        )
        is_valid, errors = dynamicform.is_valid(caller="submit")

        if not is_valid:
            applied_form.log(
                step=step,
                section=section,
                action=applied_form.ACTION_REQUEST_SUBMIT,
                detail={"status": status.HTTP_422_UNPROCESSABLE_ENTITY, "content": errors},
                is_fail=True,
                message=errors,
            )
            return Response(errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        # Pre save
        pre_save_result = dynamicform.pre_save()

        # Save
        dynamicform.save()

        dynamicform.submit()
        
        # Post save
        dynamicform.post_save()

        dynamicform.save_applied_form_obj()

        response_status_200_OK = status.HTTP_200_OK
        dynamicform.log(
            action=applied_form.ACTION_REQUEST_SUBMIT,
            detail={
                "status": response_status_200_OK,
            }
        )

        dynamicform.trigger_submit_action()
        return Response(pre_save_result, status=response_status_200_OK)

    @action(detail=True, methods=["POST"])
    def send(self, request, form_slug=None, slug=None, **kwargs):
        applied_form = self.get_object()
        if applied_form.submitted_at is None:
            raise AppliedformServiceUnavailable(_("Cannot send unsubmitted form"))

        applied_form.submit_to_cloud()
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=["POST"], url_path="hook-submit")
    def hook_submit(self, request, form_slug=None, slug=None, **kwargs):
        applied_form = self.get_object()
        """
        ?event_trigger=webhook_event_trigger
        default event_trigger := submit_form
        """
        # if applied_form.submitted_at is None:
        #     raise AppliedformServiceUnavailable(_("Cannot send unsubmitted form"))
        query = request.GET
        query = query.dict()
        success_all = applied_form.hook_submit(query=query, data=request.data)
        if not success_all:
            return Response('The webhook response status is not success.', status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(status=status.HTTP_204_NO_CONTENT)

    def __get_application(self, applied_form):
        from dynamicform.models import Application

        application = Application.objects.filter(applicationappliedform__applied_form__id=applied_form.id).first()
        if not application:
            application = applied_form.create_application()
            application.set_first_applied_form(applied_form)

        return application

    def __get_dynamic_applied_form(self, application, form_slug):
        applicationappliedform = application.applicationappliedform_set.filter(
            applied_form__form__slug=form_slug
        ).first()
        if applicationappliedform:
            return applicationappliedform.applied_form

        from dynamicform.models import Form

        form = Form.objects.filter(slug=form_slug).first()
        if not form:
            return None

        applied_form = form.apply()
        application.add_applied_form(applied_form=applied_form)
        return applied_form

    @action(detail=True, methods=["POST"], url_path="update")
    def app_update_answers(self, request, form_slug=None, slug=None, **kwargs):
        applied_form = self.get_object()
        answers = request.data.get("answers", {})
        validate_all = request.data.get("validate_all", True)
        disable = []
        if not validate_all:
            disable = ["required", "required_if", "required_unless", "required_with", "required_without", "accepted"]
        is_valid, errors = applied_form.validate(flat_questions=answers, disable=disable, validate_all=validate_all)
        if not is_valid:
            return Response(errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        applied_form.save_upmapped_answers(answers)
        applied_form.application_updated_now()

        extra = request.data.get("extra", {})
        update_current_step_section = request.data.get("update_current_step_section", False)
        step = extra.get("step", None)
        section = extra.get("section", None)

        applied_form.log(
            step,
            section,
            action=applied_form.ACTION_UPDATE,
            is_fail=False,
            update_current_step_section=update_current_step_section,
        )

        if step and section:
            after_update_answers_actions_path = f"after_update_answers_actions.{step}.{section}"
            actions = applied_form.get_form_settings(after_update_answers_actions_path, [])
            applied_form.do_actions(actions=actions, step=step, section=section, raise_validation=True, data_applied_form_obj=True)

        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=["GET"])
    def trans(self, request, form_slug=None, slug=None, **kwargs):
        applied_form = self.get_object()
        trans = applied_form.get_trans()
        return Response(trans)

    @action(detail=True, methods=["POST"])
    def tracking(self, request, form_slug=None, slug=None, **kwargs):
        data = request.data
        if not request.session.session_key:
            request.session.create()
        response = requests.post(
            EVENT_TRACKING_ENDPOINT,
            auth=(EVENT_TRACKING_USERNAME, EVENT_TRACKING_PASSWORD),
            json={
                "applied_form_slug": slug,
                "form_slug": form_slug,
                "app_name": APP_NAME,
                "app_env": APP_ENV,
                "ip_address": visitor_ip_address(request),
                "session_id": request.session.session_key,
                "data": data,
            },
        )

        if response.ok:
            return Response("ok", status=response.status_code)
        else:
            return Response("not ok", status=response.status_code)

    @action(detail=True, methods=["GET"], url_path="get-tracking-summary")
    def get_tracking(self, request, form_slug=None, slug=None, **kwargs):
        applied_form = self.get_object()
        result = applied_form.get_event_tracking_summary()

        return Response(result)

    @action(detail=True, methods=["GET"], url_path="get-tracking-log-list")
    def get_tracking_log_list(self, request, form_slug=None, slug=None, **kwargs):
        applied_form = self.get_object()
        response = applied_form.get_event_tracking_log_list()
        try:
            result = response.json()
        except:
            print("get_tracking_log_list: result json error", response.text)
            result = {}
        return Response(result, status=response.status_code)

    @action(detail=True, methods=["GET"])
    def document(self, request, form_slug=None, slug=None, **kwargs):
        applied_form = self.get_object()
        can_view = applied_form.can_view_document()
        if not can_view:
            return Response(applied_form.get_info_for_permission_deny())

        query = request.GET
        query = query.dict()
        filename = query.pop("file", None)
        form_slug = query.pop("form", None)
        download = query.pop("download", 0)

        if not filename:
            return Response(status=status.HTTP_404_NOT_FOUND)

        if form_slug:
            related_form = applied_form.application.applicationappliedform_set.filter(
                applied_form__form__slug=form_slug
            )
            if related_form.exists():
                applied_form = related_form.first().applied_form

        response = applied_form.get_document(filename=filename, query=query)

        if download:
            response["Content-Disposition"] = f'attachment; filename="{filename}"'

        return response

    @action(detail=True, methods=["POST"], url_path="step-section")
    def save_step_section(self, request, form_slug=None, slug=None, **kwargs):
        applied_form = self.get_object()

        data = request.data
        step = data.get("step", None)
        section = data.get("section", None)
        if step and section:
            applied_form.log(step, section, action="save_step_section")

        return Response(request.data)

    @action(detail=True, methods=["POST"], url_path="decision-flow-trigger/(?P<event>[^/.]+)")
    def decision_flow_trigger(self, request, form_slug=None, slug=None, event=None, **kwargs):
        applied_form = self.get_object()
        url = applied_form.decision_flow_trigger(
            event=event
        )
        return Response({'url': url}, status=status.HTTP_200_OK)
    
    @action(detail=True, methods=["POST"], url_path="decision-flows/(?P<event>[^/.]+)/re-trigger")
    def decision_flow_re_trigger(self, request, form_slug=None, slug=None, event=None, **kwargs):
        from dynamicform.submodules.decisionflow.models import DecisionFlowPipeline
        applied_form = self.get_object()

        DecisionFlowPipeline.set_rerun_pipeline_for_applied_form_event(
            form_id=applied_form.form_id,
            applied_form_id=applied_form.id,
            event_name=event
        )

        return Response(status=status.HTTP_202_ACCEPTED)
    

    @action(methods=["get"], detail=True, url_path="report-pdf")
    def report_pdf(self, request, form_slug=None, slug=None, *args, **kwargs):
        applied_form = self.get_object()
        if not applied_form.can_view_report():
            raise AppliedformIsDisbled()

        endpoint = settings.EXPORT_PDF_API_ENDPOINT
        username = settings.EXPORT_PDF_API_USERNAME
        password = settings.EXPORT_PDF_API_PASSWORD

        remote_url = request.build_absolute_uri(f"/{get_language()}/dashboard/applications/{form_slug}/{slug}/pdf")
        remote_token = request.headers.get("Authorization", "").replace("Bearer ", "")

        headers = {"Authorization": requests.auth._basic_auth_str(username, password)}
        body = {
            "url": remote_url,
            "token": remote_token,
        }
        r = requests.post(
            endpoint,
            data=body,
            headers=headers,
        )

        if not status.is_success(r.status_code):
            try:
                err_response = r.json()
            except Exception:
                err_response = r.text
            return Response({ "data": err_response, "status": r.status_code }, status=400)
        
        applied_form.application.user_log(section="Form", action="View", detail="Get PDF Report")

        response = Response(
            content_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename={form_slug}-{slug}-{datetime.now().isoformat()}.pdf",
            },
        )
        response.content = r.content
        return response

    @action(detail=True, methods=["GET"], url_path="result")
    def applied_form_result(self, request, form_slug=None, slug=None, event=None, **kwargs):
        from ..models.appliedform import AppliedForm

        process_logs = [["start", str(datetime.now().isoformat())]]
        applied_form: AppliedForm = self.get_object()

        def __send_info_log(is_fail=False):
            if applied_form:
                action = "applied_form_result_get"
                applied_form.log(action=action, detail=process_logs, is_fail=is_fail)

        # Check permission
        process_logs.append(["check_permission", str(datetime.now().isoformat())])
        if not applied_form.can_view_report():
            # can_view_report = superadmin or workspace's member/api token
            process_logs.append(["error_permission", str(datetime.now().isoformat())])
            __send_info_log(True)
            raise NotFound()

        # Serialize
        process_logs.append(["serialize", str(datetime.now().isoformat())])
        serializer = ApplicationFormResultSerializer(instance=applied_form)
        secret_key_str = applied_form.get_form_settings(
            "encryption.aes_256_cbc_pkcs7_secret_key",
            default=None,
            secure=False,
        )

        if secret_key_str:
            process_logs.append(["encrypt", str(datetime.now().isoformat())])
            aes_cbc = AESCBC(secret_key_str)
            encrypted_result = aes_cbc.encrypt(json.dumps(serializer.data, ensure_ascii=False))

            process_logs.append(["completed", str(datetime.now().isoformat())])
            __send_info_log(False)
            return HttpResponse(encrypted_result, content_type="text/plain")

        process_logs.append(["completed", str(datetime.now().isoformat())])
        __send_info_log(False)
        return Response(serializer.data, status=status.HTTP_200_OK)
