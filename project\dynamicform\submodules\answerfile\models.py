from django.db import models
from django.conf import settings
from django.utils import timezone
from dynamicform.submodules.model import AutoNow, SoftDelete
import random
import string
from dynamicform.util import reverse


class AnswerFile(AutoNow, SoftDelete):
    answer = models.ForeignKey(
        'Answer',
        on_delete=models.SET_NULL,
        null=True
    )
    name = models.CharField(max_length=191, null=True, db_index=True)
    mime_type = models.CharField(max_length=255)
    detail = models.FileField(upload_to='dynamicform', max_length=191)
    password = models.CharField(max_length=191, null=True)

    def delete(self, *args, **kwargs):
        if hasattr(self.detail, 'delete'):
            self.detail.delete()
        super().delete(*args, **kwargs)

    @property
    def document_type(self):
        return False

    @property
    def item_url(self):
        pattern = {
            'form_slug': self.answer.applied_form.form.slug,
            'applied_form_slug': self.answer.applied_form.slug,
            'question': self.answer.question,
            'file_id': self.id,
            'file_name': self.name
        }

        return reverse('dynamicform:answerfile-item', **pattern)

    @staticmethod
    def get_name(name, applied_form):
        string_length = 5
        letters = string.ascii_lowercase
        ran_str = ''.join(random.choice(letters) for i in range(string_length)) #NOSONAR
        now = timezone.now().timestamp()
        now = hex(int(now))
        # len(now) ≈ 10 + len(ran_str) ≈ 5 + len(name) ≤ 50 ≈ total 65
        file_name = now + ran_str + name[-50:]
        file_path = '/'.join([settings.GS_UPLOAD_TO, applied_form.form.slug, applied_form.slug, file_name])
        return file_path

    def info(self, request=None, report=False, **kwargs): 
        result = self.submit_info(request=request, **kwargs)

        if self.answer.applied_form.can_do_update():
            result['delete'] = self.item_url

        return result

    def get_item_url(self, applied_form=None, form=None, question=None, **kwargs):
        if not applied_form or not form or not question:
            return self.item_url
        
        pattern = {
            'form_slug': form.slug,
            'applied_form_slug': applied_form.slug,
            'question': question,
            'file_id': self.id,
            'file_name': self.name
        }

        return reverse('dynamicform:answerfile-item', **pattern)
    
    def submit_info(self, request=None, **kwargs):
        return {
            'id': self.name,
            'preview': self.get_item_url(**kwargs),
            'type': self.content_type,
        }

    @property
    def preview_url(self):
        return self.detail.url

    @property
    def content_type(self):
        try:
            return self.detail.file.mime_type
        except:
            return self.mime_type

    @staticmethod
    def delete_deleted_files():
        files = AnswerFile.all_objects.filter(deleted_at__isnull=False)
        for file in files:
            file.detail.delete()
            file.hard_delete()