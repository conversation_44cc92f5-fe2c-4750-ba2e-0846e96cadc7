import inspect
import logging
from django.conf import settings
from pydash import get

from rest_framework import status
from data_point.services.base import BaseService
from data_point.exceptions import DataPointException, DataPointResponseException
from data_point.utils import ErrorMessage
from data_point.services.asia_verify.adapters.japan_adapter import JapanAdapter
from data_point.services.asia_verify.adapters.malaysia_adapter import MalaysiaAdapter
from data_point.services.asia_verify.adapters.singapore_adapter import SingaporeAdapter
from data_point.services.asia_verify.adapters.hongkong_adapter import HongkongAdapter
from data_point.services.asia_verify.post_request.australia_post_request import AustraliaPostRequest

logger: logging.Logger = logging.getLogger(__name__)


class AsiaVerifyApiV2(BaseService):
    DATA_POINT_ASIA_VERIFY_V2_HOST = settings.DATA_POINT_ASIA_VERIFY_V2_HOST
    DATA_POINT_ASIA_VERIFY_V2_AUTH = settings.DATA_POINT_ASIA_VERIFY_V2_AUTH
    DATA_POINT_ASIA_VERIFY_V2_SIGN = settings.DATA_POINT_ASIA_VERIFY_V2_SIGN
    
    COUNTRY_SUPPORT = ['JPN', 'MYS', 'SGP', 'HKG', 'PHL', 'AUS']
    
    def get_token(self) -> str:
        data_point_function_name = inspect.currentframe().f_code.co_name
        # Setup URL, header and request body
        url = f'{self.DATA_POINT_ASIA_VERIFY_V2_HOST}/v2/open/api/token/create'
        headers = {
            'Authorization': f'{self.DATA_POINT_ASIA_VERIFY_V2_AUTH}',
            'Sign': f'{self.DATA_POINT_ASIA_VERIFY_V2_SIGN}',
        }
        
        # Logging when request API
        logging_context = self.get_logging_context()
        data_point_log_info = {
            'url': url,
            'context': logging_context,
        }
        logger.info(f'Asia verify: API Request process', data_point_log_info)
        
        # Request API
        result = self._request(
            method='POST',
            url=url,
            headers=headers,
            data_point_function_name=data_point_function_name
        )
        
        # Get result from request
        response_data = result.get('result')
        status_code = result.get('status_code')
        response_code = get(response_data, 'code')
        
        # Logging after got response
        data_point_log_info["status_code"] = status_code
        data_point_log_info["code"] = response_code
        logger.info(
            f'Asia verify: Request success: HTTP Response {status_code}',
            data_point_log_info
        )
        
        # Raise DataPointResponseException when status is not 200
        if not status.is_success(status_code):
            raise DataPointResponseException(
                f"{ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'Asia verify'})}: {status_code}",
                data_point_log_info=data_point_log_info
            )
        else:
            token: str = get(response_data, "result.token")
            return token
    
    def search_asia_verify(self, request_body: dict, country: str):
        '''
        request_body that need key: 
        {
            "input": "201913494Z",
            "language": "ALL"
        }
        '''
        data_point_function_name = inspect.currentframe().f_code.co_name
        # Setup URL, header and request body
        url = f'{self.DATA_POINT_ASIA_VERIFY_V2_HOST}/v2/open/api/{country}/basic'
        token = self.get_token()
        headers = {'token': token}
        
        # Logging when request API
        logging_context = self.get_logging_context()
        
        max_retries = 3
        retry_count = 0
        response_data = {}
        while retry_count < max_retries:
            data_point_log_info = {
                'url': url,
                'json': request_body,
                'context': logging_context,
            }
            logger.info(f'Asia verify: API Request process', data_point_log_info)
            
            # Request API
            result = self._request(
                method='POST',
                url=url,
                json=request_body,
                headers=headers,
                data_point_function_name=data_point_function_name
            )
        
            
            # Get result from request
            response_data = result.get('result')
            status_code = result.get('status_code')
            response_code = get(response_data, 'code')
            
            # Logging after got response
            data_point_log_info["status_code"] = status_code
            data_point_log_info["code"] = response_code
            logger.info(
                f'Asia verify: Request success: HTTP Response {status_code}',
                data_point_log_info
            )
            
            # Raise DataPointResponseException when status is not 200
            if not status.is_success(status_code):
                raise DataPointResponseException(
                    f"{ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'Asia verify'})}: {status_code}",
                    data_point_log_info=data_point_log_info
                )
            
            if get(response_data, 'result'):
                break  
            
            # Raise DataPointResponseException if no data is found in the response.
            else:
                retry_count += 1
                if retry_count >= max_retries:
                # Get company_id by number or codeField(MYS)
                    number = request_body.get('input')
                    error_infomation = {
                        "country": country,
                        "number": number,
                    }
                    raise DataPointResponseException(
                        f"{ErrorMessage.NO_DATA_RESPONSE.value.format(**{'data_name': 'Company Information'})}: {error_infomation}",
                        data_point_log_info=data_point_log_info
                    )

        # Compatible response to old version
        match str(country).upper():
            case "SGP":
                response_data = SingaporeAdapter.convert(response_data)
            case "JPN":
                response_data = JapanAdapter.convert(response_data)
            case "MYS":
                response_data = MalaysiaAdapter.convert(response_data)
            case "AUS":
                response_data = AustraliaPostRequest.process(v2_response=response_data)

        return response_data

    def search_asia_verify_webhook_response(self, request_body: dict, country: str):
        '''
        request_body that need key:
        
        {
            "input": "70458879", 
            "language": "ALL",
            "notifyUrl": "xxx" // will add by this function
        }
        '''
        from crequest.middleware import CrequestMiddleware
        from django.core.handlers.wsgi import WSGIRequest
        
        
        data_point_function_name = inspect.currentframe().f_code.co_name    
        # Setup host to callback
        request: WSGIRequest = CrequestMiddleware.get_request()
        request_host = request.get_host()
        request_scheme = request.scheme
        callback_url = f'{request_scheme}://{request_host}/api/asiaverify/webhook.site/callback_v2/'

        # Setup URL, header and request body
        url = f'{self.DATA_POINT_ASIA_VERIFY_V2_HOST}/v2/open/api/{country}/basic'
        token = self.get_token()
        if country == "PHL":
            headers = {
                'token': token, 
                'online': "true"
            }
        else:
            headers = {
                'token': token,     
                'realTime': "true"
            }
            
        request_body['notifyUrl'] = callback_url

        # Logging when request API
        logging_context = self.get_logging_context()
        data_point_log_info = {
            'url': url,
            'json': request_body,
            'context': logging_context,
        }
        logger.info(f'Asia verify: API Request process', data_point_log_info)
        
        # Request API
        result = self._request(
            method='POST',
            url=url,
            json=request_body,
            headers=headers,
            data_point_function_name=data_point_function_name
        )
            
        response_data = result.get('result')
        status_code = result.get('status_code')
        response_code = get(response_data, 'code')
        
        # Logging after got response
        data_point_log_info["status_code"] = status_code
        data_point_log_info["code"] = response_code
        logger.info(
            f'Asia verify: Request success: HTTP Response {status_code}',
            data_point_log_info
        )
        
        # Raise DataPointResponseException when status is not 200
        if not status.is_success(status_code):
            raise DataPointResponseException(
                f"{ErrorMessage.SERVICE_UNAVAILABLE.value.format(**{'service': 'Asia verify'})}: {status_code}",
                data_point_log_info=data_point_log_info
            )
        
        # Raise DataPointResponseException if no order_no (HKG) is found in the response.
        if get(response_data, 'orderNo') is None:
            # Get company_id by number or codeField(MYS)
            number = request_body.get('input')
            error_infomation = {
                "country": country,
                "number": number,
                "detail": f"{country} webhook not found orderNo on response"
            }
            raise DataPointResponseException(
                f"{ErrorMessage.NO_DATA_RESPONSE.value.format(**{'data_name': 'Company Information'})}: {error_infomation}",
                data_point_log_info=data_point_log_info
            )
        # Get result from database (response will get from webhook and store in db by /webhook.site/callback/ path)
        else :
            asia_verify_webhook_result = None
            if country == "HKG":
                asia_verify_webhook_result = self.get_async_asia_verify(get(response_data, 'orderNo'), callback_url=callback_url)
                # Raise an exception if no data (HKG) is found in the response.
                if not get(asia_verify_webhook_result, 'result', {}):
                    # Get company_id by number or codeField(MYS)
                    number = request_body.get('input')
                    error_infomation = {
                        "country": country,
                        "number": number,
                        "detail": "HKG webhook not found by orderNo"
                    }
                    raise DataPointResponseException(
                        f"{ErrorMessage.NO_DATA_RESPONSE.value.format(**{'data_name': 'Company Information'})}: {error_infomation}",
                        data_point_log_info=data_point_log_info
                    )
                else:
                    # Compatible response to old version
                    match country:
                        case "HKG":
                            response_data = HongkongAdapter.convert(asia_verify_webhook_result, request_body['input'])
                        case _:
                            response_data = asia_verify_webhook_result
            if country == "PHL":
                asia_verify_webhook_result = self.get_async_asia_verify(get(response_data, 'orderNo'), callback_url=callback_url)
                # Raise an exception if no data (PHL) is found in the response.
                if not get(asia_verify_webhook_result, 'result', {}):
                    # Get company_id by number or codeField(MYS)
                    number = request_body.get('input')
                    error_infomation = {
                        "country": country,
                        "number": number,
                        "detail": "PHL webhook not found by orderNo"
                    }
                    raise DataPointResponseException(
                        f"{ErrorMessage.NO_DATA_RESPONSE.value.format(**{'data_name': 'Company Information'})}: {error_infomation}",
                        data_point_log_info=data_point_log_info
                    )
                else:
                    response_data = asia_verify_webhook_result
            return response_data
    
    def get_async_asia_verify(self, order_no: str, callback_url: str) -> dict|None:
        import time
        from asiaverify.models import AsyncAsiaVerify
        from data_point.models import DataPointLogResult
        
        
        start_time = time.time()
        timeout = settings.DATA_POINT_ASIA_VERIFY_WEBHOOK_TIMEOUT

        # Loop until timeout for get hongkong asia verify result
        while time.time() - start_time < timeout:
            try:
                instance = AsyncAsiaVerify.objects.filter(order_no=order_no)
                if instance.exists():
                    result = instance.first().result
                    
                    # Add result from webhook to datapoint log result
                    data_point_function_name = inspect.currentframe().f_code.co_name 
                    DataPointLogResult.objects.create(
                        decision_flow_pipeline=self.context.get('decision_flow_pipeline'),
                        data_source_name=self.name,
                        data_point_function=data_point_function_name,
                        url=callback_url,
                        request_payload={},
                        result_status_code=200,
                        raw_result=result
                    )
                    return result

            except Exception as e:
                print(f"An error occurred: {e}")
            time.sleep(0.5)  # Wait before trying again

        raise DataPointException(detail=f"Timeout: No data received from the webhook: order_no: {order_no}")
    