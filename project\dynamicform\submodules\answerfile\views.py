from django.db import models
from django.conf import settings
from django.core.cache import cache
from django.db.models import Q
from django.shortcuts import get_object_or_404, redirect
from django.utils.translation import gettext_lazy as _
from rest_framework import viewsets, mixins, status
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound
from rest_framework.parsers import MultiPartParser
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from dynamicform.models import AnswerFile as AnswerFileModel
from dynamicform.models import Form, AppliedForm, Answer
from dynamicform.submodules.answerfile.serializer import AnswerFileSerializer
from dynamicform.validator.validator import validate
from dynamicform.exceptions import AppliedformIsDisbled
import mimetypes


class UploadFileViewSet(mixins.CreateModelMixin, viewsets.GenericViewSet):
  queryset = AnswerFileModel.objects.all()
  serializer_class = AnswerFileSerializer
  parser_classes = [MultiPartParser]

  def get_permissions(self):
    if self.action == 'item' and self.request.method == 'GET':
      permission_classes = [AllowAny]
      return [permission() for permission in permission_classes]

    return super(UploadFileViewSet, self).get_permissions()

  def create(self, request, form_slug=None, applied_form_slug=None, question=None, *args, **kwargs):
    form = get_object_or_404(Form, slug=form_slug)
    applied_form = get_object_or_404(AppliedForm, slug=applied_form_slug, form=form)
    if not applied_form.can_update_info():
      return Response(_('Cannot update submitted form'), status=status.HTTP_403_FORBIDDEN)

    backend_schema = form.backend_schema
    input_files = backend_schema['input_files']
    if question not in input_files:
      return Response(status=status.HTTP_404_NOT_FOUND)

    request_files = request.FILES.getlist('files')
    if not request_files:
      request_files = request.FILES.getlist('files[]')
    
    if len(request_files) <= 0:
      return Response({'files': [_('files is required')]}, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

    item = input_files[question]
    validator_rule = backend_schema['validator_rule']
    if question in validator_rule:
      # validate
      item_answer = {
        question: request_files
      }

      # Filter blocking rules that will prevent any upload
      full_rule_str: str = validator_rule[question]
      blocking_rules = ['min']
      tokens = full_rule_str.split('|')
      filtered_tokens = [token for token in tokens if token.split(':')[0] not in blocking_rules]
      non_blocking_rule = '|'.join(filtered_tokens)
    
      rule = {
        question: non_blocking_rule
      }
      overwrite_fields = form.get_labels()
      errors = validate(item_answer, rule, overwrite_fields=overwrite_fields)
      if len(errors) > 0:
        return Response(errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

    backend_schema = form.backend_schema
    answer = applied_form.save_answer(item['step'], item['section'], question, [], backend_schema)
    if not answer:
      raise NotFound()

    upload_items = []

    is_multiple = True
    if 'multiple' in item:
      is_multiple = item['multiple']

    file_id = False
    for file_item in request_files:
      file_name = AnswerFileModel.get_name(file_item.name, applied_form)
      obj = AnswerFileModel()
      obj.answer_id = answer.id
      obj.name = file_item.name
      mimetype = mimetypes.types_map.get(file_item.name, False)
      if mimetype:
        obj.mime_type = mimetype
      obj.detail.save(file_name, file_item)
      upload_items.append(obj.info(request=request))
      file_id = obj.id

    if not is_multiple:
      answer.answerfile_set.filter(~Q(id=file_id)).delete()
      if applied_form.form.log_answers:
        answer.log_answer('update')
    else:
      if applied_form.form.log_answers:
        answer.log_answer('create')

    return Response(data=upload_items, status=status.HTTP_201_CREATED)

  @action(methods=['GET', 'DELETE'], detail=False, url_path=r'(?P<file_id>[\w-]+)/(?P<file_name>[^/]+)')
  def item(self, request, form_slug=None, applied_form_slug=None, question=None, file_id=None, file_name=None, *args, **kwargs):
    if request.method == 'GET':
      item = self._get_item(request, form_slug, applied_form_slug, question, file_id, file_name, *args, **kwargs)
      file_url = self._get_url(item)
      return redirect(file_url)
    elif request.method == 'DELETE':
      return self._delete(request, form_slug, applied_form_slug, question, file_id, file_name, *args, **kwargs)
    
    return Response(status=status.HTTP_204_NO_CONTENT)

  @action(methods=['GET'], detail=False, url_path=r'(?P<file_id>[\w-]+)/(?P<file_name>[^/]+)/proxy')
  def proxy(self, request, form_slug=None, applied_form_slug=None, question=None, file_id=None, file_name=None, *args, **kwargs):
    item = self._get_item(request, form_slug, applied_form_slug, question, file_id, file_name, *args, **kwargs)
    response = Response(
        content_type="application/pdf",
        headers={
            "Content-Disposition": f"attachment; filename={item.detail.name}",
        },
    )
    response.content = item.detail.file.read()
    return response
      
  def _get_item(self, request, form_slug=None, applied_form_slug=None, question=None, file_id=None, file_name=None, *args, **kwargs):
    form = get_object_or_404(Form, slug=form_slug)
    applied_form = get_object_or_404(
        AppliedForm, slug=applied_form_slug, form=form)
    if not applied_form.can_view_file():
      raise AppliedformIsDisbled()

    answer = get_object_or_404(Answer, applied_form=applied_form, question=question)
    item = get_object_or_404(AnswerFileModel, answer=answer, id=file_id, name=file_name)
    return item
  
  def _get_url(self, file):
    key = 'dynamicform_file_url_' + str(file.id)
    url = cache.get(key)
    if url is None:
      url = file.preview_url
      cache_timeout = getattr(settings, 'FILE_CACHE_EXPIRATION', 0)
      cache_timeout *= 3 / 5
      cache.set(key, url, cache_timeout)
    return url
  
  def _delete(self, request, form_slug=None, applied_form_slug=None, question=None, file_id=None, file_name=None, *args, **kwargs):
    form = get_object_or_404(Form, slug=form_slug)
    applied_form = get_object_or_404(AppliedForm, slug=applied_form_slug, form=form)
    answer = get_object_or_404(Answer, applied_form=applied_form, question=question)
    target_file = get_object_or_404(AnswerFileModel, answer=answer, id=file_id, name=file_name)

    if not applied_form.can_update_info():
      return Response(_('Cannot update submitted form'), status=status.HTTP_403_FORBIDDEN)

    target_file.delete()
    if applied_form.form.log_answers:
      answer.log_answer('update')

    # Also recalculate smart uploader
    from dynamicform.submodules.appliedform.models.appliedform_smartuploader import AppliedFormSmartUploader
    appliedformsmartuploader_set: models.BaseManager[AppliedFormSmartUploader] = answer.appliedformsmartuploader_set
    if appliedformsmartuploader_set.exists():
        for item in appliedformsmartuploader_set.all():
          item.recalculate_page_count()

    return Response(status=status.HTTP_204_NO_CONTENT)