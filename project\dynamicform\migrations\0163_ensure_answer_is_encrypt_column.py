# Generated manually to ensure is_encrypt column exists

from django.db import migrations, models, connection


def check_and_add_is_encrypt_column(apps, schema_editor):
    """
    Check if is_encrypt column exists in dynamicform_answer table.
    If not, add it safely.
    """
    with connection.cursor() as cursor:
        # Check if the column exists
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'dynamicform_answer' 
            AND column_name = 'is_encrypt'
            AND table_schema = 'public'
        """)
        
        result = cursor.fetchone()
        
        if not result:
            # Column doesn't exist, add it
            cursor.execute("""
                ALTER TABLE dynamicform_answer 
                ADD COLUMN is_encrypt BOOLEAN DEFAULT FALSE
            """)
            
            # Make it nullable to match the model definition
            cursor.execute("""
                ALTER TABLE dynamicform_answer 
                ALTER COLUMN is_encrypt DROP NOT NULL
            """)
            
            print("Added is_encrypt column to dynamicform_answer table")
        else:
            print("is_encrypt column already exists in dynamicform_answer table")


def reverse_add_is_encrypt_column(apps, schema_editor):
    """
    Remove the is_encrypt column if it was added by this migration.
    Note: This is a destructive operation and should be used carefully.
    """
    with connection.cursor() as cursor:
        # Check if the column exists before trying to drop it
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'dynamicform_answer' 
            AND column_name = 'is_encrypt'
            AND table_schema = 'public'
        """)
        
        result = cursor.fetchone()
        
        if result:
            cursor.execute("""
                ALTER TABLE dynamicform_answer 
                DROP COLUMN IF EXISTS is_encrypt
            """)
            print("Removed is_encrypt column from dynamicform_answer table")


class Migration(migrations.Migration):

    dependencies = [
        ('dynamicform', '0162_merge_20250917_1746'),
    ]

    operations = [
        migrations.RunPython(
            check_and_add_is_encrypt_column,
            reverse_add_is_encrypt_column,
        ),
    ]
