from pydash import get


class MalaysiaAdapter:
    @classmethod
    def convert(cls, v2_response: dict):
        # No data in v2_response for response.result.data.shareholders.data.[n].totalNumber 
        # No data in v2_response for response.result.data.CompanyRegistrationInformation.data.[n].businessScope.code
        # company_information_v2.regAddress is always registered Address
        # bussiness scope of v2 is list so get in last index
        instance = cls()
        result = {}
        result['code'] = v2_response['code']
        result['message'] = v2_response['message']
        result['result'] = {
            "data": instance.data_adapter(v2_response['result']),
            "lastUpdated": v2_response['lastUpdated'],
            "orderNo": v2_response['orderNo'],
            "status": None,
            "total": None,
        }
        return result

    def data_adapter(self, data_response: dict) -> dict:
        print(data_response)
        data_result = {
            "Shareholders": self.data_shareholder_adapter(data_response["Shareholders"]),
            "majorPerson":  self.data_major_person_adapter(data_response["majorPerson"]),
            "CompanyRegistrationInformation": self.data_company_registration_information_adapter(data_response["CompanyInformation"]),
        }
        return data_result

    def data_shareholder_adapter(self, shareholders_response: dict):
        shareholders_result = {}
        shareholders_result['total'] = shareholders_response['total']
        
        # individual result
        individual_list = []
        
        total_shareamount = 0
        for individual_v2 in list(shareholders_response["data"]['individual']):
            share_amount = float(get(individual_v2, "numberOfShares").replace(",", ""))
            data = {
                "shareNumber": share_amount,
                "totalNumber": 0,
                "stockName":  get(individual_v2, "shareholderNameEN"),
                "id": get(individual_v2, "shareholderID"),
                "shareholderType": get(individual_v2, "entityType"),
            }
            # Add shareAmout to total for calculate persentage before append in list
            total_shareamount += share_amount
            individual_list.append(data)
            
        # Make shareAmount become percentage
        for individual in individual_list:
            if total_shareamount > 0:
                individual['shareNumber'] = round(individual['shareNumber'] / total_shareamount * 100, 2)
            else:
                individual['shareNumber'] = 0
            individual['totalNumber'] = total_shareamount
        
        shareholders_result['data'] = individual_list
        return shareholders_result

    def data_major_person_adapter(self, major_person_response: dict):
        major_person_result = {}
        major_person_result['total'] = major_person_response['total']
        
        # major_person result
        major_person_list = []
        for major_person_v2 in list(major_person_response["data"]):
            major_person = {
                "address": get(major_person_v2, "address"),
                "name": get(major_person_v2, "enName"),
                "position": get(major_person_v2, "role"),
                "birthDate": get(major_person_v2, "dob")
            }
            major_person_list.append(major_person)
        major_person_result['data'] = major_person_list
        return major_person_result

    def data_company_registration_information_adapter(self, company_information_response: dict):
        company_info_result = {}
        company_info_result['total'] = company_information_response['total']
        
        # company_registration_information result
        company_registration_information_list = []
        for company_information_v2 in list(company_information_response["data"]):
            company_info = {
                "companyNumberOld": get(company_information_v2, "companyIDOther1"),
                "establishedDate": get(company_information_v2, "incorpDate"),
                "companyNumber": get(company_information_v2, "companyID"),
                "companyType": get(company_information_v2, "companyType"),
                "companyName": get(company_information_v2, "name.companyName"),
                "capitalStructure": [
                    {
                        "currency": get(capital, "currency"),
                        "shareAmount":  get(capital, "capitalAmount")
                    } for capital in   get(company_information_v2, 'capital' , [])
                ],
                "businessScope": [
                    {
                        "code": "",  # No Map
                        "description": get(industry, "description")
                    } for industry in get(company_information_v2, 'industry' , [])
                ],
                "bizStatus":  get(company_information_v2, "companyStatus"),
                "companyAddresses": [
                    {
                        "address":  get(company_information_v2, "regAddress.full"),
                        "addressType": "Registered Address"
                    }
                ] + [{
                        "address":  get(address, "full"),
                        "addressType": get(address, "type"),
                    } for address in get(company_information_v2, "otherCompanyAddresses")],
            }
            company_registration_information_list.append(company_info)
        company_info_result['data'] = company_registration_information_list
        return company_info_result
 