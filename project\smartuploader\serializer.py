from django.core.validators import FileExtensionValidator
from rest_framework import serializers
from .models.smartuploader import SmartUploader


class SmartUploaderInputSerializer(serializers.ModelSerializer):
    files = serializers.ListField(
        child=serializers.FileField(
            max_length=300,
            allow_empty_file=False,
            use_url=False,
            validators=[FileExtensionValidator(allowed_extensions=["pdf", "jpg", "jpeg", "png"])],
        ),
        min_length=1,
        max_length=1,
    )
    password = serializers.CharField(
        max_length=191,
        allow_blank=True,
        required=False,
    )
    class Meta:
        model = SmartUploader
        fields = ["files", "password"]
        read_only_fields = []


class SmartUploaderSerializer(serializers.ModelSerializer):
    class Meta:
        model = SmartUploader
        fields = ["id", "status", "raw_results"]
        read_only_fields = ["id", "status"]

    def validate_raw_results(self, value):
        if "result" not in value:
            raise serializers.ValidationError("Result is invalid.")

        return value

    def to_representation(self, instance: SmartUploader):
        return instance.info()


class SmartUploaderCallbackSerializer(serializers.ModelSerializer):
    request_id = serializers.CharField()
    result = serializers.JSONField()
    message = serializers.CharField(required=False)

    class Meta:
        model = SmartUploader
        fields = ["request_id", "result", "message"]
        read_only_fields = []
