ALL_EKYC_LIVENESS_ITEM_TYPES = ["Ekyc.Liveness", "Ekyc.LivenessVNext"]
ALL_EKYC_DOCUMENT_ITEM_TYPES = [
    "Ekyc.Document",
    "Ekyc.DocumentVNext",
    "Ekyc.SingleDocumentBase",
    "Ekyc.FrontCard",
    "Ekyc.Passport",
    "Ekyc.DriverLicense",
    "Ekyc.ThaiAlienCard",
    "Ekyc.Portrait",
    "Ekyc.ResidencePermit",
    "Ekyc.CiPassport",
    "Ekyc.WorkPermitCard",
    "Ekyc.WorkPermitBook",
    "Ekyc.TravelDocument",
    "Ekyc.WhiteCard",
    "Ekyc.BorderPass",
    "Ekyc.MonkCard",
    "Ekyc.ImmigrationCard",
    "Ekyc.OtherDocument",
]
ALL_EKYC_BACKCARD_ITEM_TYPES = ["Ekyc.BackCard", "Ekyc.BackCardVNext"]

ALLOWED_EVENTS = [
    {
        "label": "On Form Created",
        "value": "onCreated",
    },
    {
        "label": "On Submit",
        "value": "onSubmit",
    },
    {
        "label": "On Update Status",
        "value": "onUpdateStatus",
    },
    {
        "label": "On Drop Off",
        "value": "onDropOff",
        "required_settings": ["drop_off"],
    },
    {
        "label": "On Liveness Verification Pending",
        "value": "onEkycLivenessPending",
        "required_item_types": [ALL_EKYC_LIVENESS_ITEM_TYPES],
    },
    {
        "label": "On Liveness Verification Started",
        "value": "onEkycLivenessStarted",
        "required_item_types": [ALL_EKYC_LIVENESS_ITEM_TYPES],
    },
    {
        "label": "On Liveness Verification Pass",
        "value": "onEkycLivenessSuccess",
        "required_item_types": [ALL_EKYC_LIVENESS_ITEM_TYPES],
    },
    {
        "label": "On Liveness Verification Failed",
        "value": "onEkycLivenessFailed",
        "required_item_types": [ALL_EKYC_LIVENESS_ITEM_TYPES],
    },
    {
        "label": "On Liveness Verification Cancelled",
        "value": "onEkycLivenessCancelled",
        "required_item_types": [ALL_EKYC_LIVENESS_ITEM_TYPES],
    },
    {
        "label": "On Liveness Verification Reached Max Attempts",
        "value": "onEkycLivenessReachedMaxAttempts",
        "required_item_types": [ALL_EKYC_LIVENESS_ITEM_TYPES],
    },
    {
        "label": "On Liveness Verification Complete",
        "value": "onEkycLivenessComplete",
        "required_item_types": [ALL_EKYC_LIVENESS_ITEM_TYPES],
    },
    {
        "label": "On ID Scan Verification Pass",
        "value": "onEkycFrontCardSuccess",
        "required_item_types": [ALL_EKYC_DOCUMENT_ITEM_TYPES],
    },
    {
        "label": "On ID Scan Verification Failed",
        "value": "onEkycFrontCardFailed",
        "required_item_types": [ALL_EKYC_DOCUMENT_ITEM_TYPES],
    },
    {
        "label": "On ID Scan Verification Reached Max Attempts",
        "value": "onEkycFrontCardReachedMaxAttempts",
        "required_item_types": [ALL_EKYC_DOCUMENT_ITEM_TYPES],
    },
    {
        "label": "On ID Scan Verification Complete",
        "value": "onEkycFrontCardComplete",
        "required_item_types": [ALL_EKYC_DOCUMENT_ITEM_TYPES],
    },
    {
        "label": "On Back of ID Scan Verification Pass",
        "value": "onEkycBackCardSuccess",
        "required_item_types": [ALL_EKYC_BACKCARD_ITEM_TYPES],
    },
    {
        "label": "On Back of ID Scan Verification Failed",
        "value": "onEkycBackCardFailed",
        "required_item_types": [ALL_EKYC_BACKCARD_ITEM_TYPES],
    },
    {
        "label": "On Back of ID Scan Verification Reached Max Attempts",
        "value": "onEkycBackCardReachedMaxAttempts",
        "required_item_types": [ALL_EKYC_BACKCARD_ITEM_TYPES],
    },
    {
        "label": "On Back of ID Scan Verification Complete",
        "value": "onEkycBackCardComplete",
        "required_item_types": [ALL_EKYC_BACKCARD_ITEM_TYPES],
    },
    {
        "label": "On Face Compare Pass",
        "value": "onEkycFaceCompareSuccess",
        "required_item_types": [ALL_EKYC_LIVENESS_ITEM_TYPES, ALL_EKYC_DOCUMENT_ITEM_TYPES],
    },
    {
        "label": "On Face Compare Failed",
        "value": "onEkycFaceCompareFailed",
        "required_item_types": [ALL_EKYC_LIVENESS_ITEM_TYPES, ALL_EKYC_DOCUMENT_ITEM_TYPES],
    },
    {
        "label": "On Email Verification Complete",
        "value": "onEmailVerificationComplete",
        "required_item_types": ["OTP.VerifyEmail"],
    },
    {
        "label": "On Phone Verification Complete",
        "value": "onPhoneVerificationComplete",
        "required_item_types": ["OTP.Verify"],
    },
    {
        "label": "On Bank Statement Verification Complete",
        "value": "onBankStatementVerificationComplete",
        "required_item_types": ["BankStatement.Uploader"],
    },
    {
        "label": "On Smart Uploader Complete",
        "value": "onSmartUploaderComplete",
        "required_item_types": ["SmartUploader"],
    },
    {"label": "On Other Event Failed", "value": "onOtherEventFailed"},
]

EVENTS_WEBHOOK_DECISION_FLOW = {
    "submit_form": "onSubmit",
    "update_status": "onUpdateStatus",
    "drop_off": "onDropOff",
    "ekyc_liveness_pending": "onEkycLivenessPending",
    "ekyc_liveness_started": "onEkycLivenessStarted",
    "ekyc_liveness_success": "onEkycLivenessSuccess",
    "ekyc_liveness_failed": "onEkycLivenessFailed",
    "ekyc_liveness_cancelled": "onEkycLivenessCancelled",
    "ekyc_liveness_reached_max_attempts": "onEkycLivenessReachedMaxAttempts",
    "ekyc_front_card_success": "onEkycFrontCardSuccess",
    "ekyc_front_card_failed": "onEkycFrontCardFailed",
    "ekyc_front_card_reached_max_attempts": "onEkycFrontCardReachedMaxAttempts",
    "ekyc_face_compare_success": "onEkycFaceCompareSuccess",
    "ekyc_face_compare_failed": "onEkycFaceCompareFailed",
}

EVENT_ITEM_COMPLETED = {
    "Ekyc.Liveness": "onEkycLivenessComplete",
    "Ekyc.LivenessVNext": "onEkycLivenessComplete",
    "Ekyc.Document": "onEkycFrontCardComplete",
    "Ekyc.DocumentVNext": "onEkycFrontCardComplete",
    "OTP.VerifyEmail": "onEmailVerificationComplete",
    "OTP.Verify": "onPhoneVerificationComplete",
    "BankStatement.Uploader": "onBankStatementVerificationComplete",
}

REPEAT_RUN_EVENTS = [
    {
        "value": "onComplyAdvantageSearchUpdated",
        "label": "Comply Advantage",
        "logo": "https://cdn.uppass.io/projects/uppass/img/datapoint/icon_64/Comply_64.png",
        "description": "When monitoring result has updated",
        "get_is_allow": lambda form: "comply_advantage" in form.all_data_points,
    }
]


def convert_event_decision_flow_key_to_webhook_key(decision_flow_event: str, section_trigger_events: list):
    from pydash import snake_case

    for event in ALLOWED_EVENTS:
        if decision_flow_event in event["value"]:
            return  snake_case(event["label"])

    for event in section_trigger_events:
        if decision_flow_event in event["value"]:
            return  snake_case(event["label"])
    
    return snake_case(decision_flow_event)
