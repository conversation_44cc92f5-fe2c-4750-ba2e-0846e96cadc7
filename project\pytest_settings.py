import os

from project.settings import *
from dynamicform.tests.settings import *

DATABASES = {
    'default': {
        'ENGINE': os.environ.get('SQL_ENGINE', 'psqlextra.backend'),
        'HOST': os.environ.get('SQL_HOST', '127.0.0.1'),
        'PORT': '5432',
        'NAME': 'test_dynamicform_project',
        'USER': os.environ.get('SQL_USER', 'postgres'),
        'PASSWORD': os.environ.get('SQL_PASSWORD', 'postgres'),
        'TEST': {
            'NAME': 'test_' + os.environ.get('SQL_DATABASE', os.path.join(BASE_DIR, 'db.sqlite3')),
        },
    }
}

MULTI_DATABASES = {}
DATABASE_MODEL_USE_DB = {}
CREDIT_SYSTEM_PACKAGES = {}
LOGGING = {}
DISABLE_LOG_DATA = 1
INSTALLED_APPS.remove('applog.apps.ApplogConfig')
MIDDLEWARE.remove('applog.middleware.LogResponseTimeMiddleware')
MIDDLEWARE.remove('applog.middleware.RequestResponseLogMiddleware')

USER_TOKEN_JWE_SECRET_KEY = os.urandom(16)
SPEECH_TO_TEXT_APP_ID = 'xxx'

LOCK_PASSWORD_CHANGING_START_ATTEMPT = 2
LOCK_PASSWORD_CHANGING_DURATION = 600
LOCK_PASSWORD_CHANGING_MAX_DURATION = 1500
LOCMEMCACHE_ENABLED = 0
