import json

from django.core.exceptions import ValidationError
from django.urls import reverse
from django.test import RequestFactory, TestCase, Client, modify_settings
from rest_framework import status
from rest_framework.test import APIClient

from asiaverify.models import AsyncAsiaVerify

# Create your tests here.
class AsyncAsiaVerifyViewSetTests(TestCase):
    
    def setUp(self):
        self.client = APIClient()
        self.factory = RequestFactory()
        self.order_no = "dummy_order"
        self.result = {
            "code": 200,
            "message": "Request succeeded",
            "result": {}
        }
        
        # Create a school instance for testing
        self.asia_verify_hkg = AsyncAsiaVerify.objects.create(order_no=self.order_no, result=self.result)
        
        # Reverse URLs for callback
        self.call_back_url = reverse('asiaverify:asiaverify-callback')

    def test_call_back(self):
        # Send a POST request to the callback action
        response = self.factory.post(
            self.call_back_url, 
            data={
                "result": {
                    "data": self.result,
                    "order_no": self.order_no
                }
            }, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {"message": "Callback received"})
