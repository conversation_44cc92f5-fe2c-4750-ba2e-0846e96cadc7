
from enum import Enum
from typing import Callable
from pydash import get, set_

import re

from data_point.tests.provider.mock_up_service_result.mock_asia_verify_result import MockAsiaVerifyServiceResult


class BusinessAMLValidateInputResultTestCase:
    ''' Collects all test cases that use in 
        test_input_validation
    '''
    
    class ExpectResult(Enum):
        SUCCESS_VALIDATE = True
        FAILED_VALIDATE = False
        
    ### Set of each test case
    # - Test case name
    # - Test case input   
    # - Expect result
    TEST_CASE: list = [
        (
            f"{ExpectResult.SUCCESS_VALIDATE.name}: Thailand case",
            {
                "country": "THA",
                "juristic_id": "1234567890123"
            }, 
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f"{ExpectResult.SUCCESS_VALIDATE.name}: Japan case",
            {
                "country": "JPN",
                "juristic_id": "1234567890123"
            }, 
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f"{ExpectResult.SUCCESS_VALIDATE.name}: Malaysia case",
            {
                "country": "MYS",
                "juristic_id": "1234567890123"
            }, 
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f"{ExpectResult.SUCCESS_VALIDATE.name}: Singapore case",
            {
                "country": "SGP",
                "juristic_id": "1234567890123"
            }, 
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f"{ExpectResult.SUCCESS_VALIDATE.name}: Hongkong case",
            {
                "country": "HKG",
                "juristic_id": "1234567890123"
            }, 
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: Company id is less then 13 digits for Thailand company.',
            {
                "country": "THA",
                "juristic_id": "123456789012"
            }, 
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'Business AML: The Business Information format is invalid according to the THA company ID pattern: 123456789012',
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: Company id is more then 13 digits for Thailand company.',
            {
                "country": "THA",
                "juristic_id": "12345678901234"
            }, 
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'Business AML: The Business Information format is invalid according to the THA company ID pattern: 12345678901234',
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: Company id is None value',
            {
                "country": "THA",
                "juristic_id": None
            }, 
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'Business AML: Business Information cannot be empty',
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: Company id is contain characters for Thailand company.',
            {
                "country": "THA",
                "juristic_id": '123456789None', 
            },
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'Business AML: The Business Information format is invalid according to the THA company ID pattern: 123456789None',
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: Country do not support.',
            {
                "country": "XXX",
                "juristic_id": '1234567890123', 
            },
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'Business AML: This country is not supported: XXX',
            },
        ),
    ]


class AsiaVerifyAPIServiceTestCase:
    ''' Collects all test cases that use in 
        test_asia_verify_api_service
    '''
    class ExpectResult(Enum):
        SUCCESS_VALIDATE = True
        FAILED_VALIDATE = False
        
    ### Set of each test case
    # - Test case name
    # - Test case input   
    # - Expect result
    TEST_CASE: list = [
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}',
            {
                'status_code': 200,
                'result': {
                    "result": {
                        "data": {
                            "Shareholders": {},
                            "majorPerson": {},
                        }
                        }
                    },
                'error': None
            },
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: 404 not found.',
            {
                'status_code': 404,
                'result': {'detail': 'not found'},
                'error': None
            },
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'Business AML: Asia verify service is temporarily unavailable: 404',
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: handle no data found in response',
            {
                'status_code': 200,
                'result': {},
                'error': None
            },
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": "Business AML: Not found any Company Information in response: {'country': 'JPN', 'number': 'Test1234567'}",
            },
        )
    ]


class DBDAPIServiceTestCase:
    ''' Collects all test cases that use in 
        test_dbd_api_service
        test_creden_api_service
        test_comply_advantage_api_service
    '''
    class ExpectResult(Enum):
        SUCCESS_VALIDATE = True
        FAILED_VALIDATE = False
        
    class KeywordTestCaseName(Enum):
        DBD = 'DBD'
        CREDEN = 'Creden'
        COMPLY_ADVANTAGE = 'Comply Advantage'
    
    
    def __init__(self):
        test_case_update = []
        # Make Enum to dict
        keyword_dict =  {keyword.name: keyword.value for keyword in DBDAPIServiceTestCase.KeywordTestCaseName}
        for keyword, value in keyword_dict.items():
            # Create test case from TEST_CASE Structure
            for test_case in self.TEST_CASE.copy():
                test_case_name = str(test_case[0]).format(**{'service_name': keyword})
                test_case_input = test_case[1]
                
                # Initial expected result
                expected_result = {
                    "is_success": test_case[2]['is_success'],
                    "error_message": None,
                }
                
                # Create Error message
                if expected_result['is_success'] is False:
                    error_message_template = 'Business AML: {service} service is temporarily unavailable: {status_code}'
                    format_message = {'service': value, 'status_code': test_case_input['status_code']}
                    expected_result["error_message"] =  error_message_template.format(**format_message)
                
                # Append to test case update
                test_case_update.append((test_case_name, test_case_input, expected_result))
        
        # Setup TEST_CASE with updated test case.
        self.TEST_CASE = test_case_update
        
    ### Set of each test case
    # - Test case name
    # - Test case input   
    # - Expect result
    TEST_CASE: list[tuple] = [
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: '+ '{service_name} case',
            {
                'status_code': 200,
                'result': {},
                'error': None
            },
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: 404 not found: ' + '{service_name} case' ,
            {
                'status_code': 404,
                'result': {'detail': 'not found'},
                'error': None
            },
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": None,
            },
        )
    ]


class BusinessAMLDatapointOptionResultTestCase:
    ''' Collects all test cases that use in 
    test_datapoint_option_result_from_asia_verify_(japan/malaysia/singapore).
    
    ---
    NOTE: This class is collect test case only for AsiaVerifyBusiness only.
    '''
    class ExpectResult(Enum):
        SUCCESS_VALIDATE = True
        FAILED_VALIDATE = False
        
        @staticmethod
        def data_point_result(
            is_company_with_hits: bool = False, 
            is_any_directors_with_hits: bool = False, 
            is_any_25pct_shareholders_with_hits: bool = False,
            is_any_directors_th_pep_hits: bool = False
        ):
            return {
                'is_company_with_hits': is_company_with_hits,
                'is_any_directors_with_hits': is_any_directors_with_hits,
                'is_any_directors_th_pep_hits': is_any_directors_th_pep_hits,
                'is_any_25pct_shareholders_with_hits': is_any_25pct_shareholders_with_hits,
                'is_any_15pct_shareholders_with_hits': False,
                'is_any_10pct_shareholders_with_hits': False,
                'is_any_25pct_shareholders_th_pep_hits': False,
                'is_any_15pct_shareholders_th_pep_hits': False,
                'is_any_10pct_shareholders_th_pep_hits': False,
            }

    class KeywordTestCaseName(Enum):
        ASIA_VERIFY_JPN = 'asia_verify_jpn'
        ASIA_VERIFY_MYS = 'asia_verify_mys'
        ASIA_VERIFY_SGP = 'asia_verify_sgp'
        ASIA_VERIFY_HKG = 'asia_verify_hkg'
        COMPANY_HIT = 'company_hit'
        DIRECTOR_HIT = 'directors_hit'
        SHAREHOLDER_HIT = 'shareholders_hit'
        ALL_HIT = 'all_hit'
        
    
    def __init__(self):
        self.config_test_case()
    
    def config_test_case(self):
        test_case_update_list = []
        keyword_name = BusinessAMLDatapointOptionResultTestCase.KeywordTestCaseName
        
        # director_case and shareholder_case must be matched with data point option result in each country
        for test_case in self.TEST_CASE:
            if keyword_name.ASIA_VERIFY_JPN.name in test_case[0]:
                test_case_update = self.__config_asia_verify_test_case(
                    country="JPN",
                    test_case=test_case,
                    director_case=lambda person: re.search(r'Director\s(.+),', person['rdPersonnel']),
                    shareholder_case=lambda person: float(get(person, 'proportion', 0.0)) >= 25,
                )
            elif keyword_name.ASIA_VERIFY_MYS.name in test_case[0]:
                test_case_update = self.__config_asia_verify_test_case(
                    country="MYS",
                    test_case=test_case,
                    director_case=lambda person: str(person['position']).lower() == 'director',
                    shareholder_case=lambda person: float(get(person, 'shareNumber', 0)) >= 25,
                )
            elif keyword_name.ASIA_VERIFY_SGP.name in test_case[0]:
                test_case_update = self.__config_asia_verify_test_case(
                    country="SGP",
                    test_case=test_case,
                    director_case=lambda person: str(person['position']).lower() == 'director',
                    shareholder_case=lambda person: int(get(person, 'shareAmount', 0)) >= 25,
                )
            elif keyword_name.ASIA_VERIFY_HKG.name in test_case[0]:
                test_case_update = self.__config_asia_verify_test_case(
                    country="HKG",
                    test_case=test_case,
                    director_case=lambda person: str(person['position']).lower() == 'director', 
                    shareholder_case=lambda person: float(str(get(person, 'percent')).replace('%', '0')) >= 25.0
                )
            else:
                test_case_update = tuple()
            test_case_update_list.append(test_case_update)
        self.TEST_CASE = test_case_update_list    
    
    def __config_asia_verify_test_case(
            self,
            country: str,
            test_case: tuple,
            director_case: Callable[[dict], bool],
            shareholder_case: Callable[[dict], bool],
            ) -> tuple:
        ca_side_effect = []
        keyword_name = BusinessAMLDatapointOptionResultTestCase.KeywordTestCaseName

        # Company hit
        company_hit = 0
        if any([
            True for keyword in [keyword_name.COMPANY_HIT.name, keyword_name.ALL_HIT.name] 
            if keyword in test_case[0]
        ]):
            company_hit = 1
        ca_side_effect.append(set_({}, 'content.data.total_hits', company_hit))

        # Director hit
        director_list = [
            person for person in MockAsiaVerifyServiceResult.get_major_person_list(country) 
            if director_case(person)
        ]
        for _ in director_list:
            director_hit = 0
            if any([
                True for keyword in [keyword_name.DIRECTOR_HIT.name, keyword_name.ALL_HIT.name] 
                if keyword in test_case[0]
            ]):
                director_hit = 1
            ca_side_effect.append(set_({}, 'content.data.total_hits', director_hit))

        # Shareholder hit
        shareholder_list = [
            person for person in MockAsiaVerifyServiceResult.get_25shareholder_list(country) 
            if shareholder_case(person)
        ]
        for _ in shareholder_list:
            shareholder_hit = 0
            if any([
                True for keyword in [keyword_name.SHAREHOLDER_HIT.name, keyword_name.ALL_HIT.name] 
                if keyword in test_case[0]
            ]):
                shareholder_hit = 1
            ca_side_effect.append(set_({}, 'content.data.total_hits', shareholder_hit))

        # Setup ca_side_effect in test case input to setup mock result in each time for api request Comply Adventage
        test_case[1]['ca_side_effect'] = ca_side_effect
        return test_case
        
    @classmethod
    def filter_test_case_by_keyword(cls, keyword: KeywordTestCaseName):
        return [case for case in cls.TEST_CASE if keyword.name in case[0]]

    TEST_CASE: list[tuple] = [
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_JPN.name}: {KeywordTestCaseName.COMPANY_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.JPN_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(is_company_with_hits=True)
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_JPN.name}: {KeywordTestCaseName.DIRECTOR_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.JPN_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(is_any_directors_with_hits=True, is_any_directors_th_pep_hits=True)
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_JPN.name}: {KeywordTestCaseName.SHAREHOLDER_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.JPN_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(is_any_25pct_shareholders_with_hits=True)
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_JPN.name}: {KeywordTestCaseName.ALL_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.JPN_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(
                is_company_with_hits=True,
                is_any_directors_with_hits=True,
                is_any_25pct_shareholders_with_hits=True,
                is_any_directors_th_pep_hits=True,
            )
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_MYS.name}: {KeywordTestCaseName.COMPANY_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.MYS_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(is_company_with_hits=True)
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_MYS.name}: {KeywordTestCaseName.DIRECTOR_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.MYS_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(is_any_directors_with_hits=True)
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_MYS.name}: {KeywordTestCaseName.SHAREHOLDER_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.MYS_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(is_any_25pct_shareholders_with_hits=True)
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_MYS.name}: {KeywordTestCaseName.ALL_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.MYS_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(
                is_company_with_hits=True,
                is_any_directors_with_hits=True,
                is_any_25pct_shareholders_with_hits=True,
            )
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_SGP.name}: {KeywordTestCaseName.COMPANY_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.SGP_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(is_company_with_hits=True)
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_SGP.name}: {KeywordTestCaseName.DIRECTOR_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.SGP_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(is_any_directors_with_hits=True)
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_SGP.name}: {KeywordTestCaseName.SHAREHOLDER_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.SGP_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(is_any_25pct_shareholders_with_hits=True)
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_SGP.name}: {KeywordTestCaseName.ALL_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.SGP_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(
                is_company_with_hits=True,
                is_any_directors_with_hits=True,
                is_any_25pct_shareholders_with_hits=True,
            )
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_HKG.name}: {KeywordTestCaseName.COMPANY_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.HKG_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(is_company_with_hits=True)
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_HKG.name}: {KeywordTestCaseName.DIRECTOR_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.HKG_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(is_any_directors_with_hits=True)
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_HKG.name}: {KeywordTestCaseName.SHAREHOLDER_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.HKG_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(is_any_25pct_shareholders_with_hits=True)
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: {KeywordTestCaseName.ASIA_VERIFY_HKG.name}: {KeywordTestCaseName.ALL_HIT.name}',
            {
                "asia_verify_data": MockAsiaVerifyServiceResult.HKG_RESULT['result']['data'],
                "ca_side_effect": []
            },
            ExpectResult.data_point_result(
                is_company_with_hits=True,
                is_any_directors_with_hits=True,
                is_any_25pct_shareholders_with_hits=True,
            )
        )
    ]
