from django.apps import apps
from django.db.models import Manager, Model
from django.db.models.query import QuerySet
from loc_mem_cache.conf import settings
from loc_mem_cache.cache import cache_get_or_set

class CacheQuerySet(QuerySet):
    ops = None
    timeout = 0

    def is_allow_op(self, op:str):
        if self.ops == "all":
            return True

        return op in self.ops

    def extract_filter_kwargs(self):
            kwargs = {}
            for child in self.query.where.children:
                try:
                    if hasattr(child, 'lhs') and hasattr(child, 'rhs'):
                        field_name = child.lhs.target.name
                        value = child.rhs
                        kwargs[field_name] = value
                except Exception:
                    continue
            return kwargs

    def get(self, *args, **kwargs):
        if self.is_allow_op("get"):
            return cache_get_or_set(
                self.model,
                'get',
                kwargs,
                lambda: super(CacheQuerySet, self).get(*args, **kwargs),
                self.timeout
            )
        return super(CacheQuerySet, self).get(*args, **kwargs)
    
    def filter(self, *args, **kwargs):
        qs = super().filter(*args, **kwargs)
        if self.is_allow_op("filter"):
            def _cached_iter(qs_self):
                return cache_get_or_set(
                    qs_self.model,
                    'filter',
                    kwargs,
                    lambda: list(super(CacheQuerySet, qs_self).iterator()),
                    self.timeout
                )
            qs.iterator = iter(_cached_iter(qs))
        return qs
    
    def first(self, *args, **kwargs):
        if self.is_allow_op("first"):
            filter_kwargs = self.extract_filter_kwargs()
            return cache_get_or_set(
                self.model,
                'first',
                filter_kwargs,
                lambda: super(CacheQuerySet, self).first(*args, **kwargs),
                self.timeout
            )
        return super(CacheQuerySet, self).first(*args, **kwargs)

    def all(self, *args, **kwargs):
        if self.is_allow_op("all"):
            filter_kwargs = self.extract_filter_kwargs()
            return cache_get_or_set(
                self.model,
                'all',
                filter_kwargs,
                lambda: super(CacheQuerySet, self).all(),
                self.timeout
            )
        return super(CacheQuerySet, self).all()

    def count(self):
        if self.is_allow_op("count"):
            filter_kwargs = self.extract_filter_kwargs()
            return cache_get_or_set(
                self.model,
                'count',
                filter_kwargs,
                lambda: super(CacheQuerySet, self).count(),
                self.timeout
            )
        return super(CacheQuerySet, self).count()


class CacheQuerySetManager(Manager):
        def get_queryset(self):
            return CacheQuerySet(self.model, using=self._db)
        
        # Forward these methods explicitly so they hit CacheQuerySet
        def filter(self, *args, **kwargs):
            return self.get_queryset().filter(*args, **kwargs)

        def first(self, *args, **kwargs):
            return self.get_queryset().first(*args, **kwargs)

        def count(self, *args, **kwargs):
            return self.get_queryset().count(*args, **kwargs)


def get_manager(config, original_queryset_cls, original_manager_cls):
    ops = config.get("ops")
    timeout = config.get("timeout")

    PatchedQuerySet = type(
        f"Patched{original_queryset_cls.__name__}",
        (CacheQuerySet, original_queryset_cls),
        {
            "__module__": __name__,
            "ops": ops,
            "timeout": timeout,
        }
    )

    class PatchedQuerySetManager(original_manager_cls):
        def get_queryset(self):
            if str(original_manager_cls.__name__) == "SoftDeletionManager":
                # to handle dynamicform.submodules.model.SoftDeletionManager
                if getattr(self, "alive_only", None):
                    return PatchedQuerySet(self.model, using=self._db).filter(deleted_at=None)
            return PatchedQuerySet(self.model, using=self._db)
    
    PatchedQuerySetManager.__name__ = f"Patched{original_manager_cls.__name__}"
    PatchedQuerySetManager.__module__ = __name__

    return PatchedQuerySetManager
    

def patch_queryset(model_path, config):
    model_cls = apps.get_model(model_path)
    original_queryset_cls = type(model_cls._default_manager.get_queryset())
    original_manager_cls = type(model_cls._default_manager)
    if not config:
        return

    CacheQuerySetManager = get_manager(config, original_queryset_cls, original_manager_cls)

    if isinstance(model_cls.objects, Manager):
        model_cls._meta.local_managers = [
            m for m in model_cls._meta.local_managers if m.name != "objects"
        ]
    manager = CacheQuerySetManager()
    manager.contribute_to_class(model_cls, "objects")


def install_logmemcache():
    for model_path, conf in settings.LOCMEMCACHE.items():
        patch_queryset(model_path, conf)