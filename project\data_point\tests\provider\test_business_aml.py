import unittest
from unittest.mock import MagicMock, patch
import pytest
from data_point.provider.business_aml import BusinessAML
from data_point.tests.provider.mock_up_resources.mock_business_aml_test_case import (
    BusinessAMLValidateInputResultTestCase,
    AsiaVerifyAPIServiceTestCase,
    DBDAPIServiceTestCase,
    BusinessAMLDatapointOptionResultTestCase,
)
from data_point.fields.base import DataPointJ<PERSON><PERSON>ield
from data_point.exceptions import DataPointException, DataPointResponseException


class TestBusinessAML(): 
    ''' Unit tests for the BusinessAML class.

    This class contains unit tests to validate the functionality of the BusinessAML class.

    Tests
    -----
    * test_initial_data
        Tests the initialization of the BusinessAML class and the is_valid() function.
    * test_input_validation
        Tests the input validation in the input_validation function of BusinessAML.
    * test_juristic_id_info
        Tests if the juristic_id matches the expected values after initialization.
    * test_asia_verify_api_service
        Tests handling exception from asia verify api service request.
    * test_dbd_api_service
        Tests handling exception from dbd, creden and comply adventage api service request.
    * test_datapoint_option_result_from_asia_verify_japan
        Tests data point option result from asia verify api service in Japan business
    * test_datapoint_option_result_from_asia_verify_malaysia
        Tests data point option result from asia verify api service in Malaysia business
    * test_datapoint_option_result_from_asia_verify_singapore
        Tests data point option result from asia verify api service in Singapore business
    
    '''
    # Initial class for run __init__(self) function to config TEST_CASE
    business_aml_datapoint_option_result = BusinessAMLDatapointOptionResultTestCase()
    dbd_api_service_test_case = DBDAPIServiceTestCase()

    def fake_search(self, input_dict):
        full_name = input_dict.get("full_name")
        if full_name and full_name == "Toshihiro Mibe, Executive Officer and President":
            return {
                "message": "success",
                "data": {
                    "prefix": "นางสาว",
                    "full_name": "แพทองธาร ชินวัตร",
                    "position": "นายกรัฐมนตรี",
                    "responsible_province": None,
                    "responsible_district": None,
                    "responsible_subdistrict": None,
                    "political_party": None,
                    "appointed_year": "2024",
                    "type": "รัฐมนตรีและรองรัฐมนตรี",
                    "term_of_office": {
                        "from": None,
                        "to": None
                    }
                }
            }
        return {
            "message": "success",
            "data": {}
        }
    
    @patch("data_point.provider.answer.Answer.set_form_item_labels")
    def test_initial_data(self, mock_business_aml_object: BusinessAML):
        mock_business_aml_object.is_valid()

    @pytest.mark.parametrize(
        "test_case_name, test_case_input, expected_result",
        BusinessAMLValidateInputResultTestCase.TEST_CASE,
    )
    def test_input_validation(
        self,
        test_case_name: str,
        test_case_input: dict,
        expected_result: dict,
        mock_business_aml_object: BusinessAML,
    ):
        # Prepare test case input
        juristic_id = test_case_input['juristic_id']
        country = test_case_input['country']
        
        # Call function to test with test case input
        actual_result = None
        try:
            actual_result = mock_business_aml_object.validate_input(juristic_id, country)
        except DataPointException:
            actual_result = False

        # Actual result must be equal to expected_result
        assert actual_result[0] == expected_result['is_success'], f'{test_case_name}: actual_result is not equal to expected_result.'
        assert actual_result[1] == expected_result['error_message'], f'{test_case_name}: actual_result is not equal to expected_result.'

    def test_juristic_id_info(self, mock_business_aml_object: BusinessAML):
        test_case_name = "juristic_info"
        # Get juristic_id for get infomation
        juristic_id_info: DataPointJSONField = mock_business_aml_object.fields.get('juristic_id')
        actual_label_name: str = juristic_id_info.label or ""
        actual_type = juristic_id_info.type
        actual_required = juristic_id_info.required
        actual_split_field = juristic_id_info.split_fields
        actual_allowed_item_builder_types = juristic_id_info.allowed_item_builder_types or []
        
        # Validate infomation
        assert actual_label_name == "Business Information", f"{test_case_name}: Label is incorrect."
        assert actual_type == "string", f"{test_case_name}: Type is incorrect."
        assert actual_required == True, f"{test_case_name}: Required setup is incorrect."
        assert actual_split_field == True, f"{test_case_name}: Split field setup is incorrect."
        assert actual_allowed_item_builder_types == [
            "short_long_answer", 
            "business_information"
        ] , f"{test_case_name}: Item builder allowed setup is incorrect."
    
    # @pytest.mark.parametrize(
    #     "test_case_name, test_case_input, expected_result",
    #     AsiaVerifyAPIServiceTestCase.TEST_CASE,
    # )
    # @patch.object(BusinessAML, '_request')
    # def test_asia_verify_api_service(
    #     self,
    #     mock_request: MagicMock,
    #     test_case_name: str,
    #     test_case_input: dict,
    #     expected_result: bool,
    #     mock_business_aml_object: BusinessAML,
    # ):
    #     # Prepare test case input for mock result of _request function
    #     status_code = test_case_input['status_code']
    #     result = test_case_input['result']
    #     error = test_case_input['error']
    #     mock_request.return_value = {
    #         'status_code': status_code,
    #         'result': result,
    #         'error': error,
    #     }
        
    #     actual_success = None
    #     actual_error_message = None
    #     try :
    #         mock_business_aml_object.search_asia_verify(body={'country': 'JPN', 'number': 'Test1234567'})
    #         actual_success = True
    #     except DataPointResponseException as e:
    #         actual_success = False
    #         actual_error_message = e.detail
    #     except Exception as ee:
    #         print(f'unhandled exeption: {ee}')
    #     finally :
    #         # Actual result must be equal to expected_result
    #         assert actual_success is not None, f"{test_case_name}: Unhandled exception"
    #         assert actual_success == expected_result['is_success'], f"{test_case_name}: Actual result is not match."
    #         assert actual_error_message == expected_result['error_message'], f"{test_case_name}: Actual result is not match."

    @pytest.mark.parametrize(
        "test_case_name, test_case_input, expected_result",
        dbd_api_service_test_case.TEST_CASE,
    )
    @patch.object(BusinessAML, '_request')
    def test_dbd_api_service(
        self,
        mock_request: MagicMock,
        test_case_name: str,
        test_case_input: dict,
        expected_result: bool,
        mock_business_aml_object: BusinessAML,
    ):
        # Prepare test case input for mock result of _request function
        status_code = test_case_input['status_code']
        result = test_case_input['result']
        error = test_case_input['error']
        mock_request.return_value = {
            'status_code': status_code,
            'result': result,
            'error': error,
        }
        keywords = DBDAPIServiceTestCase.KeywordTestCaseName
        if keywords.DBD.name in test_case_name:
            api_function = mock_business_aml_object.get_dbd_result
        elif keywords.CREDEN.name in test_case_name:
            api_function = mock_business_aml_object.get_creden_result
        elif keywords.COMPLY_ADVANTAGE.name in test_case_name:
            api_function = mock_business_aml_object.get_comply_advantage_result
        else:
            assert False, f"{test_case_name}: No any keyword found in test case name."

        actual_success = None
        actual_error_message = None
        try :
            api_function({})
            actual_success = True
        except DataPointResponseException as e:
            actual_success = False
            actual_error_message = e.detail
        except Exception as ee:
            print(f'unhandled exeption: {ee}')
        finally :
            # Actual result must be equal to expected_result
            assert actual_success is not None, f"{test_case_name}: Unhandled exception"
            assert actual_success == expected_result['is_success'], f"{test_case_name}: Actual result is not match."
            assert actual_error_message == expected_result['error_message'], f"{test_case_name}: Actual result is not match."
        
    
    
    @pytest.mark.parametrize(
        "test_case_name, test_case_input, expected_result",
        business_aml_datapoint_option_result.filter_test_case_by_keyword(
            business_aml_datapoint_option_result.KeywordTestCaseName.ASIA_VERIFY_JPN,
        ),
    )
    @patch.object(BusinessAML, 'get_comply_advantage_result')
    @patch.object(BusinessAML, 'search_thai_pep_list')
    def test_datapoint_option_result_from_asia_verify_japan(
        self,
        mock_search_thai_pep_list: MagicMock,
        mock_ca_request: MagicMock,
        test_case_name: str,
        test_case_input: dict,
        expected_result: bool,
        mock_business_aml_object: BusinessAML,
    ):
        # Prepare test case input for mock result of _request function
        asia_verify_data = test_case_input['asia_verify_data']
        ca_side_effect = test_case_input['ca_side_effect']
        
        mock_ca_request.side_effect = ca_side_effect
        mock_ca_request.return_value = {'content': {'data': {'total_hit': 0}}}

        mock_search_thai_pep_list.side_effect = self.fake_search

        # Call function api service and record result
        actual_result = None
        mock_business_aml_object.values_list = ['is_company_with_hits', 'is_any_directors_with_hits', 'is_any_25pct_shareholders_with_hits']
        pct_share_data_point_ca_hits_list=['is_any_25pct_shareholders_with_hits']
        pct_share_data_point_th_pep_hits_list=['is_any_25pct_shareholders_th_pep_hits', 'is_any_directors_th_pep_hits']
        if 'DIRECTOR_HIT' in test_case_name:
            mock_business_aml_object.values_list = ['is_any_directors_with_hits']
            pct_share_data_point_ca_hits_list = []
            pct_share_data_point_th_pep_hits_list = ['is_any_directors_th_pep_hits']
        elif 'SHAREHOLDER_HIT' in test_case_name:
            mock_business_aml_object.values_list = ['is_any_25pct_shareholders_with_hits']
            pct_share_data_point_ca_hits_list = ['is_any_25pct_shareholders_with_hits']
            pct_share_data_point_th_pep_hits_list = ['is_any_25pct_shareholders_th_pep_hits']
            pass
        elif 'COMPANY_HIT' in test_case_name:
            mock_business_aml_object.values_list = ['is_company_with_hits']
            pct_share_data_point_ca_hits_list = []
            pct_share_data_point_th_pep_hits_list = []

        actual_result, ca = mock_business_aml_object.data_point_option_result_for_japan(company_information=asia_verify_data, pct_share_data_point_ca_hits_list=pct_share_data_point_ca_hits_list, pct_share_data_point_th_pep_hits_list=pct_share_data_point_th_pep_hits_list)
        
        # Actual result must be equal to expected_result
        assert actual_result == expected_result, f"{test_case_name}: Actual result is not match."
    
    @pytest.mark.parametrize(
        "test_case_name, test_case_input, expected_result",
        business_aml_datapoint_option_result.filter_test_case_by_keyword(
            business_aml_datapoint_option_result.KeywordTestCaseName.ASIA_VERIFY_MYS,
        ),
    )
    @patch.object(BusinessAML, 'get_comply_advantage_result')
    @patch.object(BusinessAML, 'search_thai_pep_list')
    def test_datapoint_option_result_from_asia_verify_malaysia(
        self,
        mock_search_thai_pep_list: MagicMock,
        mock_ca_request: MagicMock,
        test_case_name: str,
        test_case_input: dict,
        expected_result: bool,
        mock_business_aml_object: BusinessAML,
    ):
        # Prepare test case input for mock result of _request function
        asia_verify_data = test_case_input['asia_verify_data']
        ca_side_effect = test_case_input['ca_side_effect']
        
        mock_ca_request.side_effect = ca_side_effect
        mock_ca_request.return_value = {'content': {'data': {'total_hit': 0}}}

        mock_search_thai_pep_list.side_effect = self.fake_search

        # Call function api service and record result
        actual_result = None
        mock_business_aml_object.values_list = ['is_company_with_hits', 'is_any_directors_with_hits', 'is_any_25pct_shareholders_with_hits']
        actual_result, ca = mock_business_aml_object.data_point_option_result_for_malaysia(company_information=asia_verify_data, pct_share_data_point_ca_hits_list=['is_any_25pct_shareholders_with_hits'], pct_share_data_point_th_pep_hits_list=['is_any_25pct_shareholders_th_pep_hits', 'is_any_directors_th_pep_hits'])
        # Actual result must be equal to expected_result
        assert actual_result == expected_result, f"{test_case_name}: Actual result is not match."

    @pytest.mark.parametrize(
        "test_case_name, test_case_input, expected_result",
        business_aml_datapoint_option_result.filter_test_case_by_keyword(
            business_aml_datapoint_option_result.KeywordTestCaseName.ASIA_VERIFY_SGP,
        ),
    )
    @patch.object(BusinessAML, 'get_comply_advantage_result')
    @patch.object(BusinessAML, 'search_thai_pep_list')
    def test_datapoint_option_result_from_asia_verify_singapore(
        self,
        mock_search_thai_pep_list: MagicMock,
        mock_ca_request: MagicMock,
        test_case_name: str,
        test_case_input: dict,
        expected_result: bool,
        mock_business_aml_object: BusinessAML,
    ):
        # Prepare test case input for mock result of _request function
        asia_verify_data = test_case_input['asia_verify_data']
        ca_side_effect = test_case_input['ca_side_effect']
        
        mock_ca_request.side_effect = ca_side_effect
        mock_ca_request.return_value = {'content': {'data': {'total_hit': 0}}}

        mock_search_thai_pep_list.side_effect = self.fake_search
        # Call function api service and record result
        actual_result = None
        mock_business_aml_object.values_list = ['is_company_with_hits', 'is_any_directors_with_hits', 'is_any_25pct_shareholders_with_hits']
        
        actual_result, ca = mock_business_aml_object.data_point_option_result_for_singapore(company_information=asia_verify_data, pct_share_data_point_ca_hits_list=['is_any_25pct_shareholders_with_hits'], pct_share_data_point_th_pep_hits_list=['is_any_25pct_shareholders_th_pep_hits','is_any_directors_th_pep_hits'])
        
        # Actual result must be equal to expected_result
        assert actual_result == expected_result, f"{test_case_name}: Actual result is not match."
