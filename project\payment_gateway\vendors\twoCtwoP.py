from rest_framework import status, serializers
from ..exceptions import (
    PaymentGatewayKwargException,
    PaymentGatewayException,
    PaymentGatewayConverResponseException
)
from pydash.objects import set_, get
import requests
import base64
import jwt
import json

SUCCESS_CODE = '0000'
ALLOW_PT_DATA = [
            'merchantID',
            'invoiceNo',
            'description',
            'amount',
            'currencyCode',
            'paymentChannel',
            'request3DS',
            'tokenize',
            'cardTokens',
            'cardTokenOnly',
            'tokenizeOnly',
            'interestType',
            'installmentPeriodFilter',
            'productCode',
            'recurring',
            'invoicePrefix',
            'recurringAmount',
            'allowAccumulate',
            'maxAccumulateAmount',
            'recurringInterval',
            'recurringCount',
            'chargeNextDate',
            'chargeOnDate',
            'paymentExpiry',
            'promotionCode',
            'paymentRouteID',
            'fxProviderCode',
            'immediatePayment',
            'userDefined1',
            'userDefined2',
            'userDefined3',
            'userDefined4',
            'userDefined5',
            'statementDescriptor',
            'subMerchants',
            'locale',
            'frontendReturnUrl',
            'backendReturnUrl',
            'nonceStr',
            'uiParams',
            'customerAddress',
            '3DSecure2Params',
        ]
class BaseKwargsSerializer(serializers.Serializer):
    secret = serializers.CharField(max_length=200)
    baseurl = serializers.URLField(max_length=255, allow_blank=True)

class RequestKwargsSerializer(serializers.Serializer):
    merchantID = serializers.CharField(max_length=25)
    invoiceNo = serializers.CharField(max_length=50)
    description = serializers.CharField(max_length=255)
    amount = serializers.DecimalField(max_digits=12, decimal_places=5)
    currencyCode = serializers.CharField(max_length=3)


class TwoCTwoP:
    vendor_code = '2c2p'
    baseurl = 'https://sandbox-pgw.2c2p.com/payment/4.1'
    secret = None
    data = {}
    token_response = {}
    invoice_no = None
    nonce = None
    paid_amount = None


    @property
    def pt_data(self):
        data = self.data
        allow_pt_data = ALLOW_PT_DATA
        result = {}
        for i in allow_pt_data:
            if i in data:
                result.update({i: data.get(i)})

        return result

    @property
    def request_payload(self):
        return self.pt_data

    @property
    def transaction_attr(self):
        return {
            'vendor_code': self.vendor_code,
            'nonce': self.pt_data.get('nonceStr', None),
            'request_payload': self.pt_data,
            'invoice_no': self.pt_data.get('invoiceNo', None),
            'amount': self.pt_data.get('amount', 0)
        }
    
    @property
    def payment_status(self):
        status = self.backend_response.get('respCode', None)
        if status == SUCCESS_CODE:
            return 'success'
        return 'pending'
    
    @property
    def is_payment_success(self):
        status = self.backend_response.get('respCode', None)
        if status == SUCCESS_CODE:
            return True
        return False

    def set_nonce(self, nonce, nonce_path=None):
        self.data['nonceStr'] = nonce
        if nonce_path:
            set_(self.data, nonce_path, nonce)

    def is_valid_base_kwargs(self, pgw_kwargs, **kwargs):
        serializer = BaseKwargsSerializer(data=pgw_kwargs)
        if not serializer.is_valid():
            raise PaymentGatewayKwargException(serializer.errors)
        self.secret = pgw_kwargs.get('secret', None)
        baseurl = pgw_kwargs.get('baseurl', None)
        if baseurl:
            self.baseurl = baseurl.strip('/')

    def is_valid_request_kwargs(self, pgw_kwargs, **kwargs):
        self.is_valid_base_kwargs(pgw_kwargs)

        serializer = RequestKwargsSerializer(data=pgw_kwargs)
        if not serializer.is_valid():
            raise PaymentGatewayKwargException(serializer.errors)

        self.data = pgw_kwargs

    def payment_token_request(self, **kwargs):
        try:
            pt_payloaddata = jwt.encode(self.pt_data, self.secret, algorithm="HS256")
            pt_payload = {
                'payload': pt_payloaddata
            }

            apiurl = 'PaymentToken'
            url = f'{self.baseurl}/{apiurl}'
        except Exception as e:
            message = f'Cannot build payment gateway request payload. {str(e)}'
            raise PaymentGatewayException(detail=message)

        return requests.post(url, json=pt_payload)
    
    def request(self, **kwargs):
        return self.payment_token_request(**kwargs)
    
    def get_payment_page_url(self):
        return self.token_response.get('webPaymentUrl', None)
    
    def is_valid_request_response(self, r, raise_exception=True):
        if not status.is_success(r.status_code):
            if raise_exception:
                raise PaymentGatewayException(detail=r.text, status_code=r.status_code)
            return False
        try:
            response = r.json()
        except Exception as e:
            if raise_exception:
                raise PaymentGatewayConverResponseException(str(e))
            return False

        pt_payloaddata  = response.get('payload', None)
        if not pt_payloaddata:
            if raise_exception:
                raise  PaymentGatewayConverResponseException(response)
            return False

        try:
            pt_data = jwt.decode(pt_payloaddata, self.secret, algorithms=["HS256"])
        except Exception as e:
            if raise_exception:
                raise PaymentGatewayConverResponseException(str(e))
            return False

        self.token_response = pt_data

        return pt_data

    def is_valid_frontend_response(self, data, raise_exception=True):
        pgw_response = data.get('paymentResponse', None)
        if not pgw_response:
            if raise_exception:
                raise  PaymentGatewayConverResponseException('Frontend callback Fail')
            return False

        try:
            self.frontend_response = json.loads(base64.b64decode(pgw_response.encode()))
            self.invoice_no = self.frontend_response.get('invoiceNo', None)
        except Exception as e:
            if raise_exception:
                raise PaymentGatewayConverResponseException(str(e))
            return False

    def is_valid_backend_response(self, data, raise_exception=True, nonce_path=None, **kwargs): # NOSONAR
        pgw_response = data.get('payload', None)
        if not pgw_response:
            if raise_exception:
                raise  PaymentGatewayConverResponseException('Backend callback Fail')
            return False # NOSONAR
        
        try:
            self.backend_response = jwt.decode(pgw_response, self.secret, algorithms=["HS256"])
            self.paid_amount = get(self.backend_response, 'amount', None)
            self.invoice_no = self.backend_response.get('invoiceNo', None)
            if not self.invoice_no:
                return False # NOSONAR
            if nonce_path:
                self.nonce = get(self.backend_response, nonce_path, None)
                if not self.nonce:
                    return False # NOSONAR
        except Exception as e:
            if raise_exception:
                raise PaymentGatewayConverResponseException(str(e))
            return False # NOSONAR
