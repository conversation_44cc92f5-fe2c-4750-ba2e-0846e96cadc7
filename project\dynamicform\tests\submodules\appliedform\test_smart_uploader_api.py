from django.conf import settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test import TestCase
from dj_rest_auth.tests.mixins import TestsMixin, APIClient
from PIL import Image
from rest_framework import status
from unittest import mock
from dynamicform.models import Form, FormSetting, Answer, AnswerFile, AppliedFormSmartUploader
from dynamicform.submodules.appliedform.dynamicform import Dynamicform
from workspace.models import Workspace, Form as WorkspaceForm
from django.contrib.auth import get_user_model
from dynamicform.tests.mockupschema.smartuploader import MOCKUP_FORM_FRONTEND_SCHEMA
import tempfile

User = get_user_model()

MOCK_UPLOAD_FILE_PATH = (
    "dynamicform.submodules.appliedform.models.appliedform_smartuploader.AppliedFormSmartUploader.upload_file"
)
IMAGE_TYPE = "image/jpeg"


class SmartUploaderAPITests(TestCase, TestsMixin):
    def setUp(self):
        self.client = APIClient()
        settings.MIDDLEWARE = [
            "django.middleware.security.SecurityMiddleware",
            "django.contrib.sessions.middleware.SessionMiddleware",
            "django.contrib.auth.middleware.AuthenticationMiddleware",
            "app.middleware.JWTAuthenticationMiddleware",
            "crequest.middleware.CrequestMiddleware",
        ]
        settings.ENABLE_CREDIT_SYSTEM = False

        self.admin = User.objects.create_user(username="owner_admin", is_active=True)
        self.workspace = Workspace.create(slug="workspace_test", created_by=self.admin)

        frontend_schema = MOCKUP_FORM_FRONTEND_SCHEMA

        self.form = Form(name="test", slug="form_test_1", frontend_schema=frontend_schema)
        self.form.save()
        WorkspaceForm.objects.create(workspace=self.workspace, form=self.form)
        FormSetting.objects.create(settings={}, form=self.form)

        dynamicform = Dynamicform(applied_form=None, form=self.form, is_create=True)
        dynamicform.create()
        dynamicform.save()
        dynamicform.save_applied_form_obj()
        self.applied_form = dynamicform.applied_form
        self.application = dynamicform.application
        self.dynamicform = dynamicform

        self.token, _ = self.workspace.create_token("token_test")
        self.token.forms.add(self.form)
        self.token.set_permissions(permissions=["view_submission_detail", "edit_submission_detail"])

    def get_file(self):
        image = Image.new("RGB", (100, 100))
        tmp_file = tempfile.NamedTemporaryFile(suffix="_1.jpg")
        image.save(tmp_file)
        tmp_file.seek(0)
        return tmp_file

    def test_upload_file_api(self):
        url = f"/th/api/forms/{self.form.slug}/applied-forms/{self.applied_form.slug}/smartuploader/smart_uploader/files/"
        print(url)

        # fail validate input fail
        self.client.force_login(self.token.user)
        r = self.client.post(
            url,
            data={},
            format="multipart",
        )
        self.assertEqual(r.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)

        # validate fail
        tmp_file_1 = self.get_file()
        with mock.patch(MOCK_UPLOAD_FILE_PATH, mock.Mock(return_value={"pass_validation": False, "mock": True})):
            r = self.client.post(
                url,
                data={
                    "files": tmp_file_1,
                },
                format="multipart",
            )
            self.assertDictEqual(r.json(), {"files": ["File validation failed."]})
            self.assertEqual(r.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)

        # validate pass
        tmp_file_2 = self.get_file()
        with mock.patch(MOCK_UPLOAD_FILE_PATH, mock.Mock(return_value={"pass_validation": True, "mock": {}})):
            r = self.client.post(
                url,
                data={
                    "files": tmp_file_2,
                },
                format="multipart",
            )
            self.assertEqual(r.status_code, status.HTTP_201_CREATED)

        # fail page limit
        tmp_file_answer = self.get_file()
        tmp_file_3 = self.get_file()
        answer, _ = Answer.objects.get_or_create(applied_form=self.applied_form, question="smart_uploader", _value=[])
        AnswerFile.objects.create(
            answer=answer,
            name="aaa",
            mime_type=IMAGE_TYPE,
            detail=SimpleUploadedFile("test.jpg", content=tmp_file_answer.read(), content_type=IMAGE_TYPE),
        )
        with mock.patch(MOCK_UPLOAD_FILE_PATH, mock.Mock(return_value={"pass_validation": True, "mock": {}})):
            r = self.client.post(
                url,
                data={
                    "files": [tmp_file_3],
                },
                format="multipart",
            )
            self.assertDictEqual(r.json(), {"files": ["File exceeds the page limit."]})
            self.assertEqual(r.status_code, 413)

    def test_get_get_smartuploader_result_api(self):
        from smartuploader.models.smartuploader import SmartUploader

        self.client.force_login(self.token.user)
        url = f"/th/api/forms/{self.form.slug}/applied-forms/{self.applied_form.slug}/smartuploader/smart_uploader/result/"
        print(url)

        # no answer
        r = self.client.get(url)
        self.assertEqual(r.status_code, 200)
        self.assertDictEqual(
            r.json(),
            {
                "status": "incomplete",
                "data": {"result": {}, "message": None, "validation": {}, "files": []},
                "detail": "waiting_for_submit",
            },
        )

        # no submit
        tmp_file_answer = self.get_file()
        answer, _ = Answer.objects.get_or_create(applied_form=self.applied_form, question="smart_uploader", _value=[])
        ans_file = AnswerFile.objects.create(
            answer=answer,
            name="aaa",
            mime_type=IMAGE_TYPE,
            detail=SimpleUploadedFile("test.jpg", content=tmp_file_answer.read(), content_type=IMAGE_TYPE),
        )
        r = self.client.get(url)
        self.assertEqual(r.status_code, 200)
        self.assertDictEqual(
            r.json(),
            {
                "status": "incomplete",
                "data": {
                    "result": {},
                    "message": None,
                    "validation": {},
                    "files": [
                        {
                            "id": "aaa",
                            "preview": f"http://testserver/th/api/forms/{self.form.slug}/applied-forms/{self.applied_form.slug}/files/smart_uploader/{ans_file.id}/aaa/",  # NOSONAR
                            "type": IMAGE_TYPE,
                        }
                    ],
                },
                "detail": "waiting_for_submit",
            },
        )

        # submit
        smart_uploader = SmartUploader.objects.create(
            request_id="request_id", status="complete", raw_results={"result": {}}
        )
        AppliedFormSmartUploader.objects.create(
            applied_form=self.applied_form, answer=answer, smart_uploader=smart_uploader
        )
        r = self.client.get(url)
        self.assertEqual(r.status_code, 200)
        self.assertDictEqual(
            r.json(),
            {
                "status": "passed",
                "data": {
                    "result": {"fields": {}, "extracted_at": None, "page_count": None, "custom_fields": {}},
                    "message": None,
                    "validation": {},
                    "files": [
                        {
                            "id": "aaa",
                            "preview": f"http://testserver/th/api/forms/{self.form.slug}/applied-forms/{self.applied_form.slug}/files/smart_uploader/{ans_file.id}/aaa/",  # NOSONAR
                            "type": IMAGE_TYPE,
                        }
                    ],
                },
                "detail": None,
            },
        )
