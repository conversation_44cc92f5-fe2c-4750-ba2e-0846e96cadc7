
from enum import Enum


class LocalThaiPepListValidateInputResultTestCase:
    class ExpectResult(Enum):
        SUCCESS_VALIDATE = True
        FAILED_VALIDATE = False
        
    ### Set of each test case
    # - Test case name
    # - Test case input   
    # - Expect result
    TEST_CASE: list = [
        (
            ExpectResult.SUCCESS_VALIDATE.name,
            {
                'full_name': '<PERSON><PERSON>wat J<PERSON>urongparatipat',
                'last_name_list': [],
                'validate_only_lastname': False
            },
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}: last name not match',
            {
                'full_name': 'Apiwat Jaturong',
                'last_name_list': ['Jaturong'],
                'validate_only_lastname': True
            },
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: None value',
            {
                'full_name': None,
                'last_name_list': [],
                'validate_only_lastname': False
            },
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'Local Thai PEP List: Full Name cannot be empty',
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: empty value',
            {
                'full_name': '',
                'last_name_list': [],
                'validate_only_lastname': False
            },
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'Local Thai PEP List: Full Name cannot be empty',
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: last name not match',
            {
                'full_name': 'Apiwat Jaturong',
                'last_name_list': ['jatu'],
                'validate_only_lastname': True
            },
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'Local Thai PEP List: Full Name is invalid',
            },
        )
    ]
    

class LocalThaiPepListDataPointOptionsResultTestCase:
    class ExpectResult(Enum):
        ANY_FOUND = {
            'is_any_found': True,
            'is_all_found': False,
            'is_any_in_house_of_representative': False,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': False,
            'is_all_president_of_provincial': False,
            'is_any_minister': False,
            'is_all_minister': False,
            'is_any_in_police_armforces': False,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': False,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': False, 
            'is_any_senator': False,
        }
        ALL_FOUND = {
            'is_any_found': True,
            'is_all_found': True,
            'is_any_in_house_of_representative': False,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': False,
            'is_all_president_of_provincial': False,
            'is_any_minister': False,
            'is_all_minister': False,
            'is_any_in_police_armforces': False,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': False,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': False, 
            'is_any_senator': False,
        }
        ANY_HOUSE_OF_REPRESENTATIVE = {
            'is_any_found': True,
            'is_all_found': False,
            'is_any_in_house_of_representative': True,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': False,
            'is_all_president_of_provincial': False,
            'is_any_minister': False,
            'is_all_minister': False,
            'is_any_in_police_armforces': False,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': False,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': False, 
            'is_any_senator': False,
        }
        ALL_HOUSE_OF_REPRESENTATIVE = {
            'is_any_found': True,
            'is_all_found': True,
            'is_any_in_house_of_representative': True,
            'is_all_in_house_of_representative': True,
            'is_any_president_of_provincial': False,
            'is_all_president_of_provincial': False,
            'is_any_minister': False,
            'is_all_minister': False,
            'is_any_in_police_armforces': False,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': False,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': False, 
            'is_any_senator': False,
        }
        ANY_PRESIDENT_OF_PROVINCIAL = {
            'is_any_found': True,
            'is_all_found': False,
            'is_any_in_house_of_representative': False,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': True,
            'is_all_president_of_provincial': False,
            'is_any_minister': False,
            'is_all_minister': False,
            'is_any_in_police_armforces': False,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': False,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': False, 
            'is_any_senator': False,
        }
        ALL_PRESIDENT_OF_PROVINCIAL = {
            'is_any_found': True,
            'is_all_found': True,
            'is_any_in_house_of_representative': False,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': True,
            'is_all_president_of_provincial': True,
            'is_any_minister': False,
            'is_all_minister': False,
            'is_any_in_police_armforces': False,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': False,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': False, 
            'is_any_senator': False,
        }
        ANY_MINISTER = {
            'is_any_found': True,
            'is_all_found': False,
            'is_any_in_house_of_representative': False,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': False,
            'is_all_president_of_provincial': False,
            'is_any_minister': True,
            'is_all_minister': False,
            'is_any_in_police_armforces': False,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': False,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': False, 
            'is_any_senator': False,
        }
        ALL_MINISTER = {
            'is_any_found': True,
            'is_all_found': True,
            'is_any_in_house_of_representative': False,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': False,
            'is_all_president_of_provincial': False,
            'is_any_minister': True,
            'is_all_minister': True,
            'is_any_in_police_armforces': False,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': False,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': False, 
            'is_any_senator': False,
        }
        ANY_POLICE_ARMFORCES = {
            'is_any_found': True,
            'is_all_found': False,
            'is_any_in_house_of_representative': False,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': False,
            'is_all_president_of_provincial': False,
            'is_any_minister': False,
            'is_all_minister': False,
            'is_any_in_police_armforces': True,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': False,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': False, 
            'is_any_senator': False,
        }
        ALL_POLICE_ARMFORCES = {
            'is_any_found': True,
            'is_all_found': True,
            'is_any_in_house_of_representative': False,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': False,
            'is_all_president_of_provincial': False,
            'is_any_minister': False,
            'is_all_minister': False,
            'is_any_in_police_armforces': True,
            'is_all_in_police_armforces': True,
            'is_any_president_of_subdistrict': False,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': False, 
            'is_any_senator': False,
        }
        ANY_PRESIDENT_OF_SUBDISTRICT = {
            'is_any_found': True,
            'is_all_found': False,
            'is_any_in_house_of_representative': False,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': False,
            'is_all_president_of_provincial': False,
            'is_any_minister': False,
            'is_all_minister': False,
            'is_any_in_police_armforces': False,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': True,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': False, 
            'is_any_senator': False,
        }
        ALL_PRESIDENT_OF_SUBDISTRICT = {
            'is_any_found': True,
            'is_all_found': True,
            'is_any_in_house_of_representative': False,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': False,
            'is_all_president_of_provincial': False,
            'is_any_minister': False,
            'is_all_minister': False,
            'is_any_in_police_armforces': False,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': True,
            'is_all_president_of_subdistrict': True,
            'is_all_senator': False, 
            'is_any_senator': False,
        }
        ANY_SENATOR = {
            'is_any_found': True,
            'is_all_found': False,
            'is_any_in_house_of_representative': False,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': False,
            'is_all_president_of_provincial': False,
            'is_any_minister': False,
            'is_all_minister': False,
            'is_any_in_police_armforces': False,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': False,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': False, 
            'is_any_senator': True,
        }
        ALL_SENATOR = {
            'is_any_found': True,
            'is_all_found': True,
            'is_any_in_house_of_representative': False,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': False,
            'is_all_president_of_provincial': False,
            'is_any_minister': False,
            'is_all_minister': False,
            'is_any_in_police_armforces': False,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': False,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': True, 
            'is_any_senator': True,
        }
        
    class TestCaseInput(Enum):
        CURRENT_RESULT_TEST_ALL = None
        CURRENT_RESULT_TEST_ANY = {
            'is_any_found': False,
            'is_all_found': False,
            'is_any_in_house_of_representative': False,
            'is_all_in_house_of_representative': False,
            'is_any_president_of_provincial': False,
            'is_all_president_of_provincial': False,
            'is_any_minister': False,
            'is_all_minister': False,
            'is_any_in_police_armforces': False,
            'is_all_in_police_armforces': False,
            'is_any_president_of_subdistrict': False,
            'is_all_president_of_subdistrict': False,
            'is_all_senator': False, 
            'is_any_senator': False
        }

    ### Set of each test case
    # - Test case name
    # - Test case input   
    # - Expect result
    TEST_CASE: list = [
        (
            ExpectResult.ANY_FOUND.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ANY.value,
                "thai_pep_info": {
                    "type": "สำหรับทดสอบเท่านั้น"
                },
            }, 
            ExpectResult.ANY_FOUND.value
        ),
        (
            ExpectResult.ALL_FOUND.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ALL.value,
                "thai_pep_info": {
                    "type": "สำหรับทดสอบเท่านั้น"
                },
            },
            ExpectResult.ALL_FOUND.value
        ),
        (
            ExpectResult.ANY_HOUSE_OF_REPRESENTATIVE.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ANY.value,
                "thai_pep_info": {
                    "type": "สส.เขต"
                },
            },
            ExpectResult.ANY_HOUSE_OF_REPRESENTATIVE.value,
        ),
        (
            ExpectResult.ALL_HOUSE_OF_REPRESENTATIVE.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ALL.value, 
                "thai_pep_info": {
                    "type": "สส.เขต",
                },
            },
            ExpectResult.ALL_HOUSE_OF_REPRESENTATIVE.value
        ),
        (
            ExpectResult.ANY_HOUSE_OF_REPRESENTATIVE.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ANY.value,
                "thai_pep_info": {
                    "type": "สส.บัญชีรายชื่อ"
                },
            },
            ExpectResult.ANY_HOUSE_OF_REPRESENTATIVE.value,
        ),
        (
            ExpectResult.ALL_HOUSE_OF_REPRESENTATIVE.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ALL.value, 
                "thai_pep_info": {
                    "type": "สส.บัญชีรายชื่อ",
                },
            },
            ExpectResult.ALL_HOUSE_OF_REPRESENTATIVE.value
        ),
        (
            ExpectResult.ANY_PRESIDENT_OF_PROVINCIAL.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ANY.value, 
                "thai_pep_info": {
                    "type": "อบจ",
                },
            },
            ExpectResult.ANY_PRESIDENT_OF_PROVINCIAL.value
        ),
        (
            ExpectResult.ALL_PRESIDENT_OF_PROVINCIAL.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ALL.value, 
                "thai_pep_info": {
                    "type": "อบจ",
                },
            },
            ExpectResult.ALL_PRESIDENT_OF_PROVINCIAL.value
        ),
        (
            ExpectResult.ANY_MINISTER.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ANY.value,
                "thai_pep_info": {
                    "type": "รัฐมนตรีและรองรัฐมนตรี",
                },
            },
            ExpectResult.ANY_MINISTER.value
        ),
        (
            ExpectResult.ALL_MINISTER.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ALL.value,
                "thai_pep_info": {
                    "type": "รัฐมนตรีและรองรัฐมนตรี",
                },
            },
            ExpectResult.ALL_MINISTER.value
        ),
        (
            ExpectResult.ANY_POLICE_ARMFORCES.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ANY.value,
                "thai_pep_info": {
                    "type": "ตำรวจ",
                },
            },
            ExpectResult.ANY_POLICE_ARMFORCES.value
        ),
        (
            ExpectResult.ALL_POLICE_ARMFORCES.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ALL.value,
                "thai_pep_info": {
                    "type": "ตำรวจ",
                },
            },
            ExpectResult.ALL_POLICE_ARMFORCES.value
        ),
        (
            ExpectResult.ANY_POLICE_ARMFORCES.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ANY.value,
                "thai_pep_info": {
                    "type": "ทหาร",
                },
            },
            ExpectResult.ANY_POLICE_ARMFORCES.value
        ),
        (
            ExpectResult.ALL_POLICE_ARMFORCES.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ALL.value,
                "thai_pep_info": {
                    "type": "ทหาร",
                },
            },
            ExpectResult.ALL_POLICE_ARMFORCES.value
        ),
        (
            ExpectResult.ANY_PRESIDENT_OF_SUBDISTRICT.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ANY.value, 
                "thai_pep_info": {
                    "type": "อบต",
                },
            },
            ExpectResult.ANY_PRESIDENT_OF_SUBDISTRICT.value
        ),
        (
            ExpectResult.ALL_PRESIDENT_OF_SUBDISTRICT.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ALL.value, 
                "thai_pep_info": {
                    "type": "อบต",
                },
            },
            ExpectResult.ALL_PRESIDENT_OF_SUBDISTRICT.value
        ),
        (
            ExpectResult.ANY_SENATOR.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ANY.value, 
                "thai_pep_info": {
                    "type": "สว",
                },
            },
            ExpectResult.ANY_SENATOR.value
        ),
        (
            ExpectResult.ALL_SENATOR.name,
            {
                "current_result": TestCaseInput.CURRENT_RESULT_TEST_ALL.value, 
                "thai_pep_info": {
                    "type": "สว",
                },
            },
            ExpectResult.ALL_SENATOR.value
        ),
    ]


class LocalThaiPEPAPIServiceTestCase:
    ''' Collects all test cases that use in 
        test_local_thai_pep_api_service
    '''
    class ExpectResult(Enum):
        SUCCESS_VALIDATE = True
        FAILED_VALIDATE = False
        
    ### Set of each test case
    # - Test case name
    # - Test case input   
    # - Expect result
    TEST_CASE: list = [
        (
            f'{ExpectResult.SUCCESS_VALIDATE.name}',
            {
                'status_code': 200,
                'result': {
                    "result": {
                        "data": {}
                    }
                },
                'error': None
            },
            {
                "is_success": ExpectResult.SUCCESS_VALIDATE.value,
                "error_message": None,
            },
        ),
        (
            f'{ExpectResult.FAILED_VALIDATE.name}: 404 not found.',
            {
                'status_code': 404,
                'result': {'detail': 'not found'},
                'error': None
            },
            {
                "is_success": ExpectResult.FAILED_VALIDATE.value,
                "error_message": 'Local Thai PEP List: Local Thai PEP List service is temporarily unavailable: 404',
            },
        )
    ]
