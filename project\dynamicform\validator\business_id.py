import re
from typing import Any

from django.utils import timezone
from spotlight.rules import Rule

VALID_PQ_CODES = {
    'LP',
    'LL',
    'FC',
    'PF',
    'RF',
    'MQ',
    'MM',
    'NB',
    'CC',
    'CS',
    'MB',
    'FM',
    'GS',
    'DP',
    'CP',
    'NR',
    'CM',
    'CD',
    'MD',
    'HS',
    'VH',
    'CH',
    'MH',
    'CL',
    'XL',
    'CX',
    'HC',
    'RP',
    'TU',
    'TC',
    'FB',
    'FN',
    'PA',
    'PB',
    'SS',
    'MC',
    'SM',
    'GA',
}


class BusinessIDRule(Rule):
    """Business ID"""

    name = 'business_id'

    def passes(self, field: str, value: Any, parameters: list[str], validator) -> bool:
        self.message_fields = dict(field=field)

        if not parameters:
            return False

        business_id = value
        question_key = parameters.pop(0)
        contry_code = validator.data.get(question_key)

        if not contry_code:
            return True

        all_validated_cases = self._validate_business_id(business_id, contry_code)

        return any(all_validated_cases)

    @property
    def message(self) -> str:
        return 'The {field} is invalid'

    def _validate_business_id(self, business_id: str, contry_code: str) -> list[bool] | bool:
        lookup_table = {
            'THA': [
                self._validate_business_id_th_brn,
            ],
            'JPN': [
                self._validate_business_id_jp_bcn,
            ],
            'HKG': [
                self._validate_business_id_hk_brn,
                self._validate_business_id_hk_crn,
            ],
            'MYS': [
                self._validate_business_id_my_crn_old,
                self._validate_business_id_my_crn,
            ],
            'SGP': [
                self._validate_business_id_sg_uen_a,
                self._validate_business_id_sg_uen_b,
                self._validate_business_id_sg_uen_c,
            ],
            'PHL': [
                self._validate_business_id_ph_brn,
            ],
            'AUS': [
                self._validate_business_id_au_acn,
            ],
        }

        try:
            result = [i(business_id) for i in lookup_table.get(contry_code, [])]
        except Exception:
            result = False

        return result

    def _validate_business_id_th_brn(self, business_id: str):
        re_pattern = r'^\d{13}$'
        result = re.match(re_pattern, business_id)

        return result

    def _validate_business_id_jp_bcn(self, business_id: str): # NOSONAR
        re_pattern = r'^\d{13}$'
        result = re.match(re_pattern, business_id)

        return result

    def _validate_business_id_hk_brn(self, business_id: str):
        re_pattern = r'^\d{8}$'
        result = re.match(re_pattern, business_id)

        return result

    def _validate_business_id_hk_crn(self, business_id: str):
        re_pattern = r'^\d{7}$'
        result = re.match(re_pattern, business_id)

        return result
    
    def _validate_business_id_my_crn_old(self, business_id: str):

        re_pattern = r'^\d{7}-?[A-Z]$'
        result =  re.match(re_pattern, business_id)

        return result
    
    def _validate_business_id_my_crn(self, business_id: str):
        
        re_pattern = r'\d{4}\d{2}\d{6}'
        result = re.match(re_pattern, business_id)
        if not result:
            return False
        
        current_year = timezone.now().year
        if int(business_id[0:4]) > current_year:
            return False
        
        allow_type_of_business_activity_my = ['01', '02', '03', '04']
        if business_id[4:6] not in allow_type_of_business_activity_my:
            return False
        
        return True

    def _validate_business_id_sg_uen_a(self, business_id: str):
        re_pattern = r'^^\d{8}[A-Z]$'
        result = re.match(re_pattern, business_id)

        return result

    def _validate_business_id_sg_uen_b(self, business_id: str):
        current_year = timezone.now().year
        re_pattern = r'^\d{4}\d{5}[A-Z]$'
        match_result = re.match(re_pattern, business_id)
        year_of_issuance = business_id[:4]

        try:
            is_valid_year_of_issuance = int(year_of_issuance) <= current_year
        except Exception:
            is_valid_year_of_issuance = False

        return match_result and is_valid_year_of_issuance

    def _validate_business_id_sg_uen_c(self, business_id: str):
        current_year = timezone.now().year
        year_of_issuance_codes = {
            'T': 2000,
            'S': 1900,
            'R': 1800,
        }
        re_pattern = r'^[TSR]\d{2}[A-Z]{2}\d{4}[A-Z]$'
        match_result = re.match(re_pattern, business_id)
        year_of_issuance_code = business_id[:1]
        base_year_of_issuance = year_of_issuance_codes.get(year_of_issuance_code)

        try:
            year_of_issuance_count = business_id[1:3]
            year_of_issuance = base_year_of_issuance + int(year_of_issuance_count)
            is_valid_year_of_issuance = year_of_issuance <= current_year
        except Exception:
            is_valid_year_of_issuance = False

        pq_code = business_id[3:5]
        is_valid_pq_code = pq_code in VALID_PQ_CODES

        return match_result and is_valid_year_of_issuance and is_valid_pq_code
    
    def _validate_business_id_ph_brn(self, business_id: str):
        re_pattern = r'[A-Z]{2}\d{8}'
        result =  re.match(re_pattern, business_id)

        return result
    
    def _validate_business_id_au_acn(self, business_id: str):
        re_pattern = r'^\d{9}$'
        result =  re.match(re_pattern, business_id)

        return result
