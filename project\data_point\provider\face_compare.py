from data_point.base import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from workspace.events import ALL_EKYC_LIVENESS_ITEM_TYPES, ALL_EKYC_DOCUMENT_ITEM_TYPES

DATAPOINT_OPTIONS = {
    'face_compare_score ': {
        'label': 'The Face comparison score between selife and face image on ID Documents',
        'type': 'number',
    },
    'face_match_level': {
        'label': 'The Face Similarity Level',
        'type': 'string',
    },
}


class FaceCompare(EKYCBase):
    name = 'facial_comparison'
    title = 'Facial Comparison'
    sub_title = ''
    description = ''
    icon = ''
    option_icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/uppass_verification_services_36/Facial-Comparison_36.png'
    option_icon_name = "lucide:scan-face"
    data_point_opitons = DATAPOINT_OPTIONS
    required_item_types = [ALL_EKYC_LIVENESS_ITEM_TYPES, ALL_EKYC_DOCUMENT_ITEM_TYPES]
    required_item_types_auto_connect = True

    def get_data_result(self, **kwargs):
        result = {}
        ekyc = self.get_ekyc()
        face_compare_result, _ = ekyc.getResultFaceCompare()
        face_compare_score = face_compare_result.get('score', 0)
        result['face_compare_score'] = face_compare_score
        result['face_match_level'] = self.get_face_match_level(face_compare_score)

        return result

    def get_face_match_level(self, face_compare_score: int) -> str:
        if face_compare_score > 85:
            result = 'Same Person'
        elif face_compare_score > 69:
            result = 'Very Similar'
        elif face_compare_score >= 60:
            result = 'Similar'
        else:
            result = 'Not Similar'

        return result
