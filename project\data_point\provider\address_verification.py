import logging

from data_point.base import DataPointBase
from django.apps import apps

logger: logging.Logger = logging.getLogger(__name__)

DATAPOINT_OPTIONS = {
    'address_match': {
        'label': 'Check if the address is matched with the address provided in the PDF file',
        'type': 'boolean',
    },
}


class AddressVerification(DataPointBase):
    name = 'address_verification'
    title = 'Address Verification'
    sub_title = ''
    description = ''
    option_icon = 'https://cdn.uppass.io/projects/uppass/img/datapoint/uppass_verification_services_36/Address_Ver_36.png'
    data_point_opitons = DATAPOINT_OPTIONS
    required_item_types = [['UtilityBill']]
    required_item_types_auto_connect = True
    
    
    def get_data_result(self, **kwargs):
        result = {}
        raw_result = {
            'address_match': [True],
        }
        utility_bills = self.get_utility_bill_data()

        for i in utility_bills:
            validation_address = i.raw_results.get('validation_address', True)
            raw_result['address_match'].append(validation_address)

        result['address_match'] = all(raw_result['address_match'])

        return result

    def get_utility_bill_data(self):
        # fmt: off
        AppliedFormUtilityBill = apps.get_model('dynamicform', 'AppliedFormUtilityBill')  # NOSONAR
        applied_form_utility_bill = AppliedFormUtilityBill.objects.select_related(
            'utility_bill'
        ).filter(applied_form_id=self.applied_form.id)
        # fmt: on

        return applied_form_utility_bill
