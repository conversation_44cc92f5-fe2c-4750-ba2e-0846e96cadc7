import os
import hashlib
from cryptography.hazmat.primitives.ciphers.aead import AESGCM as CryptographyAESGCM
from django.conf import settings

AES_GCM_SECRET_KEY = settings.AES_GCM_SECRET_KEY


class AESGCM:
    def __init__(self, secret_key: str = AES_GCM_SECRET_KEY) -> None:
        if secret_key == AES_GCM_SECRET_KEY:
            self._secret_key = bytes.fromhex(secret_key)
        else:
            self._secret_key = secret_key.encode()
        self._cipher: CryptographyAESGCM = CryptographyAESGCM(self._secret_key)

    def encrypt(self, plain_text: str) -> str:
        nonce = os.urandom(12)
        cipher_text = self._cipher.encrypt(nonce, plain_text.encode(), None)
        cipher_text = f'{nonce.hex()}{cipher_text.hex()}'

        return cipher_text
    
    def fix_encrypt(self, plain_text: str) -> str:
        nonce = hashlib.sha256(plain_text.encode()).digest()[:12]
        cipher_text = self._cipher.encrypt(nonce, plain_text.encode(), None)
        cipher_text = f'{nonce.hex()}{cipher_text.hex()}'

        return cipher_text

    def decrypt(self, cipher_text_hex: str) -> str:
        cipher_text = bytes.fromhex(cipher_text_hex)
        plain_text = self._cipher.decrypt(cipher_text[:12], cipher_text[12:], None)
        plain_text = plain_text.decode()

        return plain_text
