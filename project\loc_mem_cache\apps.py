from django.apps import AppConfig

class LocMemCacheConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'loc_mem_cache'

    def ready(self):
        from loc_mem_cache.patch import install_logmemcache
        from .signals import connect_signals
        from .conf import settings

        if not settings.LOCMEMCACHE_ENABLED:
            return

        install_logmemcache()
        connect_signals()
