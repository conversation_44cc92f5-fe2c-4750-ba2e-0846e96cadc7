from django.db import models
from dynamicform.submodules.model import <PERSON><PERSON>ow, SoftDelete
from dynamicform.util import reverse
from ..answerfile.models import AnswerFile


class AnswerFilePreview(AutoNow, SoftDelete):
    answerfile = models.ForeignKey(AnswerFile, on_delete=models.SET_NULL, null=True)
    name = models.CharField(max_length=191, null=True, db_index=True)
    detail = models.FileField(upload_to="dynamicform", max_length=191)

    def delete(self, *args, **kwargs):
        if hasattr(self.detail, "delete"):
            self.detail.delete()
        super().delete(*args, **kwargs)

    @property
    def mime_type(self):
        return "image/png"

    @property
    def item_url(self):
        pattern = {
            "form_slug": self.answerfile.answer.applied_form.form.slug,
            "applied_form_slug": self.answerfile.answer.applied_form.slug,
            "file_id": self.pk,
            "file_name": self.name,
        }

        return reverse("dynamicform:answerfilepreview-item", **pattern)

    def info(self, request=None, report=False, **kwargs):
        result = self.submit_info(request=request, report=report, **kwargs)

        return result

    def get_item_url(self, report=False, applied_form=None, form=None, question=None, **kwargs):
        if not applied_form or not form or not question:
            return  self.answerfile.item_url if self.answerfile.password and not report else self.item_url

        pattern = {
            "form_slug": form.slug,
            "applied_form_slug": applied_form.slug,
            "file_id": self.pk,
            "file_name": self.name,
        }
        return reverse("dynamicform:answerfilepreview-item", **pattern)

    def submit_info(self, request=None, report=False, **kwargs):
        return {
            "id": self.name,
            "preview": self.get_item_url(report=report, **kwargs),
            "type": self.answerfile.mime_type if self.answerfile.password and not report else self.mime_type,
        }

    @property
    def preview_url(self):
        return self.detail.url
