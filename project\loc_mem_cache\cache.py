from django.core.cache import cache
import hashlib
import pickle

KEY_REGISTRY_PREFIX = 'lmc:keys'  # used to store per-model key index
KEY_PREFIX = 'lmc:data'


def get_cache_model_prefix(model, slug: str=None):
    key = f"{model._meta.app_label}.{model._meta.model_name}"
    if slug:
        return f"{key}:{slug}"
    return key


def get_cache_key(model, op, kwargs, slug: str=None):
    cache_model_prefix = get_cache_model_prefix(model, slug=slug)
    hash_kwargs = hashlib.md5(pickle.dumps(str(kwargs))).hexdigest()
    return f"{KEY_PREFIX}:{cache_model_prefix}:{op}:{hash_kwargs}"


def get_registry_key(model, slug: str=None):
    cache_model_prefix = get_cache_model_prefix(model, slug=slug)
    key = f"{KEY_REGISTRY_PREFIX}:{cache_model_prefix}"
    if slug:
        return f"{key}:{slug}"

    return key


def cache_get_or_set(model, op, kwargs, func, timeout=300):
    slug = kwargs.get("slug", None)

    cache_key = get_cache_key(model, op, kwargs, slug=slug)
    registry_key = get_registry_key(model, slug=slug)

    result = cache.get(cache_key)
    if result is not None:
        return result

    result = func()
    try:
        cache.set(cache_key, result, timeout)
    except Exception as e:
        return result

    # Update registry
    cache_keys = cache.get(registry_key, set())
    cache_keys.add(cache_key)
    cache.set(registry_key, cache_keys, timeout)
    
    return result


def invalidate_model_cache(sender, instance, **kwargs):
    slug = getattr(instance, "slug", None)
    registry_key = get_registry_key(model=sender, slug=slug)

    cache_keys = cache.get(registry_key)
    if not cache_keys:
        return

    for cache_key in cache_keys:
        cache.delete(cache_key)

    cache.delete(registry_key)
