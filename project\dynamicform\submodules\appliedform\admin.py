from django.contrib import admin, messages
from django.utils.safestring import mark_safe
from django.utils.translation import ngettext
from django.utils.html import format_html
from django.urls import path, reverse
from dynamicform.models import Answer, AnswerFile, AppliedFormLog, AppliedFormPGWTransaction
from dynamicform.submodules.appliedform.models.appliedform_smartuploader import AppliedFormSmartUploader
from .models import AppliedForm


class AnswerInline(admin.TabularInline):
    model = Answer
    fields = ("step", "section", "question", "label", "_value")
    readonly_fields = ("step", "section", "label")
    extra = 1
    classes = ["collapse"]

    def label(self, answer):
        backend_schema = answer.applied_form.form.backend_schema
        try:
            items = backend_schema["items"]
            for key, item in items.items():
                if (
                    answer.step == item["step"]
                    and answer.section == item["section"]
                    and answer.question == item["name"]
                ):
                    return mark_safe(item["display"]["label"])
        except:
            pass

        return mark_safe("")


class AnswerFileInline(admin.TabularInline):
    model = AnswerFile
    fields = ("name", "mime_type", "detail")
    extra = 1
    classes = []


class LogInline(admin.TabularInline):
    model = AppliedFormLog
    readonly_fields = (
        "step",
        "section",
        "action",
        "session",
        "ip_address",
        "user_agent",
        "detail",
        "is_fail",
        "message",
        "created_at",
    )
    extra = 1
    classes = ["collapse"]


class PaymentTransacrionInline(admin.TabularInline):
    model = AppliedFormPGWTransaction
    extra = 1
    classes = ["collapse"]


class AppliedFormAdmin(admin.ModelAdmin):
    list_display = ["no", "slug", "form", "is_tracking", "created_at", "submitted_at", "updated_at"]
    inlines = [
        AnswerInline,
        LogInline,
        PaymentTransacrionInline,
    ]
    list_filter = ("submitted_at", "form")
    search_fields = ("slug", "form__slug")
    actions = ["softdelete"]
    fields = [
        "id",
        "slug",
        "form",
        "step",
        "section",
        "submitted_at",
        "is_tracking",
        "tracking_at",
        "disabled_at",
        "created_at",
        "updated_at",
        "deleted_at",
        "drop_off_at",
    ]
    readonly_fields = ["id", "slug", "created_at", "updated_at"]
    autocomplete_fields = ["form"]

    def no(self, obj):
        return obj.no_format

    def has_delete_permission(self, request, obj=None):
        return False

    def softdelete(self, request, queryset):
        deleted = queryset.delete()
        self.message_user(
            request,
            ngettext(
                "%d item was successfully delete (Soft).",
                "%d items were successfully delete (Soft).",
                deleted,
            )
            % deleted,
            messages.SUCCESS,
        )

    softdelete.short_description = "Soft Delete"


admin.site.register(AppliedForm, AppliedFormAdmin)


class AnswerAdmin(admin.ModelAdmin):
    list_display = [
        "applied_form",
        "step",
        "section",
        "question",
    ]
    inlines = [AnswerFileInline]
    search_fields = [
        "applied_form__slug",
        "question",
    ]


admin.site.register(Answer, AnswerAdmin)


class AppliedFormSmartUploaderAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "_applied_form",
        "_answer",
        "_smart_uploader",
        "page_count",
        "created_at",
        "updated_at",
    ]
    actions = ["recalculate_page_count"]
    inlines = []
    exclude = []
    search_fields = [
        "applied_form__slug",
        "answer__question",
    ]

    def get_fields(self, request, obj=None):
        fields = list(super().get_fields(request, obj=obj))
        if not obj:
            fields.remove("_answer_files")
        return fields

    def has_add_permission(self, request, obj=None):
        return False

    def _applied_form(self, obj: AppliedFormSmartUploader):
        url = reverse("admin:dynamicform_appliedform_change", args=[obj.applied_form.id])
        return format_html(f"<a href='{url}'>{obj.applied_form.slug}</a>")

    def _answer(self, obj: AppliedFormSmartUploader):
        url = reverse("admin:dynamicform_answer_change", args=[obj.answer.id])
        return format_html(f"<a href='{url}'>{obj.answer.question}</a>")

    def _smart_uploader(self, obj: AppliedFormSmartUploader):
        if obj.smart_uploader:
            url = reverse("admin:smartuploader_smartuploader_change", args=[obj.smart_uploader.id])
            return format_html(f"<a href='{url}'>{obj.smart_uploader.id}</a>")
        else:
            return "-"

    def recalculate_page_count(self, request, queryset):
        from dynamicform.submodules.appliedform.util import check_pdf_pages

        updated = 0
        for obj in queryset:
            try:
                # Get all answer files for this uploader
                answer_files = obj.answer_files
                if answer_files:
                    # Calculate total page count
                    total_pages = sum([check_pdf_pages(answer_file.detail) for answer_file in answer_files])

                    # Update the page_count field
                    obj.page_count = total_pages
                    obj.save(update_fields=["page_count"])
                    updated += 1
            except Exception as e:
                self.message_user(
                    request,
                    f"Error recalculating page count for {obj.id}: {str(e)}",
                    messages.ERROR,
                )

        self.message_user(
            request,
            ngettext(
                "%d item had its page count successfully recalculated.",
                "%d items had their page counts successfully recalculated.",
                updated,
            )
            % updated,
            messages.SUCCESS,
        )

    recalculate_page_count.short_description = "Recalculate page count for selected items"


admin.site.register(AppliedFormSmartUploader, AppliedFormSmartUploaderAdmin)
