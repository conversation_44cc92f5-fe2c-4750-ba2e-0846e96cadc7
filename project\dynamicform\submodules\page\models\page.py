from django.apps import apps
from django.conf import settings
from django.db import models
from django.utils.translation import get_language, gettext_lazy as _
from rest_framework.exceptions import PermissionDenied
from dynamicform.submodules.model import AutoNow, SoftDelete
from dynamicform.schema.collecttext import collect_text
from dynamicform.schema.trans import collect_trans, trans as schema_trans
import json
import os
from ...form.external_api_gitlab import list_commits, get_file, create_commit
from dynamicform.util import current_user

GITLAB_LINKED_ENV = settings.GITLAB_LINKED_ENV
********************* = settings.*********************


DATABASE_MODEL_USE_DB = settings.DATABASE_MODEL_USE_DB.get("page", {})

DEFAULT_SLUG = "default_"


class Category(models.TextChoices):
    LANDING = "landing", _("landing")
    THANKS = "thanks", _("thanks")
    EMBED = "embed", _("embed")


class Page(AutoNow, SoftDelete):
    name = models.CharField(max_length=191, unique=True)
    _schema = models.TextField(db_column="schema", default={})
    form = models.ForeignKey("Form", on_delete=models.CASCADE, default=None, blank=True, null=True)
    category = models.CharField(
        max_length=10,
        choices=Category.choices,
        db_index=True,
        default=None,
        blank=True,
        null=True,
    )

    def __str__(self):
        return "name: {}".format(self.name)

    @property
    def schema(self):
        return json.loads(self._schema)

    @schema.setter
    def schema(self, value):
        self._schema = json.dumps(value)

    def get_answers(self, applied_form):
        fields = self.schema.get("fields", None)
        if not fields:
            return {}

        forms = {}
        for field in fields:
            try:
                form_slug, answer = field.split(".")
            except:
                continue
            if form_slug not in forms:
                forms[form_slug] = []
            forms[form_slug].append(answer)

        info_answers = {}

        for form_slug, answers in forms.items():
            if applied_form.form.slug == form_slug:
                info_answers.update(applied_form.get_answers(filter_questions=answers))
            application_appliedform = applied_form.applicationappliedform_set.filter(
                applied_form__form__slug=form_slug
            ).first()
            if application_appliedform:
                related_applied_form = application_appliedform.applied_form
                info_answers.update(related_applied_form.get_answers(filter_questions=answers))

        return info_answers

    def can_view_page(self, applied_form): # NOSONAR
        fields = self.schema.get("fields", None)
        if not fields:
            return True # NOSONAR

        can_view = applied_form.can_view_page()
        if not can_view:
            raise PermissionDenied()

        return True # NOSONAR

    def update_lang(self, language):
        LocalePage = apps.get_model("dynamicform", "LocalePage") # NOSONAR
        LocalePage.objects.filter(page=self, language=language).delete()
        schema = self.schema
        text = collect_text(schema)
        for key, value in text.items():
            if type(value) == dict:
                value = json.dumps(value)
            LocalePage.objects.update_or_create(page=self, key=key, language=language, defaults={"text": value})

    def trans(self):
        lang = get_language()
        schema = self.schema
        if lang:
            text = self.localepage_set.filter(language=lang)
            return schema_trans(schema, text)

        return schema

    def get_trans(self):
        LocalePage = apps.get_model("dynamicform", "LocalePage") # NOSONAR
        use_db = DATABASE_MODEL_USE_DB.get("read", "default")
        localepage_set = LocalePage.objects.using(use_db)
        language = get_language()
        trans = localepage_set.filter(page_id=self.id, language=language)
        result = collect_trans(trans, schema=self.schema)
        return result

    def get_form_slug(self):
        if not self.form:
            return None
        return self.form.slug

    def get_form_settings(self, path):
        if not self.form:
            return {}
        return self.form.get_settings(path=path)

    @property
    def trans_schema(self):
        return self.trans()

    @property
    def info(self):
        return {
            **self.trans_schema,
            "trans": self.get_trans(),
            "schema_config": self.get_form_settings(path="schema_config"),
            "form": self.get_form_slug(),
            "category": self.category,
        }

    @staticmethod
    def init_page(form, name, schema_json_file, category):
        page = Page()
        page.form = form
        page.name = name
        page.category = category
        path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        path = os.path.join(path, "schema", schema_json_file)
        with open(path, "r") as f:
            schema = f.read()
            schema = schema.replace("{{form_slug}}", form.slug)

        schema = json.loads(schema)
        page.schema = schema
        page.save()
        return page

    @staticmethod
    def init_landing_page(form):
        form = form
        name = f"{form.slug}"
        schema_json_file = "landing.json"
        category = Category.LANDING
        page = Page.init_page(form, name, schema_json_file, category)
        return page

    @property
    def trans_schema(self):
        return self.trans()
  
    @property
    def info(self):
        return {
            **self.trans_schema,
            'trans': self.get_trans(),
            'schema_config': self.form.get_settings(path='schema_config'),
            'form': self.get_form_slug(),
            'category': self.category
        }
            
    @staticmethod
    def init_thanks_page(form):
        form = form
        name = f"thanks-{form.slug}"
        schema_json_file = "thanks.json"
        category = Category.THANKS
        page = Page.init_page(form, name, schema_json_file, category)
        return page

    def replace_str_in_schema(self, str_1, str_2):
        self._schema = self._schema.replace(str_1, str_2)

    @staticmethod
    def commit_save(
        form_slug: str,
        form_locale: str,
        pages=[],
        commit_message="",
    ):
        if not GITLAB_LINKED_ENV:
            return
            
        # Prepare data for commit
        user = current_user()
        form_slug = form_slug or DEFAULT_SLUG
        branch_name = f"{GITLAB_LINKED_ENV}/{*********************}/{form_slug}"
        locale = form_locale

        # Get ref to identify the start version_sha (HEAD)
        is_new_branch = False
        commits = list_commits(ref=branch_name)
        if len(commits) == 0:
            is_new_branch = True
            commits = list_commits(ref=f"{GITLAB_LINKED_ENV}/{*********************}")
            if len(commits) == 0:
                commits = list_commits(ref=f"{GITLAB_LINKED_ENV}")
                if len(commits) == 0:
                    commits = list_commits(ref="")
        ref = commits[0]["id"] if len(commits) > 0 else None

        contents = []
        file_paths = []
        is_new_files = []
        message_lines = []

        if commit_message:
            message_lines.append(commit_message)
        message_lines.append("files:")

        for page in pages:
            # Prepare content
            updated_schema_json = json.loads(page.schema)
            if updated_schema_json.get("version_sha"):
                del updated_schema_json["version_sha"]
            updated_schema_str = json.dumps(updated_schema_json, indent=2, ensure_ascii=False)
            contents.append(updated_schema_str)

            # Prepare file path
            page_slug = page.name
            file_path = f"projects/{*********************}/pages/{page_slug}/page_{locale}.json"
            file_paths.append(file_path)

            message_lines.append(f" - {page.name}/page_{form_locale}.json")

            # Prepare is_new_file flag
            response_file = get_file(ref=ref, path=file_path)
            is_new_file = not bool(response_file)
            is_new_files.append(is_new_file)

        final_message = "\n".join(message_lines)

        # Create new commit
        response_new_commit = create_commit(
            branch=branch_name,
            message=final_message,
            paths=file_paths,
            contents=contents,
            start_sha=ref,
            is_new_files=is_new_files,
            is_new_branch=is_new_branch,
            author_email=user.email,
            author_name=user.username,
        )
        if response_new_commit:
            response_new_commit["branch_name"] = branch_name
        else:
            print("Cant create new commit")

        return response_new_commit

    @staticmethod
    def is_obj_allow_member(name, user, permission):
        page = Page.objects.filter(name=name).first()
        try:
            if permission.allow_api_token and user.is_api_token:
                if not page.form.token_set.filter(user=user).exists():
                    return False
                return user.has_perm(f"workspace.{permission.action_permission}")
            return page.form.form_of.workspace.is_allow_member(user, permission=permission)
        except Exception:
            return None