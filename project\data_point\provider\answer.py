from data_point.base import DynamicformBase
from decision_flow.helper import <PERSON><PERSON><PERSON><PERSON>LE_OPERATIONS
from pydash import get
import re


ANSWER_LIST_STRING_TYPE = [
    'Button',
    'InputText',
    'InputTelephone',
    'Textarea',
]
ANSWER_LIST_NUMBER_TYPE = [
    'InputNumber',
    'Slider',
    'SliderChoice',
]
ANSWER_LIST_JSON_TYPE = [
    'InputRadio',
    'InputRadioStaticIcon',
    'SingleSelectButton',
    'PopupChoice',
    'Select',
    'AdvanceSelect',
    'ChoiceTree',
    'ChoiceModal',
    'InputCheckbox',
    'MultipleSelectButton',
    'Multiselect',
    'CountrySelect',
    'NationalitySelect',
    'DataField',
]
ANSWER_LIST_NONE_TYPE = []
ANSWER_LIST_BOOLEAN_TYPE = []
ANSWER_LIST_DATE_TYPE = [
    'InputDate',
    'InputDateText',
]
ANSWER_LIST_FULL_NAME_BUILDER_TYPE = [
    'full_name',
]

ANSWER_BUSINESS_INFOMATION_BUILDER_TYPE = [
    'business_information',
]
DEFAULT_LANGUAGE = "en"


class Answer(DynamicformBase):
    name = "answer"
    title = "Answer"
    sub_title = ""
    description = ""
    icon = ""
    option_icon = "https://cdn.uppass.io/projects/uppass/img/datapoint/uppass_verification_services_36/Answer_36.png"
    option_icon_name = "lucide:message-circle"
    __list_full_name = {}
    item_labels = {}

    def get_data_result(self, values_list=[], **kwargs):
        full_name_question_name = self.get_full_name_question_name(values_list)
        values_list = values_list + full_name_question_name
        
        # Find key list from preset build type then add in value list for filter answers
        key_found_list, business_info_answers = self.get_business_info_question_name_info(values_list)
        values_list = values_list + key_found_list
        
        answers = self.applied_form.answer_set.filter(question__in=values_list)

        result = {}

        for answer in answers:
            # get decrypted value from answer
            backend_schema: dict = self.form.backend_schema
            items_dict: dict = backend_schema.get('items')
            answer_type = items_dict.get(answer.question, {}).get('type')
            value = answer.decrypted_value
            if answer_type in ANSWER_LIST_STRING_TYPE and isinstance(value, str) is False:
                value = str(value)
            result.update({answer.question: value})
        if self.dynamicform.answers:
            result.update(self.dynamicform.answers)

        full_name_values = self.get_full_name_value(result)
        result.update(full_name_values)
        
        # Find id to set in business_information key
        business_information_id = self.__get_business_information_value(result,business_info_answers)
        result.update(business_information_id)

        return result

    def get_options(self):
        backend_schema: dict = self.form.backend_schema
        items_dict: dict = backend_schema.get('items')
        options = {}

        # Get all fields dict from AddressAutoFill type
        address_fields_list: list[dict] = [
            value['fields'] for key, value in items_dict.items() 
            if str(get(value, 'type', '')).upper() == 'ADDRESSAUTOFILL'
        ]
        self.set_form_item_labels()
        for item_name in items_dict.keys():
            item_data: dict = items_dict.get(item_name)

            # Find that is address full
            is_address_full = False
            if address_fields_list:
                is_address_full = self.is_address_full_item(address_fields_list, item_name)
            
            # Exclude hidden items and not address full
            if (item_data.get('visible') is False or item_data.get('step_visible') is False )and not is_address_full:
                continue

            # Find step label to be the prefix
            step_index = -1
            step_label = ''
            step: dict = item_data.get('step', '')

            if step:
                steps_sections: list = backend_schema.get('steps_sections')
                step_list = list([ss['step'] for ss in steps_sections])
                step_keys = list(dict.fromkeys(step_list))  # Remove duplicates

                try:
                    step_index = step_keys.index(step)
                except Exception:
                    pass

            if step_index > -1:
                step_label = f'Step {step_index+1}'
            else:
                step_label = '(Hidden)'

            # Build label with format "Step 1 : My Label"
            # item_label = item_data.get('display', {}).get('label') or item_name
            item_label = self.get_item_label(item_name, item_data)
            label = f'{step_label} : {item_label}'

            data_type = item_data.get('type')
            builder_type = get(item_data, 'builder.type', None)
            is_boolean = item_data.get('boolean', None)

            if is_boolean:
                data_type = 'boolean'
            elif data_type in ANSWER_LIST_STRING_TYPE:
                data_type = 'string'
            elif data_type in ANSWER_LIST_NUMBER_TYPE:
                data_type = 'number'
            elif data_type in ANSWER_LIST_DATE_TYPE:
                data_type = 'date'
            elif data_type in ANSWER_LIST_BOOLEAN_TYPE:
                data_type = 'boolean'
            elif data_type in ANSWER_LIST_NONE_TYPE:
                data_type = 'null'
            elif data_type in ANSWER_LIST_JSON_TYPE:
                data_type = 'json'
            elif builder_type in ANSWER_LIST_FULL_NAME_BUILDER_TYPE:
                data_type = 'string'
            elif builder_type in ANSWER_BUSINESS_INFOMATION_BUILDER_TYPE:
                data_type = 'string'
            else:
                continue

            item_dict = {
                'label': label,
                'type': data_type,
                'builder_type': builder_type,
                'operations': AVAILABLE_OPERATIONS.get(data_type),
            }
            options.update({item_name: item_dict})

        return options

    def get_is_connected(self):
        return True

    def get_full_name_question_name(self, values_list):
        self.__list_full_name = {}
        items = self.form.backend_schema.get('items', {})
        result = []

        for item_name in values_list:
            item_data = items.get(item_name)
            builder_type = get(item_data, 'builder.type', None)
            if builder_type not in ANSWER_LIST_FULL_NAME_BUILDER_TYPE:
                continue

            first_name = get(item_data, 'fields.first_name', None)
            if first_name:
                result.append(first_name)
            middle_name = get(item_data, 'fields.middle_name', None)
            if middle_name:
                result.append(middle_name)
            last_name = get(item_data, 'fields.last_name', None)
            if last_name:
                result.append(last_name)

            self.__list_full_name.update({item_name: {
                'first_name': first_name,
                'middle_name': middle_name,
                'last_name': last_name,
            }})
            
        return result

    def get_business_info_question_name_info(self, values_list) -> tuple[list, dict] :
        ''' Return key_found_list to add with values_list and business_info_answer for get answer for business information
        '''
        business_info_answer = {}
        key_found_list = []
        
        items = self.form.backend_schema.get('items', {})
        for item_name in values_list:
            item_data = items.get(item_name)
            builder_type = get(item_data, 'builder.type', None)
            
            # Skip if build type is not business information
            if builder_type not in ANSWER_BUSINESS_INFOMATION_BUILDER_TYPE:
                continue
            
            # Field key for business information
            question_keys: list = ['id', 'name', 'country']
            
            # Loop for find answer key to get answer value in next step
            list_preset_updated = {}
            for key in question_keys:
                answer_from_key = get(item_data, f'fields.{key}', None)
                if answer_from_key:
                    key_found_list.append(answer_from_key)
                    list_preset_updated[key] = answer_from_key
            business_info_answer.update({item_name: list_preset_updated})
        return key_found_list, business_info_answer
    
    def get_full_name_value(self, answers):
        result = {}
        def update_full_name(name_key, full_name, item):
            name_question = item.get(name_key, None)
            if name_question:
                name = answers.get(name_question, None)
                if not name:
                    return full_name
                name = name.strip()
                if name != '':
                    full_name.append(name)
            return full_name

        for question_name, item in self.__list_full_name.items():
            full_name = []
            full_name = update_full_name('first_name', full_name, item)
            full_name = update_full_name('middle_name', full_name, item)
            full_name = update_full_name('last_name', full_name, item)
            if len(full_name) == 0:
                continue
            result.update({
                question_name: ' '.join(full_name)
            })
        
        return result

    def __get_business_information_value(self, answers, business_info_answers):
        '''Find id to return for business_information key'''
        result = {}
        business_id = None
        for question_name, item in business_info_answers.items():
            name_question = item.get('id', None)
            if name_question:
                business_id = answers.get(name_question, None)
            else :
                continue
            result.update({
                question_name: business_id
            })
        return result
    
    def is_address_full_item(self, 
                             address_fields_list: list[dict], 
                             item_name: str) -> bool:
        '''Check if the specified item name is present as a "full" field in any address fields.

        This function iterates over the list of address fields and checks whether the 
        given `item_name` matches the value of the 'full' key in any of the dictionaries 
        within `address_fields_list`. It is used to determine whether the item should be 
        excluded from certain options.

        Args:
            address_fields_list (list[dict]): A list of dictionaries, where each dictionary represents the fields of an address in the current submission.
            item_name (str): The name of the item to validate against the 'full' field.

        Returns:
            bool: Returns True if `item_name` is found in the 'full' field of any dictionary 
            in `address_fields_list`. Otherwise, returns False.
        '''
        for fields in address_fields_list:
            if get(fields, 'full', '') == item_name:
                return True
        return False

    def get_form_local_set(self):
        available_locales = self.form.get_settings(path="schema_config.configs.locale.available_locales", default=[])

        language = DEFAULT_LANGUAGE
        if isinstance(available_locales, list) and len(available_locales) > 0 and  language not in available_locales:
            language = available_locales[0]

        return self.form.locale_set.filter(language=language).all()

    def set_form_item_labels(self):
        '''
        english first
        return {
            "question_name" : "label",
        }
        '''
        self.item_labels = {}
        locale_set = self.get_form_local_set()
        for label in locale_set:
            display_label = re.search(r"(\w)*\.display.label$", label.key)
            if not display_label:
                continue
            display_label_key = display_label.group().replace(".display.label", "")
            self.item_labels.update({
                display_label_key: label.text
            })

    def get_item_label(self, item_name, item_data):
        item_label = self.item_labels.get(item_name, None)
        if item_label:
            return item_label
        
        return item_data.get('display', {}).get('label') or item_name
