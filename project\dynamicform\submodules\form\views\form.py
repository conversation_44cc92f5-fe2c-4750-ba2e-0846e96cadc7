from django.core.cache import cache
from django.db.models import F, Value, <PERSON><PERSON><PERSON>Field
from django.shortcuts import get_object_or_404
from rest_framework import viewsets, status, filters, serializers
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from dynamicform.models import (
    Form,
    FormUsers,
    FormSetting,
    CustomStatusKey,
    CustomStatusValue,
)
from dynamicform.submodules.appliedform.serializer import (
    ApplicationFormNewSerializer,
    ApplicationFormApplyNewSerializer,
)
from dynamicform.submodules.form.serializer_form_ekyc import (
    EKYCReferenceSerializer
)
from dynamicform.submodules.form.pagination import FormPagination
from dynamicform.submodules.form.serializer import (
    FormSerializer,
    FormCloneSerializer,
    FormListSerializer,
    FormUserSerializer,
)
from workspace.helpers.permissions import (
    Is<PERSON>uperUser,
    HasViewSubmissionDashboardPermission,
    HasCreateEditDuplicateFlowPermission,
    HasCreateEditDecisionFlowsPermission,
    HasDeleteFlowPermission,
    HasAddNewApplicationPermission,
    HasChangeFlowIdPermission,
    HasViewSubmissionDetailPermission,
)
from dynamicform.submodules.form.pagination import FormPagination
from dynamicform.submodules.form.serializer import (
    CustomStatusKeySerializer,
    CustomStatusValueSerializer,
    FormListSerializer,
    FormSerializer,
    FormUserSerializer,
    FormCommitSerializer,
)
from pydash import set_, get
from rest_framework import filters, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from ..external_api_statistic_dashboard import statistics_dashboard_api
from django.conf import settings

GITLAB_LINKED_ENV = settings.GITLAB_LINKED_ENV
********************* = settings.*********************

ACTION_VIEW_SUBMISSION_DASHBOARD = [
    'list',
    'retrieve',
    'statistic',
    'mysettings',
]
ACTION_VIEW_SUBMISSION_DETAIL = [
    'create_or_batch_update_or_list_custom_status_key',
]
ACTION_CREATE_EDIT_DUPLICATE_FLOW = [
    'create',
    'update',
    'clone',
    'partial_update',
    'update_with_commit',
    'update_component_item',
]
ACTION_CREATE_EDIT_DECISION_FLOWS = [
    'create_or_batch_update_or_list_custom_status_key',
    'create_or_batch_update_custom_status_value',
    'update_or_delete_custom_status_key',
    'update_or_delete_custom_status_value',
]
ACTION_ADD_NEW_APPLICATION = [
    'create_applied_form',
    'new',
]
ACTION_VIEW_SUBMISSION_DASHBOARD_VIEW_FORM_SETTINGS = [
    'form_settings',
]
ACTION_VIEW_SUBMISSION_DASHBOARD_UPDATE_FORM_SETTINGS = [
    'form_settings',
    'save_dashboard_settings',
]
ACTION_DELETE_FLOW = [
    'destroy'
]
ACTION_CHANGE_FLOW_ID = [
    'create'
]

FORM_CACHE_TIMEOUT = 30
FORM_CACHE_KEY_FORMAT = "form_%s_user_%s"


class FormViewSet(viewsets.ModelViewSet):
    lookup_field = "slug"
    queryset = Form.objects.all()
    serializer_class = FormSerializer
    pagination_class = FormPagination
    filter_backends = [filters.OrderingFilter, filters.SearchFilter]
    search_fields = ["name", "slug"]
    ordering_fields = "__all__"
    ordering = ["created_at"]
    ordering_param = "ordering"

    def get_queryset(self):
        user = self.request.user
        if not user:
            return Form.objects.none()
        elif user.is_superuser:
            return self.queryset
        elif user.is_api_token:
            return self.queryset.filter(token__user=user)
        else:
            query_set = self.queryset.filter(form_of__workspace__member__user=user)
            if not query_set:
                return self.queryset.filter(users=user)

            return query_set

    def get_permissions(self):
        if self.request.method in ['GET'] and self.action in ACTION_VIEW_SUBMISSION_DASHBOARD_VIEW_FORM_SETTINGS:
            permission_classes = [IsAuthenticated, HasViewSubmissionDashboardPermission]
        elif self.action in ACTION_VIEW_SUBMISSION_DASHBOARD_UPDATE_FORM_SETTINGS:
            permission_classes = [IsAuthenticated, HasCreateEditDuplicateFlowPermission]
        elif self.action in ACTION_VIEW_SUBMISSION_DASHBOARD:
            permission_classes = [IsAuthenticated, HasViewSubmissionDashboardPermission]
        elif self.request.method in ['GET'] and self.action in ACTION_VIEW_SUBMISSION_DETAIL:
            permission_classes = [IsAuthenticated, HasViewSubmissionDetailPermission]
        elif self.action in ACTION_CREATE_EDIT_DUPLICATE_FLOW:
            permission_classes = [IsAuthenticated, HasCreateEditDuplicateFlowPermission]
            if self.action in ACTION_CHANGE_FLOW_ID:
                data_slug = self.request.data.get("slug")
                if data_slug:
                    # works only action = create (create a form with slug != name in the body)
                    permission_classes = [IsAuthenticated, HasChangeFlowIdPermission]
        elif self.action in ACTION_CREATE_EDIT_DECISION_FLOWS:
            permission_classes = [IsAuthenticated, HasCreateEditDecisionFlowsPermission]
        elif self.action in ACTION_DELETE_FLOW:
            permission_classes = [IsAuthenticated, HasDeleteFlowPermission]
        elif self.action in ACTION_ADD_NEW_APPLICATION:
            permission_classes = [IsAuthenticated, HasAddNewApplicationPermission]
        else:
            permission_classes = [IsAuthenticated, IsSuperUser]
        return [permission() for permission in permission_classes]

    def filter_queryset(self, queryset):
        MAX_SEARCH_LENGTH = 280
        query = self.request.GET

        search = query.get("search", None)
        if type(search) == str and len(search) > MAX_SEARCH_LENGTH:
            raise serializers.ValidationError(
                f"The maximum allowed length for search text is {MAX_SEARCH_LENGTH} characters."
            )

        queryset = super(FormViewSet, self).filter_queryset(queryset)

        is_active = query.get("is_active", None)
        if is_active:
            try:
                is_active = bool(int(is_active))
                queryset = queryset.filter(is_active=is_active)
            except:
                pass

        ordering = query.get("ordering", None)
        highlight = query.get("highlight", None)
        my_highlight = None
        my_highlight_id = []
        all_forms_exclude_my_highlight = None

        if highlight:
            try:
                highlight = bool(int(highlight))
                if self.request.user.is_superuser and (not highlight):
                    my_highlight = queryset.filter(
                        formusers__user=self.request.user, formusers__highlight=False
                    )
                    my_unhighlight = queryset.filter(
                        formusers__user=self.request.user, formusers__highlight=True
                    )
                    my_highlight_id = my_highlight.values_list("id", flat=True)
                    my_unhighlight_id = my_unhighlight.values_list("id", flat=True)
                    my_formsusers_id = list(my_highlight_id) + list(my_unhighlight_id)
                    all_forms_exclude_my_highlight = queryset.exclude(
                        id__in=my_formsusers_id
                    )
                    all_forms_exclude_my_highlight_id = (
                        all_forms_exclude_my_highlight.values_list("id", flat=True)
                    )
                    my_highlight_query_id = list(my_highlight_id) + list(
                        all_forms_exclude_my_highlight_id
                    )
                    queryset = queryset.filter(id__in=my_highlight_query_id)
                else:
                    queryset = queryset.filter(
                        formusers__user=self.request.user,
                        formusers__highlight=highlight,
                    )
                    if ordering and "formusers__highlight" in ordering:
                        my_highlight = queryset
            except:
                pass

        if ordering and "formusers__highlight" in ordering:
            try:
                if self.request.user.is_superuser:
                    if not my_highlight:
                        my_highlight = queryset.filter(
                            formusers__user=self.request.user, formusers__highlight=True
                        )

                    if not all_forms_exclude_my_highlight:
                        my_highlight_id = my_highlight.values_list("id", flat=True)
                        all_forms_exclude_my_highlight = queryset.exclude(
                            id__in=list(my_highlight_id)
                        )

                    my_highlight = my_highlight.annotate(
                        formusers__highlight=F("formusers__highlight")
                    )
                    all_forms_exclude_my_highlight = (
                        all_forms_exclude_my_highlight.annotate(
                            formusers__highlight=Value(
                                False, output_field=BooleanField()
                            )
                        )
                    )

                    queryset = my_highlight.union(all_forms_exclude_my_highlight)
                    ordering = ordering.split(",")
                    queryset = queryset.order_by(*ordering)
                else:
                    ordering = ordering.split(",")
                    queryset = queryset.order_by(*ordering)
            except:
                pass

        return queryset

    def get_object(self):
        user = self.request.user
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        lookup_value = self.kwargs[lookup_url_kwarg]
        cache_key = FORM_CACHE_KEY_FORMAT % (lookup_value, user.id)
        cache_value = cache.get(cache_key)
        if cache_value == 1:
            if user:
                obj = self.get_queryset().filter(slug=lookup_value).first()
            else:
                obj = Form.objects.filter(slug=lookup_value).first()
            if not obj:
                raise NotFound()
            return obj
        elif cache_value == 0:
            raise NotFound()

        queryset = self.filter_queryset(self.get_queryset())

        assert lookup_url_kwarg in self.kwargs, (
            "Expected view %s to be called with a URL keyword argument "
            'named "%s". Fix your URL conf, or set the `.lookup_field` '
            "attribute on the view correctly."
            % (self.__class__.__name__, lookup_url_kwarg)
        )
        filter_kwargs = {self.lookup_field: lookup_value}
        obj = queryset.filter(**filter_kwargs).first()
        if not obj:
            raise NotFound()

        if obj:
            cache.set(cache_key, 1, 30)
        else:
            cache.set(cache_key, 0, 30)

        # May raise a permission denied
        self.check_object_permissions(self.request, obj)

        return obj

    def list(self, request, slug=None):
        self.serializer_class = FormListSerializer
        return super(FormViewSet, self).list(request, slug)

    @action(detail=False, methods=["POST"], url_path="new", url_name="apply-new")
    def new(self, request, *args, **kwargs):
        # To support Old version
        data = request.data
        form = Form.objects.filter(
            users=self.request.user, slug=data.get("form", None)
        ).first()
        serializer = ApplicationFormNewSerializer(data=data, context={"form": form})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    @action(
        detail=True, methods=["PATCH"], url_path="commit-save", url_name="commit-save"
    )
    def update_with_commit(self, request, *args, **kwargs):
        data = request.data
        form = self.get_object()

        is_form_change = True if data.get("frontend_schema") else False
        is_report_change = True if data.get("report_schema") else False

        # set frontend_schema in case of update only report schema
        if not data.get("frontend_schema"):
            data["frontend_schema"] = form.frontend_schema

        # use serializer to validate payload
        serializer = FormCommitSerializer(
            instance=form, data=data, context={"form": form}
        )
        serializer.is_valid(raise_exception=True)

        # Prepare data for commit
        form_locale = data.get("locale", "en")
        commit_message = data.get("commit_message")
        form_slug = serializer.data.get("slug")

        frontend_schema = (
            serializer.validated_data.get("frontend_schema") if is_form_change else None
        )
        report_schema = (
            serializer.validated_data.get("report_schema") if is_report_change else None
        )

        response_new_commit = Form.commit_save(
            form_slug=form_slug,
            form_locale=form_locale,
            frontend_schema=frontend_schema,
            report_schema=report_schema,
            commit_message=commit_message,
        )

        # update to our env
        version_sha = (
            response_new_commit.get("short_id") if response_new_commit else None
        )
        branch_name = (
            response_new_commit.get("branch_name") if response_new_commit else None
        )
        serializer.update_with_commit(form, version_sha, branch_name)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_200_OK, headers=headers)

    @action(
        detail=True, methods=["POST"], url_path="create", url_name="create_applied_form"
    )
    def create_applied_form(self, request, form_slug=None, *args, **kwargs):
        # api for client for creating an application
        # https://docs.uppass.io/#/paths/~1%7Blang%7D~1api~1forms~1%7Bform_slug%7D~1create~1/post
        # https://app.uppass.io/{lang}/api/forms/{form_slug}/create/
        # need to authen for HasAddNewApplicationPermission (ACTION_ADD_NEW_APPLICATION)
        data = request.data
        form = self.get_object()
        serializer = ApplicationFormApplyNewSerializer(
            data=data, context={"form": form}
        )
        serializer.is_valid(raise_exception=True)

        data_ekyc_reference = data.get("ekyc_reference", {})
        ekyc_refer_serializer = None
        if data_ekyc_reference:
            ekyc_refer_serializer = EKYCReferenceSerializer(
                data=data_ekyc_reference, context={"form": form}
            )
            ekyc_refer_serializer.is_valid(raise_exception=True)

        applied_form = serializer.save()
        if ekyc_refer_serializer:
            ekyc_refer_serializer.clone_ekyc_from_slug_ref(
                applied_form=applied_form
            )

        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    @action(detail=True, methods=["POST"], url_path="clone")
    def clone(self, request, slug=None, workspace=None, *args, **kwargs):
        data = request.data
        instance = self.get_object()

        serializer = FormCloneSerializer(data=data)
        serializer.is_valid(raise_exception=True)

        clone_instance = instance.clone(**request.data, workspace=workspace)
        clone_serializer = FormCloneSerializer(instance=clone_instance)

        return Response(clone_serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=["POST"])
    def mysettings(self, request, slug=None, *args, **kwargs):
        form = self.get_object()
        user = self.request.user
        data = request.data
        serializer = FormUserSerializer(data=data)
        serializer.is_valid(raise_exception=True)

        form_user = form.formusers_set.filter(user=user).first()
        if not form_user:
            form_user = FormUsers(form=form, user=user)

        for attr, value in serializer.validated_data.items():
            if hasattr(form_user, attr):
                setattr(form_user, attr, value)
        form_user.save()

        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["POST"], url_path="dashboard-settings")
    def save_dashboard_settings(self, request, slug=None, *args, **kwargs):
        form = Form.objects.get(slug=slug)
        path = "dashboard"
        settings_payload = {}

        # Get default locale
        schema_config_settings = form.get_settings(path='schema_config')

        trans = form.get_trans('en')
        if not trans:
            default_language = get(schema_config_settings, ['configs', 'locale', 'default_locale'])
            trans = form.get_trans(default_language)

        # Get label mapping from backend schema
        form.refresh_from_db()
        form.refresh_from_db(fields=["_backend_schema"])
        label_key_map = get(form.backend_schema, 'labels')

        # Try to get exists form setting
        try:
            form_settings = form.formsetting
            settings_payload = form.get_settings(path=path, secure=True)
        except:
            form_settings = FormSetting(settings=settings_payload, form=form)

        # Define new form setting payload
        dadshboard_settings = request.data

        # Translate labels to default value
        dashboard_columns = dadshboard_settings.get('columns', [])
        dashboard_columns_hidden = dadshboard_settings.get('columns_hidden', [])

        # Translate columns
        for i, col in enumerate(dashboard_columns):
            for j, cell in enumerate(col.get('cells')):
                if cell.get('type') == 'answer':
                    tran_key = label_key_map.get(cell['answer_key'])
                    new_label = trans.get(tran_key)
                    if cell.get('label') is None or cell.get('label') == '':
                        set_(dadshboard_settings, ['columns', i, 'cells' ,j ,'label'], new_label)
        # Translate columns hidden
        for i, col in enumerate(dashboard_columns_hidden):
            for j, cell in enumerate(col.get('cells')):
                if cell.get('type') == 'answer':
                    tran_key = label_key_map.get(cell['answer_key'])
                    new_label = trans.get(tran_key)
                    if cell.get('label') is None or cell.get('label') == '':
                        set_(dadshboard_settings, ['columns_hidden', i, 'cells' ,j ,'label'], new_label)
    
        # Set new form setting payload
        set_(form_settings.settings, path, dadshboard_settings)
        # form_settings.encrypt_settings()
        form_settings.save()

        return_update_form_settings = form_settings.get_settings(path=path, secure=True)
        return Response(return_update_form_settings, status=status.HTTP_200_OK)

    @action(detail=True, methods=["POST", "GET"], url_path="settings")
    def form_settings(self, request, slug=None, *args, **kwargs):
        form = self.get_object()
        path = request.GET.get("path", None)
        settings_payload = {}
        try:
            form_settings = form.formsetting
            settings_payload = form.get_settings(path=path, secure=True)
            # ! MARK: get setting from form_setting.get_setting below will not check the branding condition
            # settings_payload = form_settings.get_settings(path=path, secure=True)
        except:
            form_settings = FormSetting(settings=settings_payload, form=form)

        if request.method == "GET":
            return Response(settings_payload, status=status.HTTP_200_OK)

        update_form_settings = FormSetting(settings=request.data, form=form)
        update_form_settings.encrypt_settings()
        if path:
            set_(form_settings.settings, path, update_form_settings.settings)
        else:
            form_settings.settings.update(update_form_settings.settings)
        form_settings.save()

        return_update_form_settings = update_form_settings.get_settings(secure=True)
        return Response(return_update_form_settings, status=status.HTTP_200_OK)

    @action(detail=True, methods=["GET"], url_path="statistic")
    def statistic(self, request, slug=None, workspace=None, *args, **kwargs):
        query = self.request.GET
        interval_time = query.get("interval_time", "3_months")
        form = self.get_object()
        result, api_status = statistics_dashboard_api(
            form.slug,
            interval_time,
        )
        return Response(result, status=api_status)

    @action(
        detail=True,
        methods=[
            "GET",
            "POST",
            "PATCH"
        ],
        url_path="custom-status-key",
    )
    def create_or_batch_update_or_list_custom_status_key(
        self,
        request,
        slug=None,
        workspace=None,
        *args,
        **kwargs,
    ):
        form = self.get_object()
        method = request.method
        lookup_table = {
            "GET": {
                "action": self._list_custom_status_key,
                "status_code": 200,
            },
            "POST": {
                "action": self._create_custom_status_key,
                "status_code": 201,
            },
            "PATCH": {
                "action": self._batch_update_custom_status_key,
                "status_code": 200,
            },
        }
        action = lookup_table[method]["action"]
        status_code = lookup_table[method]["status_code"]
        result = action(request, form)

        return Response(result, status=status_code)

    @action(
        detail=True,
        methods=[
            "PATCH",
            "DELETE",
        ],
        url_path="custom-status-key/(?P<custom_status_key_pk>[^/.]+)",
    )
    def update_or_delete_custom_status_key(
        self,
        request,
        slug=None,
        workspace=None,
        custom_status_key_pk=None,
        *args,
        **kwargs,
    ):
        form = self.get_object()
        custom_status_key = get_object_or_404(
            CustomStatusKey,
            pk=custom_status_key_pk,
            form=form,
        )
        method = request.method
        lookup_table = {
            "PATCH": {
                "action": self._partial_update_custom_status_key,
                "status_code": 200,
            },
            "DELETE": {
                "action": self._delete_custom_status_key,
                "status_code": 204,
            },
        }
        action = lookup_table[method]["action"]
        status_code = lookup_table[method]["status_code"]
        result = action(request, form, custom_status_key)

        return Response(result, status=status_code)

    def _create_custom_status_key(self, request, form):
        request_data = request.data.copy()
        options = request_data.pop("options", None)
        data = {
            "form": form.pk,
            **request_data,
        }
        custom_status_key_serializer = CustomStatusKeySerializer(
            data=data,
        )
        custom_status_key_serializer.is_valid(raise_exception=True)
        custom_status_key = custom_status_key_serializer.save()
        result = custom_status_key_serializer.data

        # Batch create custom_status_value if options are provided
        if options and isinstance(options, list):
            custom_status_value_serializer = CustomStatusValueSerializer(
                data=[
                    {"custom_status_key": custom_status_key.pk, **option}
                    for option in options
                ],
                many=True,
            )
            custom_status_value_serializer.is_valid(raise_exception=True)
            custom_status_value_serializer.save()
            # Optionally, you can include the created values in the response
            result["options"] = custom_status_value_serializer.data

        return result
    
    def _batch_update_custom_status_key(self, request, form):
        data = request.data
        custom_status_keys = data.get("custom_status_keys", [])
        if not custom_status_keys:
            raise serializers.ValidationError("No custom status keys provided for update.")

        updated_custom_status_keys = []
        for custom_status_key_data in custom_status_keys:
            custom_status_key_id = custom_status_key_data.get("id")
            if not custom_status_key_id:
                raise serializers.ValidationError("Custom status key ID is required for update.")
            custom_status_key = get_object_or_404(
                CustomStatusKey, pk=custom_status_key_id, form=form
            )
            custom_status_key_serializer = CustomStatusKeySerializer(
                custom_status_key,
                data=custom_status_key_data,
                partial=True,
            )
            custom_status_key_serializer.is_valid(raise_exception=True)
            custom_status_key_serializer.save()
            updated_custom_status_keys.append(custom_status_key_serializer.data)

        return updated_custom_status_keys

    def _list_custom_status_key(self, request, form):
        custom_status_key_queryset = CustomStatusKey.objects.filter(form=form)
        custom_status_key_serializer = CustomStatusKeySerializer(
            custom_status_key_queryset,
            many=True,
        )
        result = custom_status_key_serializer.data

        return result

    def _partial_update_custom_status_key(self, request, form, custom_status_key):
        data = request.data
        custom_status_key_serializer = CustomStatusKeySerializer(
            custom_status_key,
            data=data,
            partial=True,
        )
        custom_status_key_serializer.is_valid(raise_exception=True)
        custom_status_key_serializer.save()
        result = custom_status_key_serializer.data

        return result

    def _delete_custom_status_key(self, request, form, custom_status_key):
        current_decision_flow = form.currentdecisionflow_set.select_related("decision_flow").all()
        custom_status_key_serializer = CustomStatusKeySerializer(
            custom_status_key,
            context={
                "current_decision_flow_queryset": current_decision_flow,
            },
        )
        custom_status_key_serializer._is_deletable()
        custom_status_key.delete()

        return

    @action(
        detail=True,
        methods=["POST", "PATCH"],
        url_path="custom-status-key/(?P<custom_status_key_pk>[^/.]+)/custom-status-value",
    )
    def create_or_batch_update_custom_status_value(
        self,
        request,
        slug=None,
        workspace=None,
        custom_status_key_pk=None,
        *args,
        **kwargs,
    ):
        form = self.get_object()
        custom_status_key = get_object_or_404(
            CustomStatusKey,
            pk=custom_status_key_pk,
            form=form,
        )
        method = request.method
        lookup_table = {
            "POST": {
                "action": self._create_custom_status_value,
                "status_code": 201,
            },
            "PATCH": {
                "action": self._batch_update_custom_status_value,
                "status_code": 200,
            },
        }
        action = lookup_table[method]["action"]
        status_code = lookup_table[method]["status_code"]
        result = action(request, form, custom_status_key)

        return Response(result, status=status_code)
    
        
    def _batch_update_custom_status_value(self, request, form, custom_status_key):
        data = request.data
        custom_status_values = data.get("custom_status_values", [])
        if not custom_status_values:
            raise serializers.ValidationError("No custom status values provided for update.")

        updated_custom_status_values = []
        for custom_status_value_data in custom_status_values:
            custom_status_value_id = custom_status_value_data.get("id")
            if not custom_status_value_id:
                raise serializers.ValidationError("Custom status value ID is required for update.")
            custom_status_value = get_object_or_404(
                CustomStatusValue,
                pk=custom_status_value_id,
                custom_status_key=custom_status_key,
            )
            custom_status_value_serializer = CustomStatusValueSerializer(
                custom_status_value,
                data=custom_status_value_data,
                partial=True,
            )
            custom_status_value_serializer.is_valid(raise_exception=True)
            custom_status_value_serializer.save()
            updated_custom_status_values.append(custom_status_value_serializer.data)

        return updated_custom_status_values


    @action(
        detail=True,
        methods=[
            "PATCH",
            "DELETE",
        ],
        url_path="custom-status-key/(?P<custom_status_key_pk>[^/.]+)/custom-status-value/(?P<custom_status_value_pk>[^/.]+)",
    )
    def update_or_delete_custom_status_value(
        self,
        request,
        slug=None,
        workspace=None,
        custom_status_key_pk=None,
        custom_status_value_pk=None,
        *args,
        **kwargs,
    ):
        form = self.get_object()
        custom_status_key = get_object_or_404(
            CustomStatusKey,
            pk=custom_status_key_pk,
            form=form,
        )
        custom_status_value = get_object_or_404(
            CustomStatusValue,
            pk=custom_status_value_pk,
            custom_status_key=custom_status_key,
        )
        method = request.method
        lookup_table = {
            "PATCH": {
                "action": self._partial_update_custom_status_value,
                "status_code": 200,
            },
            "DELETE": {
                "action": self._delete_custom_status_value,
                "status_code": 204,
            },
        }
        action = lookup_table[method]["action"]
        status_code = lookup_table[method]["status_code"]
        result = action(request, form, custom_status_key, custom_status_value)

        return Response(result, status=status_code)

    def _create_custom_status_value(self, request, form, custom_status_key):
        request_data = request.data
        data = {
            "custom_status_key": custom_status_key.pk,
            **request_data,
        }
        custom_status_value_serializer = CustomStatusValueSerializer(data=data)
        custom_status_value_serializer.is_valid(raise_exception=True)
        custom_status_value_serializer.save()
        result = custom_status_value_serializer.data

        return result

    def _partial_update_custom_status_value(
        self,
        request,
        form,
        custom_status_key,
        custom_status_value,
    ):
        data = request.data
        custom_status_value_serializer = CustomStatusValueSerializer(
            custom_status_value,
            data=data,
            partial=True,
        )
        custom_status_value_serializer.is_valid(raise_exception=True)
        custom_status_value_serializer.save()
        result = custom_status_value_serializer.data

        return result

    def _delete_custom_status_value(
        self,
        request,
        form,
        custom_status_key,
        custom_status_value,
    ):
        current_decision_flow = form.currentdecisionflow_set.select_related("decision_flow").all()
        custom_status_value_serializer = CustomStatusValueSerializer(
            custom_status_value,
            context={
                "current_decision_flow_queryset": current_decision_flow,
            },
        )
        custom_status_value_serializer._is_deletable()
        custom_status_value.delete()

        return

    @action(
        detail=True,
        methods=["POST"],
        url_path="items/(?P<item_name>[^/.]+)",
    )
    def update_component_item(self,
            request,
            slug=None,
            workspace=None,
            item_name=None,
            *args,
            **kwargs
        ):
        from dynamicform.schema.getapp import GetApp

        form = self.get_object()
        item = get(form.backend_schema, f"items.{item_name}", None)

        if not item:
            return Response(status=status.HTTP_404_NOT_FOUND)
        
        item_type = get(item, "type")
        item_component = GetApp.get_item_app_from_type(item_type)

        if not hasattr(item_component, 'item_serializer'):
            return Response({}, status=status.HTTP_200_OK)

        item_serializer = getattr(item_component, 'item_serializer')
        serializer = item_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.update_item_schema(form, item)

        return Response(serializer.data, status=status.HTTP_200_OK)