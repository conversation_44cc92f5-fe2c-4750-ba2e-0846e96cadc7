from datetime import datetime
from pydash.objects import get
from rest_framework import status
from bankstatement.submodules.document.external_api_v2 import (
    list_transaction,
)

class BankstatementSummaryDownloadMixin:
    index_transaction_id_sheet_name = {}

    def workbook_write_summary_tab_data(self, workbook):
        summary, _ = self.get_bank_statement_summary()

        worksheet = workbook.add_worksheet(name="Bank Statement Summary")

        cell_header_format = workbook.add_format({
            "bold": True,
            "font_color": "white",
            "bg_color":"#4364e8"
        })
        cell_sechtion_format = workbook.add_format({
            "bold": True,
            "font_color": "white",
            "bg_color":"#262626"
        })
        cell_sechtion_gray_format = workbook.add_format({
            "bold": True,
            "font_color": "black",
            "bg_color":"#d8d8d8"
        })
        cell_sechtion_dark_gray_format = workbook.add_format({
            "bold": True,
            "font_color": "white",
            "bg_color":"#595959"
        })

        current_row = [0]
        def add_next_row(n=1):
            # n=1 == just new line
            _current_row = current_row[0]
            current_row[0] = _current_row + n

        def white_a_row(label, value):
            _current_row = current_row[0]
            worksheet.write(_current_row, 0, label)
            worksheet.write(_current_row, 1, value)
            add_next_row()

        worksheet.write(current_row[0], 0, "BANK STATEMENT SUMMARY")
        add_next_row(n=2)

        # Overall ###########
        worksheet.write(current_row[0], 0, "Overall")
        worksheet.set_row(current_row[0], None, cell_sechtion_format)
        add_next_row()
        
        white_a_row("Number of Bank Accounts", get(summary, "statement_summary.number_of_bank_accounts"))
        white_a_row("Financial Persona", get(summary, "statement_summary.persona"))
        white_a_row("Start Date", get(summary, "statement_summary.overall.start_date"))
        white_a_row("End Date", get(summary, "statement_summary.overall.end_date"))
        white_a_row("Number of Transactions", get(summary, "statement_summary.overall.number_of_transactions"))
        white_a_row("Number of Deposits", get(summary, "statement_summary.overall.number_of_deposits"))
        white_a_row("Number of Withdrawals", get(summary, "statement_summary.overall.number_of_withdrawal"))
        white_a_row("Total Deposits", get(summary, "statement_summary.overall.total_amount_of_deposits"))
        white_a_row("Total Withdrawals", get(summary, "statement_summary.overall.total_amount_of_withdrawals"))
        white_a_row("Initial Balance", get(summary, "statement_summary.overall.initial_balance"))
        white_a_row("Final Balance", get(summary, "statement_summary.overall.final_balance"))
        white_a_row("Average Daily Account Balance", get(summary, "statement_summary.overall.avg_balance_per_day"))
        white_a_row("Days with Deposits", get(summary, "statement_summary.overall.number_of_days_with_deposit"))
        white_a_row("Days with Withdrawals", get(summary, "statement_summary.overall.number_of_days_with_withdrawal"))

        add_next_row(n=2)

        # Monthly Summary ###########
        worksheet.write(current_row[0], 0, "Monthly Summary")
        worksheet.set_row(current_row[0], None, cell_sechtion_format)
        add_next_row()

        statement_summary_monthly = get(summary, "statement_summary.monthly", [])
        for monthly in statement_summary_monthly:
            monthly_header = ""
            start_date_format = ""
            end_date_format = ""

            # "start_date": "2024-07-01",
            # "end_date": "2024-07-31",
            start_date = get(monthly, "start_date")
            end_date = get(monthly, "end_date")
            try:
                start_date = datetime.strptime(str(start_date), "%Y-%m-%d")
                start_date_format = start_date.strftime("%Y-%m-%d")
                monthly_header = start_date.strftime("%B %Y")
            except ValueError:
                pass

            try:
                end_date = datetime.strptime(str(end_date), "%Y-%m-%d")
                end_date_format = end_date.strftime("%Y-%m-%d")
            except ValueError:
                pass

            worksheet.write(current_row[0], 0, monthly_header)
            worksheet.set_row(current_row[0], None, cell_sechtion_gray_format)
            add_next_row()

            white_a_row("Start Date", start_date_format) #statement_summary.monthly[0].start_date
            white_a_row("End Date", end_date_format) #statement_summary.monthly[0].end_date

            white_a_row("Number of Transactions", get(monthly, "number_of_transactions")) #statement_summary.monthly[0].number_of_transactions
            white_a_row("Total Deposits", get(monthly, "total_amount_of_deposits")) #statement_summary.monthly[0].total_amount_of_deposits
            white_a_row("Average Deposit Amount", get(monthly, "average_deposit_amount")) #statement_summary.monthly[0].average_deposit_amount
            white_a_row("Max Deposits", get(monthly, "max_deposit")) #statement_summary.monthly[0].max_deposit
            white_a_row("Min Deposits", get(monthly, "min_deposit")) #statement_summary.monthly[0].min_deposit
            white_a_row("Total Withdrawals", get(monthly, "total_amount_of_withdrawals")) #statement_summary.monthly[0].total_amount_of_withdrawals
            white_a_row("Average Withdrawal Amount", get(monthly, "average_withdrawal_amount")) #statement_summary.monthly[0].average_withdrawal_amount
            white_a_row("Max Withdrawals", get(monthly, "max_withdrawal")) #statement_summary.monthly[0].max_withdrawal
            white_a_row("Min Withdrawals", get(monthly, "min_withdrawal")) #statement_summary.monthly[0].min_withdrawal
            white_a_row("Final Balance", get(monthly, "final_balance")) #statement_summary.monthly[0].final_balance
            white_a_row("Average Daily Account Balance", get(monthly, "avg_balance_per_day")) #statement_summary.monthly[0].avg_balance_per_day
            white_a_row("Number of Unique Transaction Channels", get(monthly, "number_of_unique_channel")) #statement_summary.monthly[0].number_of_unique_channel
            white_a_row("Number of Unique Deposit Channels", get(monthly, "number_of_unique_deposit_channel")) #statement_summary.monthly[0].number_of_unique_deposit_channel
            white_a_row("Number of Unique Withdrawal Channels", get(monthly, "number_of_unique_withdrawal_channel")) #statement_summary.monthly[0].number_of_unique_withdrawal_channel

            add_next_row()

        # Top Transactions
        def white_a_transaction_header():
            _current_row = current_row[0]
            worksheet.write(_current_row, 0, "Sheet Name")
            worksheet.write(_current_row, 1, "Transaction ID")
            worksheet.write(_current_row, 2, "Date")
            worksheet.write(_current_row, 3, "Amount")
            worksheet.set_row(current_row[0], None, cell_sechtion_gray_format)
            add_next_row()

        def white_a_transaction(transactions):
            for transaction in transactions:
                _current_row = current_row[0]

                date = get(transaction, "date")
                try:
                    date = datetime.strptime(str(date), "%Y-%m-%d")
                    date_format = date.strftime("%Y-%m-%d")
                except ValueError:
                    date_format = date

                transaction_id = get(transaction, "transaction_id")
                transaction_sheet_name = str(self.index_transaction_id_sheet_name.get(transaction_id, ""))

                worksheet.write(_current_row, 0, transaction_sheet_name)
                worksheet.write(_current_row, 1, transaction_id)
                worksheet.write(_current_row, 2, date_format)
                worksheet.write(_current_row, 3, get(transaction, "amount"))
                add_next_row()

            add_next_row()

        worksheet.write(current_row[0], 0, "Top Transactions")
        worksheet.set_row(current_row[0], None, cell_sechtion_format)
        add_next_row()
        # # Top 3 Maximum Deposits
        worksheet.write(current_row[0], 0, "Top 3 Maximum Deposits")
        worksheet.set_row(current_row[0], None, cell_sechtion_dark_gray_format)
        add_next_row()
        white_a_transaction_header()
        white_a_transaction(get(summary, "statement_summary.overall.top_3_max_deposits_transaction", []))

        # # Top 3 Minimum Deposits
        worksheet.write(current_row[0], 0, "Top 3 Minimum Deposits")
        worksheet.set_row(current_row[0], None, cell_sechtion_dark_gray_format)
        add_next_row()
        white_a_transaction_header()
        white_a_transaction(get(summary, "statement_summary.overall.top_3_min_deposits_transaction", []))

        # # Top 3 Maximum Withdrawals
        worksheet.write(current_row[0], 0, "Top 3 Maximum Withdrawals")
        worksheet.set_row(current_row[0], None, cell_sechtion_dark_gray_format)
        add_next_row()
        white_a_transaction_header()
        white_a_transaction(get(summary, "statement_summary.overall.top_3_max_withdrawal_transaction", []))

        # # Top 3 Minimum Withdrawals
        worksheet.write(current_row[0], 0, "Top 3 Minimum Withdrawals")
        worksheet.set_row(current_row[0], None, cell_sechtion_dark_gray_format)
        add_next_row()
        white_a_transaction_header()
        white_a_transaction(get(summary, "statement_summary.overall.top_3_min_withdrawal_transaction", []))

        # Advaned Insights
        worksheet.write(current_row[0], 0, "Advaned Insights")
        worksheet.set_row(current_row[0], None, cell_sechtion_format)
        add_next_row()

        white_a_row("Deposit Transactions (last 1 month)", get(summary, "statement_summary.overall.debt_to_income_last_1_month")) # statement_summary.overall.debt_to_income_last_1_month
        white_a_row("Deposit Transactions (last 3 months)", get(summary, "statement_summary.overall.debt_to_income_last_3_month")) # statement_summary.overall.debt_to_income_last_3_month
        white_a_row("Deposit Transactions (last 6 months)", get(summary, "statement_summary.overall.debt_to_income_last_6_month")) # statement_summary.overall.debt_to_income_last_6_month
        white_a_row("Average Income (last 6 Months)", get(summary, "statement_summary.overall.avg_income_last_6_month")) # statement_summary.overall.avg_income_last_6_month
        white_a_row("Net Income (last 1 month)", get(summary, "statement_summary.overall.net_income_in_1_month")) # statement_summary.overall.net_income_in_1_month
        white_a_row("Average Net Income (3 months)", get(summary, "statement_summary.overall.avg_net_income_in_3_month")) # statement_summary.overall.avg_net_income_in_3_month
        white_a_row("Average Net Income (6 months)", get(summary, "statement_summary.overall.avg_net_income_in_6_month")) # statement_summary.overall.avg_net_income_in_6_month
        white_a_row("Number of Loan Institutions in Loan Repayment (last 1 month)", get(summary, "statement_summary.overall.loan_institution_count_last_1_month")) # statement_summary.overall.loan_institution_count_last_1_month
        white_a_row("Number of Loan Institutions in Loan Repayment (last 3 month)", get(summary, "statement_summary.overall.loan_institution_count_last_3_month")) # statement_summary.overall.loan_institution_count_last_3_month
        white_a_row("Number of Loan Institutions in Loan Repayment (last 6 month)", get(summary, "statement_summary.overall.loan_institution_count_last_6_month")) # statement_summary.overall.loan_institution_count_last_6_month
        white_a_row("Total Loan Repayments (last 1 month)", get(summary, "statement_summary.overall.sum_loan_repayment_last_1_month")) # statement_summary.overall.sum_loan_repayment_last_1_month
        white_a_row("Total Loan Repayments (last 3 Months)", get(summary, "statement_summary.overall.sum_loan_repayment_last_3_month")) # statement_summary.overall.sum_loan_repayment_last_3_month
        white_a_row("Total Loan Repayments (last 6 Months)", get(summary, "statement_summary.overall.sum_loan_repayment_last_6_month")) # statement_summary.overall.sum_loan_repayment_last_6_month
        white_a_row("Debt-to-income ratio (last 1 month)", get(summary, "statement_summary.overall.debt_to_income_last_1_month")) # statement_summary.overall.debt_to_income_last_1_month
        white_a_row("Debt-to-income ratio (last 3 Months)", get(summary, "statement_summary.overall.debt_to_income_last_3_month")) # statement_summary.overall.debt_to_income_last_3_month
        white_a_row("Debt-to-income ratio (last 6 Months)", get(summary, "statement_summary.overall.debt_to_income_last_6_month")) # statement_summary.overall.debt_to_income_last_6_month
        white_a_row("Days with a balance below THB 1,000 (last 1 month)", get(summary, "statement_summary.overall.days_balance_below_thb1000_last_1_month")) # statement_summary.overall.days_balance_below_thb1000_last_1_month
        white_a_row("Days with a balance below THB 1,000 (last 3 months)", get(summary, "statement_summary.overall.days_balance_below_thb1000_last_3_month")) # statement_summary.overall.days_balance_below_thb1000_last_3_month
        white_a_row("Days with a balance below THB 1,000 (last 6 months)", get(summary, "statement_summary.overall.days_balance_below_thb1000_last_6_month")) # statement_summary.overall.days_balance_below_thb1000_last_6_month
        white_a_row("Has negative balance (last 1 month)", get(summary, "statement_summary.overall.has_negative_balance_last_1_month")) # statement_summary.overall.has_negative_balance_last_1_month
        white_a_row("Has negative balance (last 3 month)", get(summary, "statement_summary.overall.has_negative_balance_last_3_month")) # statement_summary.overall.has_negative_balance_last_3_month
        white_a_row("Has negative balance (last 6 month)", get(summary, "statement_summary.overall.has_negative_balance_last_6_month")) # statement_summary.overall.has_negative_balance_last_6_month
        white_a_row("Days of Inactivity (Last 1 Month)", get(summary, "statement_summary.overall.inactive_periods_days_last_1_month")) # statement_summary.overall.inactive_periods_days_last_1_month
        white_a_row("Difference between deposits in the last 30 days and deposits in the 31-60 days.", get(summary, "statement_summary.overall.diff_30d_60d_rolling_deposit")) # statement_summary.overall.diff_30d_60d_rolling_deposit

        # style column
        text_wrap_format = workbook.add_format({
            "text_wrap": True,
        })
        worksheet.set_column(0, 0, width=42, cell_format=text_wrap_format)
        worksheet.set_column(1, 4, width=15, cell_format=text_wrap_format)
        worksheet.set_row(0, None, cell_header_format)

        return
    
    def workbook_write_account_tabs(self, workbook, query={}):
        documents = self.get_show_in_report_documents()
        self.index_transaction_id_sheet_name = {}

        current_row = [0]
        def add_next_row(n=1):
            # n=1 == just new line
            _current_row = current_row[0]
            current_row[0] = _current_row + n
        
        def write_a_row(column_start=0, texts=[]):
            _current_row = current_row[0]
            for index, text in enumerate(texts):
                worksheet.write(_current_row, index+column_start, text)
            add_next_row()
        
        # style
        text_wrap_format = workbook.add_format({
            "text_wrap": True,
        })
        cell_title_format = workbook.add_format({
            "bold": True,
            "font_color": "white",
            "bg_color":"#4364e8"
        })
        cell_sub_title_format = workbook.add_format({
            "bold": True,
            "font_color": "white",
            "bg_color":"#262626"
        })

        existing_sheet_name = {}
        # build sheet name
        for document in documents:
            sheet_name = f"{document.bank_code}_{document.account_number}"
            if sheet_name not in existing_sheet_name:
                existing_sheet_name.update({sheet_name: 1})
            else:
                counter = existing_sheet_name.get(sheet_name, 1)
                existing_sheet_name.update({sheet_name: counter+1})
                counter_char = str(counter+1)
                sheet_name = f"{sheet_name}-{counter_char}"

            document.summary_download_sheet_name = sheet_name

        for document in documents:
            if document.summary_download_sheet_name:
                sheet_name = document.summary_download_sheet_name
            else:
                sheet_name = f"{document.bank_code}_{document.account_number}"
            
            sheet_counter = existing_sheet_name.get(sheet_name)
            if sheet_counter and sheet_counter > 1:
                sheet_name = f"{sheet_name}-1"

            worksheet = workbook.add_worksheet(name=sheet_name)

            # reset row
            current_row = [0]
            #

            # Info
            write_a_row(1, ["INFO"])
            write_a_row(1, [
                "account_name",
                "account_number",
                "branch_name",
                "bank_code",
                "statement_period_from",
                "statement_period_to",
                "transaction_count"
            ])
            
            results, status_code = list_transaction(id=document.bankstatement_lookup_id, query={})
            if not status.is_success(status_code):
                continue

            transactions = get(results, "results", [])

            try:
                statement_period_from = document.statement_period_from.astimezone().isoformat()
            except:
                statement_period_from = str(document.statement_period_from)

            try:
                statement_period_to = document.statement_period_to.astimezone().isoformat()
            except:
                statement_period_to = str(document.statement_period_to)

            write_a_row(1, [
                document.account_name,
                document.account_number,
                document.branch_name,
                document.bank_code,
                statement_period_from,
                statement_period_to,
                get(results, "total", len(transactions))
            ])

            # Transactions
            write_a_row(1, ["TRANSACTIONS"])
            write_a_row(0, [
                "no",
                "transaction_id",
                "transaction_date",
                "transaction_type",
                "deposit",
                "withdrawal",
                "balance",
                "exclusion_reason",
                "chq",
                "particular",
                "channel",
                "category",
                "institution",
            ])

            for index, transaction in enumerate(transactions):
                transaction_date = get(transaction, "transaction_date")
                transaction_id = get(transaction, "transaction_id")

                try:
                    transaction_date = datetime.strptime(str(transaction_date), "%Y-%m-%d")
                    transaction_date_format = transaction_date.strftime("%Y-%m-%d")
                except ValueError:
                    pass
                
                write_a_row(0, [
                    index+1,
                    transaction_id,
                    transaction_date_format, # transaction_date
                    get(transaction, "transaction_type"),
                    get(transaction, "deposit"),
                    get(transaction, "withdrawal"),
                    get(transaction, "balance"),
                    get(transaction, "exclusion_reason"),
                    get(transaction, "chq"),
                    get(transaction, "particular"),
                    get(transaction, "channel"),
                    get(transaction, "category"),
                    get(transaction, "institution"),
                ])

                self.index_transaction_id_sheet_name.update({transaction_id:sheet_name})
            											
            # style    
            worksheet.set_column(0, 0, width=5, cell_format=text_wrap_format)
            worksheet.set_column(1, 15, width=22, cell_format=text_wrap_format)
            worksheet.set_column(8, 8, width=40, cell_format=text_wrap_format)

            worksheet.set_row(0, None, cell_title_format)
            worksheet.set_row(1, None, cell_sub_title_format)
            worksheet.set_row(3, None, cell_title_format)
            worksheet.set_row(4, None, cell_sub_title_format)

        return
