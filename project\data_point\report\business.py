from pydash import get

from data_point.models import DataPointResult

from ..provider.business_aml import BusinessAML
from ..provider.dbd_business import DBDBusiness
from ..provider.dbd_directorship import DBDDirectorship
from ..provider.dbd_shareholders import DBDShareholders
from ..provider.asia_verify_business import AsiaVerifyBusiness
from ..provider.ubo import UBO

from .base import DataPointReportBase


class BusinessReport(DataPointReportBase):
    """
    The report of
        - business_aml
        - dbd_business
        - dbd_directorship
        - dbd_shareholders
        - asia_verify_business
        - ubo
    """

    name = "business"
    data_points = [
        BusinessAML.name,
        DBDBusiness.name,
        DBDShareholders.name,
        DBDDirectorship.name,
        AsiaVerifyBusiness.name,
        UBO.name,
    ]

    def get_data_point_result(self, name, default=None, **kwargs):
        if default is None:
            default = []

        if not self.data_point_result:
            return default

        if not hasattr(self.data_point_result, name):
            return default

        return getattr(self.data_point_result, name, default)
    
    def set_dbd_creden_data(self, dbd_results, creden_results, empowerment_results: dict, data_points_result):
        for data_points_result_item in data_points_result:
            dbd_output_items  = get(data_points_result_item, "output.dbd", [])
            for dbd_output_item in dbd_output_items:
                input_jdid = get(dbd_output_item, "input.jdid", {})
                if input_jdid not in dbd_results:
                    output_raw_data = get(dbd_output_item, "output.raw_data", {})
                    output_raw_data = {
                        input_jdid: output_raw_data
                    }
                    dbd_results.update(output_raw_data)

            creden_output_items  = get(data_points_result_item, "output.creden", [])
            for creden_output_item in creden_output_items:
                input_id = get(creden_output_item, "input.id", {})
                if input_id not in creden_results:
                    output_raw_data = get(creden_output_item, "output", {})
                    output_raw_data = {
                        input_id: output_raw_data
                    }
                    creden_results.update(output_raw_data)
            
            empowerment_output_items = get(data_points_result_item, "output.empowerment", [])
            for empowerment_output_item in empowerment_output_items:
                input_id = get(empowerment_output_item, "input.jp_no", '')
                if input_id not in empowerment_results:
                    output_raw_data = get(empowerment_output_item, "output", {})
                    output_raw_data = {
                        input_id: output_raw_data
                    }
                    empowerment_results.update(output_raw_data)
    
    def set_dbd_vat_info_data(self, dbd_vat_info_result: dict, data_points_result):
        for data_points_result_item in data_points_result:
            dbd_vat_info_output_items = get(data_points_result_item, "output.dbd_vat_info", [])
            for dbd_vat_info_output_item in dbd_vat_info_output_items:
                input_id = get(dbd_vat_info_output_item, "input.tin", '')
                if input_id not in dbd_vat_info_result:
                    output_raw_data = get(dbd_vat_info_output_item, "output", {})
                    output_raw_data = {
                        input_id: output_raw_data
                    }
                    dbd_vat_info_result.update(output_raw_data)

    def set_business_aml_data(self, dbd_results, creden_results, empowerment_results):
        data_points_result = self.get_data_point_result(BusinessAML.name, [])
        self.set_dbd_creden_data(dbd_results, creden_results, empowerment_results, data_points_result)

    def set_dbd_business_data(self, dbd_results, creden_results, empowerment_results, dbd_vat_info_result):
        data_points_result = self.get_data_point_result(DBDBusiness.name, [])
        self.set_dbd_creden_data(dbd_results, creden_results, empowerment_results, data_points_result.copy())
        self.set_dbd_vat_info_data(dbd_vat_info_result, data_points_result.copy())
        
    def set_dbd_shareholders_data(self, dbd_results, creden_results, empowerment_results):
        data_points_result = self.get_data_point_result(DBDShareholders.name, [])
        self.set_dbd_creden_data(dbd_results, creden_results, empowerment_results, data_points_result)

    def set_dbd_directorship_data(self, dbd_results, creden_results, empowerment_results):
        data_points_result = self.get_data_point_result(DBDDirectorship.name, [])
        self.set_dbd_creden_data(dbd_results, creden_results, empowerment_results, data_points_result)

    def get_all_dbd_creden_data(self):
        # dbd_results from
        # - business_aml
        # - dbd_directorship
        # - dbd_business
        # - dbd_shareholders
        dbd_results = {}
        # creden_results from
        # - business_aml
        # - dbd_shareholders
        creden_results = {}
        
        # creden_results from
        # - dbd_directorship
        empowerment_result = {}
        
        # dbd_vat_info from
        #  - dbd_business
        dbd_vat_info_result = {}

        self.set_business_aml_data(dbd_results, creden_results, empowerment_result)
        self.set_dbd_business_data(dbd_results, creden_results, empowerment_result, dbd_vat_info_result)
        self.set_dbd_shareholders_data(dbd_results, creden_results, empowerment_result)
        self.set_dbd_directorship_data(dbd_results, creden_results, empowerment_result)
        return dbd_results, creden_results, empowerment_result, dbd_vat_info_result
    
    def get_report(self, **kwargs):
        """
        [{
            "input": {
                "jd_id": "123456789121", <-- all data source under business tab has jd id input
                "jd_name" : "MAPPA Co., Ltd." <-- if null please fill
            },
            "output": {
                "dbd": {},
                "creden": {},
                "empowerment": {},
                "ubo": {},
            }
        }]
        """
        report = []
        dbd_results, creden_results, empowerment_results, dbd_vat_info_results = self.get_all_dbd_creden_data()

        founded_jdid = []
        for jdid, dbd in reversed(list(dbd_results.items())):
            # already found then do next
            if jdid in founded_jdid:
                continue

            creden_result_item = {}
            empowerment_result_item = {}
            vat_info_result_item = {}
            if get(creden_results, jdid):
                creden_result_item = creden_results.pop(jdid)
            # empowerment result must be with DBD result only,
            if get(empowerment_results, jdid):
                empowerment_result_item = empowerment_results.pop(jdid)
            # vat info result must be with DBD result only,
            if get(dbd_vat_info_results, jdid):
                vat_info_result_item = dbd_vat_info_results.pop(jdid)
            
            jd_name = get(dbd, "jd_name")
            business_input = {
                "jd_id": jdid,
                "jd_name": jd_name,
            }
            report.append({
                "input": business_input,
                "output":{
                    "dbd": dbd,
                    "creden": creden_result_item,
                    "empowerment": empowerment_result_item,
                    "dbd_vat_info": {
                        'vat_registration': vat_info_result_item.get('vat_registration_status'),
                        'vat_number': vat_info_result_item.get('vat_number'),
                        'vat_branch': vat_info_result_item.get('vat_branch'),
                        'vat_name': vat_info_result_item.get('vat_name'),
                        'vat_address': vat_info_result_item.get('vat_address'),
                        'vat_zipcode': vat_info_result_item.get('vat_zipcode'),
                        'vat_date': vat_info_result_item.get('vat_date'),
                    }
                }
            })
            founded_jdid.append(jdid)
        
        founded_jdid = []
        for jdid, creden in reversed(list(creden_results.items())):
            # already found then do next
            if jdid in founded_jdid:
                continue
            business_input = {
                "jd_id": jdid,
                "jd_name": None,
            }
            report.append({
                "input": business_input,
                "output":{
                    "dbd": {},
                    "creden": creden,
                }
            })
            founded_jdid.append(jdid)

        # Add asia verify reports
        asia_verify_company_id_founded = []
        asia_verify_report_list = self.get_asia_verify_report()
        for asia_verify_report in reversed(asia_verify_report_list):
            company_id = asia_verify_report.get('input', {}).get('company_id')
            # already found then do next
            if company_id and company_id in asia_verify_company_id_founded:
                continue
            report.append(asia_verify_report)
            asia_verify_company_id_founded.append(company_id)

        # set UBO
        self.append_ubo_report(report)
        return report

    def get_asia_verify_report(self) -> list:
        '''
        Asia verify is seperate from dbd or creden.
        
        Example of Asia verify result
        [{"input": [{...}],
            "output": {
            "asia_verify": [
                {"input": {...},
                "output": { "code": 200,"result": {"data": {...}, ... },
                    ...
                }
            ]
            },
        },
            ...
        ]
        '''
        report_result = []
        
        # Get data point result from AsiaVerifyBusiness and Business Provider
        list_of_result: list = [
            self.get_data_point_result(AsiaVerifyBusiness.name, []),
            self.get_data_point_result(BusinessAML.name, []),
        ]
        company_id_added = []
        
        # Loop each list of providers but work around as only one input case.
        for asia_verify_result_list in list_of_result:
            if len(asia_verify_result_list) > 0 :
                # Get last for work-around that assume multiple company result
                asia_verify_output_list = []
                for asia_verify_result in asia_verify_result_list:
                    asia_verify_output_list += get(asia_verify_result, "output.asia_verify", [])    
                
                # Skip this list if no asia verify data
                if len(asia_verify_output_list) == 0 :
                    continue

                # Get last for work-around that assume only one result of asia verify 
                for asia_verify_output in asia_verify_output_list:
                    asia_verify_output_input_param = get(asia_verify_output, "input", {})
                    asia_verify_output_response = get(asia_verify_output, "output", {})
                
                    company_id = get(asia_verify_output_input_param, "input")
                    country = get(asia_verify_output_input_param, "country")
                    # Skip all number(company_id) that already add report information.
                    if company_id not in company_id_added :
                        v2_country_list = ["PHL", "AUS"]
                        if any(True for v2_country in v2_country_list if v2_country == country) :
                            asia_verify_output_response = get(asia_verify_output_response, "result", {})
                        else:
                            asia_verify_output_response = get(asia_verify_output_response, "result.data", {})
                        report = {
                            "input": {
                                "country_code": country,
                                "company_id": company_id,
                            },
                            "output":{
                                "dbd": {},
                                "creden": {},
                                "asia_verify": asia_verify_output_response,
                            }
                        }
                        report_result.append(report)
                        company_id_added.append(company_id)
        return report_result

    def append_ubo_report(self, report):
        # to append into output.dbd.jd_id
        data_points_result = self.get_data_point_result(UBO.name, [])
        if not data_points_result:
            return
        
        data_points_result = data_points_result.copy()

        founded_jdid = []
        # not found in dbd
        for ubo_result in reversed(data_points_result):
            for output_ubo in get(ubo_result, "output.ubo", []):
                jdid = get(output_ubo, "input.business_id")
                if jdid in founded_jdid:
                    continue
                jdname = get(output_ubo, "output.data.name", "")
                ubo_input = {
                    "jdid": jdid,
                    "explore_level": get(output_ubo, "input.explore_level"),
                    "shareholder_threshold": get(output_ubo, "input.shareholders_threshold"),
                }
                ubo_output = {
                    "ubo_node": {
                        "name": jdname,
                        "type": "juristic", #support only juristic
                        "children": get(output_ubo, "output.data.results", [])
                    },
                    "remaining_shareholders": get(output_ubo, "output.data.remaining_shareholders", []),
                }

                found_id_dbd = False
                for business_report in report:
                    dbd_jdid = get(business_report, "output.dbd.jd_id")
                    if dbd_jdid and dbd_jdid == jdid:
                        found_id_dbd = True
                        business_report["output"]["ubo"] = ubo_output
                        if isinstance(business_report["input"], dict):
                            business_report["input"].update(ubo_input)
                        elif isinstance(business_report["input"], list):
                            business_report["input"].append(ubo_input)
                
                if found_id_dbd:
                    continue

                # not found in dbd
                output_report = {
                    "dbd": {
                        "jd_id": jdid,
                        "jd_name": jdname,
                    },
                    "ubo": ubo_output
                }
                report.append({
                    "input": ubo_input,
                    "output": output_report,
                })
                founded_jdid.append(jdid)

        return