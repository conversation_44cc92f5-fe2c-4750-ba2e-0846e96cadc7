import json

from data_point.base import DynamicformBase
from django.apps import apps
from pydash import get
from rest_framework import status

DATAPOINT_OPTIONS = {
    "account_name": {
        "label": "Account name contains one of the bank statement",
        "type": "json"
    },
    "account_type": {
        "label": "Account type is in one of the bank statement",
        "type": "json"
    },
    "average_deposit_amount": {
        "label": "Average deposit amount",
        "type": "number"
    },
    "average_withdrawal_amount": {
        "label": "Average withdrawal amount",
        "type": "number"
    },
    "avg_balance_per_day": {
        "label": "Average daily account balance",
        "type": "number"
    },
    "avg_debt_to_income_last_3_month": {
        "label": "Average Loan Repayment divide by Deposit in the last 3 month",
        "type": "number"
    },
    "avg_debt_to_income_last_6_month": {
        "label": "Average Loan Repayment divide by Deposit in the last 6 month",
        "type": "number"
    },
    "avg_income_last_6_month": {
        "label": "Average income within last 6 months",
        "type": "number"
    },
    "avg_max_deposit_per_day": {
        "label": "Average maximum daily deposit amount",
        "type": "number"
    },
    "avg_max_withdrawal_per_day": {
        "label": "Average maximum daily withdrawal amount",
        "type": "number"
    },
    "avg_min_deposit_per_day": {
        "label": "Average minimum daily deposit amount",
        "type": "number"
    },
    "avg_min_withdrawal_per_day": {
        "label": "Average minimum daily withdrawal amount",
        "type": "number"
    },
    "avg_net_income_in_3_month": {
        "label": "Average Net Income in the last 3 month",
        "type": "number"
    },
    "avg_net_income_in_6_month": {
        "label": "Average Net Income in the last 6 month",
        "type": "number"
    },
    "avg_num_deposits_per_day": {
        "label": "Average number of daily deposit transactions",
        "type": "number"
    },
    "avg_num_withdrawals_per_day": {
        "label": "Average number of daily withdrawal transactions",
        "type": "number"
    },
    "avg_time_between_transactions": {
        "label": "Average time interval between transactions",
        "type": "number"
    },
    "avg_transactions_per_day": {
        "label": "Average number of transactions per day",
        "type": "number"
    },
    "bank_name": {
        "label": "Compare the bank name to the list",
        "type": "json"
    },
    "day_with_the_highest_deposit": {
        "label": "Day with the highest deposit amount",
        "type": "date"
    },
    "day_with_the_highest_withdrawal": {
        "label": "Day with the highest withdrawal amount",
        "type": "date"
    },
    "days_balance_below_thb1000_last_1_month": {
        "label": "Number of days balance below THB 1000 in the last 1 month",
        "type": "number"
    },
    "days_balance_below_thb1000_last_3_month": {
        "label": "Number of days balance below THB 1000 in the last 3 month",
        "type": "number"
    },
    "days_balance_below_thb1000_last_6_month": {
        "label": "Number of days balance below THB 1000 in the last 6 month",
        "type": "number"
    },
    "debt_to_income_last_1_month": {
        "label": "Loan Repayment divide by Deposit in the last 1 month",
        "type": "number"
    },
    "debt_to_income_last_3_month": {
        "label": "Loan Repayment divide by Deposit in the last 3 month",
        "type": "number"
    },
    "debt_to_income_last_6_month": {
        "label": "Loan Repayment divide by Deposit in the last 6 month",
        "type": "number"
    },
    "diff_30d_60d_rolling_deposit": {
        "label": "Deposit in the most recent 30 days of transactions - Deposit in the 31-60 days of transactions",
        "type": "number"
    },
    "diff_30d_60d_std_withdrawal": {
        "label": "Standard Deviation of withdrawal in the most recent 30 days of transactions - Standard Deviation of withdrawal in the most recent 30 days of transactions",
        "type": "number"
    },
    "end_date": {
        "label": "End transaction date",
        "type": "date"
    },
    "final_balance": {
        "label": "Final balance amount",
        "type": "number"
    },
    "has_large_tran_last_1_month": {
        "label": "Has transactions that are greater than 3 standard deviation of deposit in the last 1 month",
        "type": "boolean"
    },
    "has_large_tran_last_3_month": {
        "label": "Has transactions that are greater than 3 standard deviation of deposit in the last 3 month",
        "type": "boolean"
    },
    "has_large_tran_last_6_month": {
        "label": "Has transactions that are greater than 3 standard deviation of deposit in the last 6 month",
        "type": "boolean"
    },
    "has_negative_balance_last_1_month": {
        "label": "Has negative balance in the last 1 month",
        "type": "boolean"
    },
    "has_negative_balance_last_3_month": {
        "label": "Has negative balance in the last 3 month",
        "type": "boolean"
    },
    "has_negative_balance_last_6_month": {
        "label": "Has negative balance in the last 6 month",
        "type": "boolean"
    },
    "has_negative_net_income_last_1_month": {
        "label": "Has negative net income in the last 1 month",
        "type": "boolean"
    },
    "has_negative_net_income_last_3_month": {
        "label": "Has negative net income in the last 3 month",
        "type": "boolean"
    },
    "has_negative_net_income_last_6_month": {
        "label": "Has negative net income in the last 6 month",
        "type": "boolean"
    },
    "has_own_deposit_last_1_month": {
        "label": "Has own deposits in the last 1 month",
        "type": "boolean"
    },
    "has_own_deposit_last_3_month": {
        "label": "Has own deposits in the last 3 month",
        "type": "boolean"
    },
    "has_own_deposit_last_6_month": {
        "label": "Has own deposits in the last 6 month",
        "type": "boolean"
    },
    "has_salary_transaction_last_1_month": {
        "label": "Has Salary transactions in the last 1 month",
        "type": "boolean"
    },
    "has_salary_transaction_last_3_month": {
        "label": "Has Salary transactions in the last 3 month",
        "type": "boolean"
    },
    "has_salary_transaction_last_6_month": {
        "label": "Has Salary transactions in the last 6 month",
        "type": "boolean"
    },
    "inactive_periods_days_last_1_month": {
        "label": "Number of days bank statement is inactive in the last 1 month",
        "type": "number"
    },
    "initial_balance": {
        "label": "Initial balance amount",
        "type": "number"
    },
    "large_tran_count_last_1_month": {
        "label": "Number of deposit > 3 sd deposit in the last 1 month",
        "type": "number"
    },
    "large_tran_count_last_3_month": {
        "label": "Number of deposit > 3 sd deposit in the last 3 month",
        "type": "number"
    },
    "large_tran_count_last_6_month": {
        "label": "Number of deposit > 3 sd deposit in the last 6 month",
        "type": "number"
    },
    "loan_institution_count": {
        "label": "Number of loan instituition the user has transaction with",
        "type": "number"
    },
    "loan_institution_count_last_1_month": {
        "label": "Number of loan institution in Loan Repayment found in the last 1 month",
        "type": "number"
    },
    "loan_institution_count_last_3_month": {
        "label": "Number of loan institution in Loan Repayment found in the last 3 month",
        "type": "number"
    },
    "loan_institution_count_last_6_month": {
        "label": "Number of loan institution in Loan Repayment found in the last 6 month",
        "type": "number"
    },
    "longest_time_between_transaction": {
        "label": "Longest time interval between transactions",
        "type": "number"
    },
    "max_balance": {
        "label": "Maximum balance amount",
        "type": "number"
    },
    "max_deposit": {
        "label": "Maximum deposit amount",
        "type": "number"
    },
    "max_transaction_date": {
        "label": "Transaction date in statement begins",
        "type": "date"
    },
    "max_withdrawal": {
        "label": "Maximum withdrawal amount",
        "type": "number"
    },
    "median_deposit_amount": {
        "label": "Median deposit amount",
        "type": "number"
    },
    "median_withdrawal_amount": {
        "label": "Median withdrawal amount",
        "type": "number"
    },
    "min_balance": {
        "label": "Minimum balance amount",
        "type": "number"
    },
    "min_deposit": {
        "label": "Minimum deposit amount",
        "type": "number"
    },
    "min_transaction_date": {
        "label": "Transaction date in statement ends",
        "type": "date"
    },
    "min_withdrawal": {
        "label": "Minimum withdrawal amount",
        "type": "number"
    },
    "net_income_in_1_month": {
        "label": "Net Income in the last 1 month",
        "type": "number"
    },
    "number_of_days_with_deposit": {
        "label": "frequency (days) of deposits",
        "type": "number"
    },
    "number_of_days_with_withdrawal": {
        "label": "Frequency (days) of withdrawals",
        "type": "number"
    },
    "number_of_deposits": {
        "label": "Number of deposit transactions",
        "type": "number"
    },
    "number_of_transactions": {
        "label": "Number of transactions",
        "type": "number"
    },
    "number_of_unique_channel": {
        "label": "Number of unique transaction channels",
        "type": "number"
    },
    "number_of_unique_deposit_channel": {
        "label": "Number of unique deposit transaction channels",
        "type": "number"
    },
    "number_of_unique_withdrawal_channel": {
        "label": "Number of unique withdrawal transaction channels",
        "type": "number"
    },
    "number_of_withdrawal": {
        "label": "Number of withdrawal transactions",
        "type": "number"
    },
    "own_deposit_count": {
        "label": "Numbers of owners deposits",
        "type": "number"
    },
    "passing_deposit_count_last_1_month": {
        "label": "Number of passing deposit (transfer in and out in the same amount, same day) in the last 1 month",
        "type": "number"
    },
    "passing_deposit_count_last_3_month": {
        "label": "Number of passing deposit (transfer in and out in the same amount, same day) in the last 3 month",
        "type": "number"
    },
    "passing_deposit_count_last_6_month": {
        "label": "Number of passing deposit (transfer in and out in the same amount, same day) in the last 6 month",
        "type": "number"
    },
    "ratio_30d_60d_rolling_deposit": {
        "label": "Ratio of the most recent 30 days of transactions / Deposit in the 31-60 days of transactions",
        "type": "number"
    },
    "shortest_time_between_transaction": {
        "label": "Shortest time interval between transactions",
        "type": "number"
    },
    "start_date": {
        "label": "Start transaction date",
        "type": "date"
    },
    "sum_deposit_last_1_month": {
        "label": "Sum of deposit in the last 1 month",
        "type": "number"
    },
    "sum_deposit_last_3_month": {
        "label": "Sum of deposit in the last 3 month",
        "type": "number"
    },
    "sum_deposit_last_6_month": {
        "label": "Sum of deposit in the last 6 month",
        "type": "number"
    },
    "sum_deposit_net_passing_last_1_month": {
        "label": "Sum of deposit - passing transactions in the last 1 month",
        "type": "number"
    },
    "sum_deposit_net_passing_last_3_month": {
        "label": "Sum of deposit - passing transactions in the last 3 month",
        "type": "number"
    },
    "sum_deposit_net_passing_last_6_month": {
        "label": "Sum of deposit - passing transactions in the last 6 month",
        "type": "number"
    },
    "sum_loan_repayment_last_1_month": {
        "label": "Sum of loan repayment in the last 1 month",
        "type": "number"
    },
    "sum_loan_repayment_last_3_month": {
        "label": "Sum of loan repayment in the last 3 month",
        "type": "number"
    },
    "sum_loan_repayment_last_6_month": {
        "label": "Sum of loan repayment in the last 6 month",
        "type": "number"
    },
    "sum_withdrawal_last_1_month": {
        "label": "Sum of withdrawal in the last 1 month",
        "type": "number"
    },
    "sum_withdrawal_last_3_month": {
        "label": "Sum of withdrawal in the last 3 month",
        "type": "number"
    },
    "sum_withdrawal_last_6_month": {
        "label": "Sum of withdrawal in the last 6 month",
        "type": "number"
    },
    "total_amount_of_deposits": {
        "label": "Total amount of deposits",
        "type": "number"
    },
    "total_amount_of_withdrawals": {
        "label": "Total amount of withdrawals",
        "type": "number"
    },
    "zero_negative_balance_days_last_1_month": {
        "label": "Number of days with zero or negative balance in the last 1 month",
        "type": "number"
    }
}


class BankStatement(DynamicformBase):
    name = "bank_statement"
    title = "Bank Statement"
    sub_title = ""
    description = "Uppass's cutting-edge bank statement extraction technology can help effortlessly extract and integrate bank statements, streamlining financial processes and eliminating manual data entry"
    icon = ""
    option_icon_name = "tabler:building-bank"
    option_icon = "https://cdn.uppass.io/projects/uppass/img/datapoint/uppass_verification_services_36/Bankstatement_Verification_36.svg"
    data_point_opitons = DATAPOINT_OPTIONS
    required_item_types = [['BankStatement.Uploader']]
    required_item_types_auto_connect = True

    def get_data_result(self, values_list=[], **kwargs):
        # fmt: off
        result = {}
        bank_statement_result = self.get_bank_statement_result()
        statement_summary = get(bank_statement_result, 'statement_summary', {})
        statement_summary_overall = get(statement_summary, 'overall', {})
        statement_summary_features = get(bank_statement_result, 'statement_summary_features', {})
        
        # Setup map key between datapoint and bank_statement_result
        mapping_key_bank_statement_result: dict = {
            "bank_name": "_bank_name",
            "account_name": "_account_name",
            "account_type": "_account_type",
            "transaction_period_beginning_date": "_transaction_period_beginning_date",
            "transaction_period_end_date": "_transaction_period_end_date",
        }
        
        for key in DATAPOINT_OPTIONS.keys():
            # Add result from bank_statement_result(form answer)
            if key in mapping_key_bank_statement_result.keys():
                result[key] = get(bank_statement_result, mapping_key_bank_statement_result[key])
            # Add result from statement_summary_overall(bank statement summary)
            else:
                result[key] = get(statement_summary_overall, key)
        # fmt: on

        return result

    def get_bank_statement_result(self):
        BankStatement = apps.get_model("bankstatement", "BankStatement")  # NOSONAR
        bank_statement = BankStatement.objects.filter(
            ref=self.applied_form.slug
        ).first()
        bank_statement_document_set = bank_statement.documents.all()
        bank_name = []
        account_name = []
        account_type = []
        transaction_period_beginning_date = []
        transaction_period_end_date = []

        for i in bank_statement_document_set:
            bank_name.append(i.bank_code)
            account_name.append(i.account_name)
            account_type.append(i.account_type)
            transaction_period_beginning_date.append(i.statement_period_from)
            transaction_period_end_date.append(i.statement_period_to)

        if transaction_period_beginning_date:
            transaction_period_beginning_date = min(transaction_period_beginning_date)
            transaction_period_beginning_date = transaction_period_beginning_date.strftime('%Y-%m-%d')  # fmt: skip
        else:
            transaction_period_beginning_date = None

        if transaction_period_end_date:
            transaction_period_end_date = max(transaction_period_end_date)
            transaction_period_end_date = transaction_period_end_date.strftime('%Y-%m-%d')  # fmt: skip
        else:
            transaction_period_end_date = None

        if not bank_statement:
            message = f"Bank Statement record not found"
            self.log_info(
                "Bank Statement Result",
                {
                    "result": message,
                },
            )
            raise Exception(f"Bank Statement record not found")  # NOSONAR

        result, status_code = bank_statement.get_bank_statement_summary()

        self.log_info(
            "Bank Statement Result",
            {
                "result": result,
                "status_code": status_code,
                "bank_statement_batch_id": bank_statement.batch_id,
            },
        )

        self.set_current_data_point_output(result)

        # fmt: off
        result['_bank_name'] = json.dumps(list(set(bank_name)), ensure_ascii=False)
        result['_account_name'] = json.dumps(list(set(account_name)), ensure_ascii=False)
        result['_account_type'] = json.dumps(list(set(account_type)), ensure_ascii=False)
        result['_transaction_period_beginning_date'] = transaction_period_beginning_date
        result['_transaction_period_end_date'] = transaction_period_end_date
        # fmt: on

        if not status.is_success(status_code):
            result = {}

        return result

    def set_current_data_point_output(self, bank_statement={}, **kwargs):
        bank_statement.pop("validation_note", None)
        bank_statement.pop("validation_transaction_min_month", None)
        bank_statement.pop("validation_transaction_latest_after", None)
        bank_statement.pop("validation_transaction_oldest_before", None)
        super().set_current_data_point_output(bank_statement)
